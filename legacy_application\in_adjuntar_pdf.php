<?php
//error_reporting(E_ALL ^ E_DEPRECATED);

	session_start();
	//ini_set("display_errors",1);
	require_once("lib_copia_escritura.php");	
	require_once("calls/oficina/preparar_compulsa.php");	
	require_once('calls/soap/nusoap.php');
//	include "calls/cardex/php-barcode.php";
	require_once "calls/cardex/semacode.php";	


	define('TINYAJAX_PATH', 'calls');
	require_once(TINYAJAX_PATH . "/TinyAjax.php"); 	


	
	function subir_archivo($id_max, $caratula, $libro, $foja, $numero, $anho, $numero_folio_real,
						   $tipo, $dir, $cuerpo_cert, $email, $firma, $nombre_sociedad, $capital,
						   $dia_e, $mes_e, $anho_e, $tipo_soc, $repertorio, 
						   $compareciente_1, $compareciente_2 )
		{
			
		$mail_destinatario=$email;
		$libro="";
		$text=$cuerpo_cert;
		$tabla_cert="certificados_firmas";

		$source=$dir;

			
//		$dir="";
//		$codigo=$id_max.".pdf";		
//		$source=$dir.$codigo;

			
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$extensiones = explode(".",$source) ;
		$nombre1  = $extensiones[0] ;
		$nombre2  = $extensiones[1] ;
		if (is_file($source))
			{
			$rad2= md5(uniqid(rand()));
			$codigo_descarga=$rad2[1].$rad2[13].$rad2[14].$rad2[3].$rad2[9].$rad2[2].$rad2[10].$rad2[15].$rad2[8].$rad2[3].$rad2[3].$rad2[2].$rad2[18].$rad2[4];
			$card= new cardex;
			$cod=$card->devolver_codigo_not($caratula);
			
			$source2="".$id_max."_2.pdf";			
			
			$ddtt = new datos;
			
			//			$capital=1000000;
			//			$nombre_sociedad="PRESTACIONES KINOSIOLOGICAS Y DE ENFERMERIA LIMITADA";
			$dis = new diseno;
			$capital=str_replace(".","",$capital);
			$capital=$dis->protege($capital);
			$nombre_sociedad=$dis->protege($nombre_sociedad);
			$tipo_soc=$dis->protege($tipo_soc);
			$repertorio=$dis->protege($repertorio);
			$fecha_e=$anho_e."-".$mes_e."-".$dia_e;
			
			// campo4_nombre= "anho repertorio"				
			// campo4_valor = "numero_firma_vinculado"				

				
			$result4="INSERT INTO  certificados_metadata   (id_certificado, capital,  tipo, nombre_soc, fecha_e, texto, repertorio_notaria, compareciente_1 , compareciente_2, campo4_nombre  )
			VALUES ('$id_max', '$capital', '$tipo', '$nombre_sociedad','$fecha_e', '$cuerpo_cert', '$repertorio', '$compareciente_1', '$compareciente_2', '$anho'  )";	 
			$fin089 = mysqli_query($link, $result4);
			
/*
			$pdf2 = Zend_Pdf::load($source);
			$pdf2->properties['Title'] = $tipo;
			$pdf2->properties['Author'] = $ddtt->nombre();
			$dir_descarga = "http://fojas.cl/d.php?cod=".$ddtt->web_fojas()."&ndoc=".$id_max."";                
            $pdf2->properties['Keywords'] = "<numero_documento>".$id_max."</numero_documento><cod_notario>".$ddtt->web_fojas()."</cod_notario><dir_descarga>".$dir_descarga."</dir_descarga><nombre_notaria>".$ddtt->nombre()."</nombre_notaria>";
			$pdf2->save($source2);			
*/			$source2=$source;
			
			$data = addslashes(fread(fopen($source2, "r"), filesize($source2)));
			unlink($source);	
//			unlink($source2);			

			$fecha=date("Y-m-d");
			$hora=date("H:i:s");
			//REVISO SI NO SE ESTA HACIENDO EL MISMO CERTIFICADO POR CARATULA DEL MISMO DIA
			$tabla_cert="certificados_firmas";
			$rd="DELETE FROM  ".$tabla_cert."  WHERE numero_firma='$id_max' ";
			$fd = mysqli_query($link, $rd);
			$result="INSERT INTO  ".$tabla_cert." (numero_firma, data, tipo, libro, folio_real, anho, foja, numero, caratula, fecha, hora, usuario, texto, listo, mail_destinatario, codigo_descarga ) VALUES
				('$id_max', '$data', '$tipo', '$libro', '$folio_real', '$anho', '$foja', '$numero', '$caratula',  '$fecha', '$hora', '".$_SESSION['usuario']."' , '$text',  'nop', '$mail_destinatario', '$codigo_descarga'  )";	 
			
		if($fd = mysqli_query($link, $result))
			{
			$a="";
			return $a;
			}
			else
			{
			$a="<br>Hubo un problema al ingresar el regitro, Verifique si el tamanho del archivo no sobrepasa 900 Kb";
			return $a;
			}
		}
		else
			{
			echo"<br>sin source $source";
			}
	}
	
	
	function cambio_tipo ($tipo)
	{
		if($tipo<>"")
		{
			$tab = new TinyAjaxBehavior();
			$conect= new ingreso;
			$link=$conect->Conectarse();
			if ( $tipo=="")
			{
				$tt="TTTTTTTTTTTTTT";
				$tab->add( TabSetValue::getBehavior("cuerpo_cert", $tt));
				$tab->add( TabSetValue::getBehavior("titulo", $tt));
			}
			else
			{
				$r="SELECT * FROM certificados_notaria_formatos WHERE nombre='".$tipo."' ";
				$fi=mysqli_query($link, $r);
				$rw = mysqli_fetch_array($fi);
				$tab->add( TabSetValue::getBehavior("cuerpo_cert", $rw["texto"]));			
				$tab->add( TabSetValue::getBehavior("titulo", $rw["nombre"]));			
			}
			return $tab->getString();
		}
	}
	
	function carga_caratula($caratula)
	{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "SELECT * FROM cardex WHERE numero='$caratula'  ";
		$fin2 = mysqli_query($link, $r2);
		$ro2 = mysqli_fetch_array($fin2);
		$tab = new TinyAjaxBehavior();
		if($ro2["id"]<>"")
		{$tab->add( TabSetValue::getBehavior("email", $ro2["email"]));}
		return $tab->getString();				
		
	}
	
	function datos_repertorio($numero, $anho)
		{
		$conect= new ingreso;
		$link=$conect->Conectarse();		
		$result92 = "SELECT * FROM repertorio_escrituras WHERE  numero = '$numero' AND YEAR(fecha)='$anho' ";
		$fin9 = mysqli_query($link, $result92);
		$ro2 = mysqli_fetch_array($fin9);
		$tab = new TinyAjaxBehavior();
		if($ro2["id"]<>"")
			{
			$tab->add( TabSetValue::getBehavior("tipo", $ro2["tipo"]));
//			$tab->add( TabSetValue::getBehavior("compareciente_1", $ro2["nombre"]));
//			$tab->add( TabSetValue::getBehavior("compareciente_2", $ro2["vendedor"]));
			}	
			
			
		return $tab->getString();				
		}
	
	function guardar_form($tipo,$caratula,$email,$libro,$firma,$cuerpo_cert,$numero,$repertorio,$anho,$dia_e,$mes_e,$anho_e,$target_path, $sin_portada)
	{

	$tab = new TinyAjaxBehavior();
	
		$dis= new diseno;
		$tipo = strtoupper($dis->protege($tipo));
		$caratula = $dis->protege($caratula);
		$firma = $dis->protege($firma);
		$email = $dis->protege($email);
		$cuerpo_cert = $dis->protege($cuerpo_cert);
		$numero = $dis->protege($numero);
		$repertorio = $dis->protege($repertorio);
		$anho = $dis->protege($anho);

		
		$dia_e = $dis->protege($dia_e);
		$mes_e = $dis->protege($mes_e );
		$anho_e = $dis->protege($anho_e);
		
		if($dia_e==""){$dia_e=date("d");}
		if($mes_e==""){$mes_e=date("m");}
		if($anho_e==""){$anho_e=date("Y");}		

		
		
		
		
		if($tipo=="")
		{
			if($tipo==""){$msg="<div class='text_negative'>Debe ingresar el tipo de documento.</div>";}
		} else if ($target_path == "")
		{
			$msg="<div class='text_negative'>Debe adjuntar documento.</div>";
		}
		else
		{
			
			$dis= new diseno;
			$cert= new certificado;

			////////////////////////////////////////////////////////////
			//SE EL PDF DE LA ESCRITURA
			////////////////////////////////////////////////////////////

				
				if($tipo<>"")
					{$titulo=trim("COPIA ".$tipo);}
				else
					{$titulo=trim("Copia Escritura");}				

					$dis= new diseno;
					$cert= new certificado;
					srand((double)microtime()*10000000);
					$rad= md5(uniqid(rand()));

					$ddd0="tmp/UP_".$rad.".pdf";				
					$src = fopen($target_path, "r");
					$dest1 = fopen( $ddd0, 'w');
					stream_copy_to_stream($src, $dest1);
					fclose($src);
					fclose($dest1);
					unlink($target_path);

					$id_max=generar_compulsa_pdf(  $tipo, $caratula, $email, $cuerpo_cert,  $numero, $anho,  $dia_e, $mes_e, $anho_e, $compareciente_1, $compareciente_2, "copia_escritura", $id_max );
					$archivo_uno="tmp/".$id_max.".pdf";	
					$archivo_dos=$ddd0;
					if($sin_portada=="yep")
						{devolver_archivo("", $archivo_dos, $id_max , $archivo_posterior);}
					else
						{
 						$conect= new ingreso;
						$link=$conect->Conectarse();
						$result="CREATE TABLE IF NOT EXISTS `zzz_certificados_sin_inicio` (  `id` bigint(20) NOT NULL AUTO_INCREMENT,  `numero_firma` bigint(20) NOT NULL,  PRIMARY KEY (`id`),  KEY `numero_firma` (`numero_firma`) ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";	 
						$fin3 = mysqli_query($link, $result);
						$result="INSERT INTO zzz_certificados_sin_inicio (numero_firma) VALUES ('$id_max' )";	 
						$fin3 = mysqli_query($link, $result);
						devolver_archivo($archivo_uno, $archivo_dos, $id_max , $archivo_posterior);
						}

					
					$archivo_posterior = "tmp/EPF_".$id_max.".pdf";


					$repertorio=$numero;



					$aa=subir_archivo ($id_max, $caratula, $libro, $foja, $numero, $anho, $numero_folio_real,
								  $titulo, $archivo_posterior, $cuerpo_cert, $email, $firma, $nombre_sociedad, $capital,
								  $dia_e, $mes_e, $anho_e, $tipo_soc, $repertorio, 
								  $compareciente_1, $compareciente_2 );				
							
				
				
				
			$msg="<table align='center' border='0'>
			<br>
						<td class='headtabla_blanco' style='Color:#536976 !important;'  >Ver Documento $tipo </td>
						<td class='headtabla_blanco' ><a href='calls/ver_pdf.php?iadsfd=".$id_max."&pdf=PDF' ><img  width='40' src='imagenes/pdf.jpg'><a></td>
						</table>";
	
			
			
	
			$titulo = "";
			$tipo = "";
			$caratula = "";
			$pdf = "";
			$email = "";
			$cuerpo_cert = "";
			$tipo = "";
			$compareciente_1 = "";
			$compareciente_2 = "";
			
			$repertorio = "";
			$foja = "";
			$numero = "";
			$anho = "";
		}
	
	$tab->add( TabInnerHtml::getBehavior("msg", $msg));
		return $tab->getString();
	
	}
	
	
	$ajax = new TinyAjax(); 
	$ajax->showLoading();
	$ajax->exportFunction("carga_caratula",   array("caratula"));
	$ajax->exportFunction("datos_repertorio",   array("numero","anho"));
	$ajax->exportFunction("guardar_form", array("tipo","caratula","email","libro","firma","cuerpo_cert","numero","repertorio","anho","dia_e","mes_e","anho_e","target_path","sin_portada"));
	$ajax->process();
	echo"<html>
	<head>";
	echo"<meta http-equiv='content-type' content='text/html; charset=ISO-8859-1'>";
	$ajax->drawJavaScript(); 
	echo"
	<title>Sign</title>";
	$labelB=("col-sm-3 control-label labelAjustado");
	$row=("class='row'");
	echo "</head>
	<script language='javascript' type='text/javascript' src='calls/libreria_java.js'></script>
	<script src='dropzone/js/jquery_1.11.js'></script>  
	<link href='library/dropzone/dropzone.min.css' type='text/css' rel='stylesheet' />
	<script src='library/dropzone/dropzone.min.js'></script>
	<link href='//netdna.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css' rel='stylesheet' id='bootstrap-css'>
	<script src='//netdna.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js'></script>
	<script src='//code.jquery.com/jquery-1.11.1.min.js'></script>
	<link href='https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css' rel='stylesheet' integrity='sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN' crossorigin='anonymous'>
	<link rel='stylesheet' href='https://use.fontawesome.com/releases/v5.7.1/css/all.css' integrity='sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr' crossorigin='anonymous'>
	<script src='//netdna.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js'></script>";
	$conect= new ingreso;
	$link=$conect->Conectarse();
	$linkfs=$conect->Coneccion_fs();
	$dis = new diseno;
	$dd = new datos;
	$dis->ingresar_body("doc_elect");

	$msg_ini = "Adjuntar el Documento PDF";
	$mgs_derecha1 = "Adjuntar la Escritura en Formato PDF (Ver 1.4 - 1.5).";
	$link_cuadro="<a href='in_archivo_certificado_not.php?mandato=' class='btn btn-success btn-block'  style='color:white !important;' >DOCUMENTO POR TEXTO</a>";
//	$link_cuadro2="<a href='calls/oficina/subida_auto.php' class='headtabla_blanco' >COPIAS CBR</a>";	
	$link_cuadro2="<a href='in_archivo_certificado_not_cbr.php' class='btn btn-success btn-block'  style='color:white !important;'>COPIAS CBR</a>";	

	ECHO"<div class='col-sm-2'></div>

	<div class='col-sm-7'>
	<h2 align='center'>".$msg_ini."</h2>";  


	$conect= new ingreso;
	$link=$conect->Conectarse();
	$usur = new usuario;	
//if($usur->verificar_permiso($_SESSION['usuario'],"20"))
if(1==1)
	{	
	
	$dis = new diseno();
	//echo"<form name='form3765' method='post' action='in_adjuntar_pdf.php' enctype='multipart/form-data'  >
	echo"<input type='hidden' name='sig' id='sig' value='74yn8amjma794h'>";
	$sig=$_REQUEST['sig'];
	$libro = strtoupper($dis->protege($_REQUEST['libro']));
	$numero_folio_real = $dis->protege($_REQUEST['numero_folio_real']);
	$msg = "";
$target_path = "";
$ddd0 = "";
$tipo_arch = "";
if (!empty($_FILES)){
     
    $name = $_FILES['userfile_tmp']['name'];             
	$target_path = "" . basename( $_FILES['userfile_tmp']['name']);  
 	$tipo_arch = $_FILES['userfile_tmp']['type']; 
    move_uploaded_file($_FILES['userfile_tmp']['tmp_name'], $target_path);

}
	
	
	if($email==""){$email="-";}
	$link=$conect->Conectarse();
	
	if($anho==""){$anho=date("Y");}
	

	echo"
	
<br>	
	<input type='hidden' select name='libro' id='libro' value='Escritura Publica' >	
	<input type='hidden' id='firma' name='firma' value='".$_REQUEST['firma']."'>
	<input type='hidden' id='cuerpo_cert' name='cuerpo_cert' value='".$_REQUEST['cuerpo_cert']."'>
	<input type='hidden' id='numero' name='numero' value='".$_REQUEST['numero']."'>
	<input type='hidden' id='repertorio' name='repertorio' value='".$_REQUEST['numero']."'>
	<input type='hidden' id='anho' name='anho' value='".$_REQUEST['anho']."'>";

	$dia_e = $dis->protege($_REQUEST['dia_e']);
	if($dia_e == "") {$dia_e = date("d");}
	$mes_e = $dis->protege($_REQUEST['mes_e']);
	if($mes_e == "") {$mes_e = date("m");}
	$anho_e = $dis->protege($_REQUEST['anho_e']);
	if($anho_e == "") {$anho_e = date("Y");}
/*
	<input type='hidden' id='dia_e' name='dia_e' value='".$_REQUEST['dia_e']."'>
	<input type='hidden' id='mes_e' name='mes_e' value='".$_REQUEST['mes_e']."'>
	<input type='hidden' id='anho_e' name='anho_e' value='".$_REQUEST['anho_e']."'>	
*/
echo"
	<input type='hidden' name='target_path' id='target_path' value='' >
	
	<div $row>
		<label class='$labelB'>Tipo Documento:</label>
		<input class='col-sm-3 form-control fecha2' type='text' name='tipo' id='tipo' size = '70' value='$tipo' >
	</div>
	<div $row>
		<label class='$labelB'>Car&aacute;tula/OT:</label>
		<input type='text' class='col-sm-3 form-control fechaA' name='caratula'   onChange='carga_caratula();'   id='caratula' size='12'>
		<label class='control-label col-sm-1 labelAjustado'>Email:</label>
		<input type='text' class='col-sm-3 form-control textoS' name='email'  id='email' size='28' value='$email'></tr>
		<input type='hidden' id='pdf' name='pdf'  value='PDF' selected >
	</div>
	<div $row>
		<label class='$labelB'>Fecha Emision:</label>
			<input type='text' name='dia_e' class='col-sm-2 form-control fecha'  id='dia_e' size='2' maxlength='2' value='$dia_e' > 
			<input type='text' name='mes_e' class='col-sm-2 form-control fecha' id='mes_e' size='2' maxlength='2' value='$mes_e' > 
			<input type='text' name='anho_e' class='col-sm-2 form-control fecha'  id='anho_e' size='4' maxlength='4' value='$anho_e' >
	</div>
	<div $row>	
		<label class='$labelB'>No adjuntar portada</label>
		<input type='checkbox' class='labelAjustado' name='sin_portada'  id='sin_portada' value='yep'>
	</div>

	<div $row> 
		<label class='$labelB'>Adjuntar PDF</label>
		<form action='in_adjuntar_pdf.php' class='dropzone'></form></td>
	</div>

	<br><div align='center'><input type='button' class='form-control btn btn-info textoS' name='button' value='Ingresar' onclick='guardar_form();'></div>
</div>



<div class='col-md-3'>
	<div id='cuadro_texto' style='background:none; margin:50px 0 0 0;'>";
echo"	$link_cuadro
<br>";
echo"	$link_cuadro2
<br>";
echo"	<a href='firma_digital.php' class='btn btn-success btn-block'  style='color:white !important;' >DOCUMENTOS EMITIDOS POR FIRMAR</a>
		
	<br>
		<a href='in_adjuntar_pdf_cadena.php' class='btn btn-success btn-block'  style='color:white !important;' >ADJUNTAR CADENA FEA</a>
<br>
		<a href='index.php' class='btn btn-success btn-block'  style='color:white !important;'>VOLVER</a>
		<br>
		<div class='textAreaDerecha'>".$mgs_derecha1."
		<div id='msg'></div>
		<div><br></div>
		
		</div>


	</div>
</td>
</tr>
</table>
</div>
<style>
.fecha{
	max-width:17% !important;
}
.fechaA{
	max-width:18% !important;
}

.fechaAn{
	max-width:20% !important;
	margin-right:0%;
}

.fecha2{
	max-width:58% !important;
}

.textoS{
	max-width:31.5% !important;
}
.textoPatente{
	max-width:25% !important;
}
.textoPatente2{
	max-width:45% !important;
}
.label_tm{
	margin-left:3rem;
	margin-top:1.6%;
}
.labelAjustado{
	margin-top:1.2%;
}
.botones{
	margin-left: 3%;
	max-width:24% !important;
	border-radius:30px;
}
.botonesA{
	margin-left: 8%;
	max-width:24% !important;
	border-radius:30px;
}
.textoNormal{
	max-width: 51% !important;
}
.botonesAA{
	max-width: 40% !important;
}

.textAreaDerecha{
	border-radius:10px;
    overflow:hidden;
	border:1px solid #536976;
	height: 100%;
	color:7D8F9F;
}
</style>
</body></html>";





	$cons= new diseno;
	$cons->ingresar_fin();
	}
else
	{
	$cons= new diseno;
	$cons->negacion_permiso();
	}
	
			
	
	
	?>