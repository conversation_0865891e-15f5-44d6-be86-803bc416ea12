"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { MainLayout, PageHeader, PageContent } from "@/components/layout/main-layout"
import { usePermissions } from "@/hooks/use-permissions"
import { getAppName, getCompanyName } from "@/lib/config"
import {
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  UsersIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  BookOpenIcon,
} from "@heroicons/react/24/outline"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { hasPermission, canAccessOT, canAccessDocuments, canAccessUsers, canAccessReports, canAccessCaja, canAccessRepertorio } = usePermissions()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  // Módulos disponibles basados en permisos
  const modules = [
    {
      name: "Órdenes de Trabajo",
      description: "Crear y gestionar órdenes de trabajo",
      icon: ClipboardDocumentListIcon,
      href: "/app/ot",
      color: "bg-blue-500",
      visible: canAccessOT(),
      stats: { total: 45, pending: 12 }
    },
    {
      name: "Documentos Privados",
      description: "Gestión de documentos privados",
      icon: DocumentTextIcon,
      href: "/app/documentos/privados",
      color: "bg-green-500",
      visible: canAccessDocuments(),
      stats: { total: 128, pending: 8 }
    },
    {
      name: "Escrituras Públicas",
      description: "Gestión de escrituras públicas",
      icon: BookOpenIcon,
      href: "/app/documentos/publicas",
      color: "bg-purple-500",
      visible: hasPermission("escritura_publica_access"),
      stats: { total: 67, pending: 3 }
    },
    {
      name: "Repertorio",
      description: "Consulta y gestión del repertorio",
      icon: BookOpenIcon,
      href: "/app/repertorio",
      color: "bg-indigo-500",
      visible: canAccessRepertorio(),
      stats: { total: 234, pending: 0 }
    },
    {
      name: "Caja",
      description: "Gestión financiera y pagos",
      icon: CurrencyDollarIcon,
      href: "/app/caja",
      color: "bg-yellow-500",
      visible: canAccessCaja(),
      stats: { total: "$2.450.000", pending: 5 }
    },
    {
      name: "Reportes",
      description: "Informes y estadísticas",
      icon: ChartBarIcon,
      href: "/app/reportes",
      color: "bg-red-500",
      visible: canAccessReports(),
      stats: { total: 15, pending: 0 }
    },
    {
      name: "Usuarios",
      description: "Gestión de usuarios y permisos",
      icon: UsersIcon,
      href: "/app/usuarios",
      color: "bg-gray-500",
      visible: canAccessUsers(),
      stats: { total: 12, pending: 2 }
    }
  ].filter(module => module.visible)

  return (
    <MainLayout>
      <PageHeader
        title={`Bienvenido, ${session.user.nombreSocial || session.user.username}`}
        description={`Panel de control - ${getCompanyName()}`}
      />

      <PageContent>
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-soft p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClipboardDocumentListIcon className="h-8 w-8 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">OT Activas</p>
                <p className="text-2xl font-bold text-gray-900">12</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-soft p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Documentos Hoy</p>
                <p className="text-2xl font-bold text-gray-900">8</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-soft p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Ingresos Mes</p>
                <p className="text-2xl font-bold text-gray-900">$2.4M</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-soft p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UsersIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Usuarios Activos</p>
                <p className="text-2xl font-bold text-gray-900">12</p>
              </div>
            </div>
          </div>
        </div>

        {/* Modules Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {modules.map((module) => (
            <div
              key={module.name}
              className="bg-white rounded-lg shadow-soft border border-gray-200 hover:shadow-soft-md transition-shadow duration-200 cursor-pointer group"
              onClick={() => router.push(module.href)}
            >
              <div className="p-6">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-3 rounded-lg ${module.color}`}>
                    <module.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-lg font-medium text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
                      {module.name}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      {module.description}
                    </p>
                  </div>
                </div>

                <div className="mt-4 flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    Total: <span className="font-medium">{module.stats.total}</span>
                  </div>
                  {typeof module.stats.pending === 'number' && module.stats.pending > 0 && (
                    <div className="text-sm text-orange-600">
                      Pendientes: <span className="font-medium">{module.stats.pending}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-soft border border-gray-200 p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Acciones Rápidas</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {canAccessOT() && (
              <button
                onClick={() => router.push("/app/ot/crear")}
                className="flex items-center p-3 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
              >
                <ClipboardDocumentListIcon className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm font-medium text-gray-700">Nueva OT</span>
              </button>
            )}

            {canAccessDocuments() && (
              <button
                onClick={() => router.push("/app/documentos/privados/crear")}
                className="flex items-center p-3 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
              >
                <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm font-medium text-gray-700">Nuevo Documento</span>
              </button>
            )}

            {canAccessReports() && (
              <button
                onClick={() => router.push("/app/reportes")}
                className="flex items-center p-3 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
              >
                <ChartBarIcon className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm font-medium text-gray-700">Ver Reportes</span>
              </button>
            )}

            {canAccessUsers() && (
              <button
                onClick={() => router.push("/app/usuarios")}
                className="flex items-center p-3 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
              >
                <UsersIcon className="h-5 w-5 text-gray-400 mr-3" />
                <span className="text-sm font-medium text-gray-700">Gestionar Usuarios</span>
              </button>
            )}
          </div>
        </div>
      </PageContent>
    </MainLayout>
  )
}
