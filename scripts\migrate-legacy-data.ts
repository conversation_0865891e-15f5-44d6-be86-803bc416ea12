import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import mysql from 'mysql2/promise'

const prisma = new PrismaClient()

// Configuración de la base de datos legacy (MariaDB)
const legacyDbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'your_legacy_password',
  database: 'legacy_notaria_db'
}

async function migrateLegacyData() {
  console.log('🔄 Iniciando migración de datos legacy...')

  let legacyConnection: mysql.Connection | null = null

  try {
    // Conectar a la base de datos legacy
    legacyConnection = await mysql.createConnection(legacyDbConfig)
    console.log('✅ Conectado a base de datos legacy')

    // 1. Migrar usuarios
    await migrateUsers(legacyConnection)

    // 2. Migrar roles (si existen en el sistema RBAC legacy)
    await migrateRoles(legacyConnection)

    // 3. Migrar permisos
    await migratePermissions(legacyConnection)

    // 4. Migrar relaciones usuario-rol
    await migrateUserRoles(legacyConnection)

    console.log('✅ Migración completada exitosamente!')

  } catch (error) {
    console.error('❌ Error durante la migración:', error)
    throw error
  } finally {
    if (legacyConnection) {
      await legacyConnection.end()
    }
    await prisma.$disconnect()
  }
}

async function migrateUsers(legacyConnection: mysql.Connection) {
  console.log('📥 Migrando usuarios...')

  // Obtener usuarios del sistema legacy
  const [legacyUsers] = await legacyConnection.execute(`
    SELECT 
      id,
      nombre as username,
      nombreSocial,
      password,
      email,
      estado,
      created_at,
      updated_at
    FROM usuarios_not 
    WHERE estado = 1
  `)

  const users = legacyUsers as any[]

  for (const legacyUser of users) {
    try {
      // Verificar si el usuario ya existe
      const existingUser = await prisma.user.findUnique({
        where: { username: legacyUser.username.toUpperCase() }
      })

      if (existingUser) {
        console.log(`⚠️  Usuario ${legacyUser.username} ya existe, saltando...`)
        continue
      }

      // Hash de la contraseña si no está hasheada
      let hashedPassword = legacyUser.password
      if (!legacyUser.password.startsWith('$2')) {
        hashedPassword = await bcrypt.hash(legacyUser.password, 12)
      }

      // Crear usuario en el nuevo sistema
      await prisma.user.create({
        data: {
          username: legacyUser.username.toUpperCase(),
          email: legacyUser.email || null,
          password: hashedPassword,
          nombreSocial: legacyUser.nombreSocial || null,
          estado: legacyUser.estado === 1,
          createdAt: legacyUser.created_at || new Date(),
          updatedAt: legacyUser.updated_at || new Date()
        }
      })

      console.log(`✅ Usuario migrado: ${legacyUser.username}`)

    } catch (error) {
      console.error(`❌ Error migrando usuario ${legacyUser.username}:`, error)
    }
  }

  console.log(`📊 Migración de usuarios completada: ${users.length} usuarios procesados`)
}

async function migrateRoles(legacyConnection: mysql.Connection) {
  console.log('📥 Migrando roles...')

  try {
    // Intentar obtener roles del sistema RBAC legacy
    const [legacyRoles] = await legacyConnection.execute(`
      SELECT 
        ID as id,
        Title as name,
        Description as description
      FROM rbac_roles
    `)

    const roles = legacyRoles as any[]

    for (const legacyRole of roles) {
      try {
        await prisma.role.upsert({
          where: { name: legacyRole.name },
          update: {
            description: legacyRole.description
          },
          create: {
            name: legacyRole.name,
            description: legacyRole.description || null
          }
        })

        console.log(`✅ Rol migrado: ${legacyRole.name}`)
      } catch (error) {
        console.error(`❌ Error migrando rol ${legacyRole.name}:`, error)
      }
    }

    console.log(`📊 Migración de roles completada: ${roles.length} roles procesados`)

  } catch (error) {
    console.log('⚠️  Tabla rbac_roles no encontrada, creando roles básicos...')
    
    // Crear roles básicos si no existen en el legacy
    const basicRoles = [
      { name: 'admin', description: 'Administrador del sistema' },
      { name: 'user', description: 'Usuario básico' },
      { name: 'repertorista', description: 'Repertorista' },
      { name: 'notario', description: 'Notario' }
    ]

    for (const role of basicRoles) {
      await prisma.role.upsert({
        where: { name: role.name },
        update: {},
        create: role
      })
    }
  }
}

async function migratePermissions(legacyConnection: mysql.Connection) {
  console.log('📥 Migrando permisos...')

  try {
    const [legacyPermissions] = await legacyConnection.execute(`
      SELECT 
        ID as id,
        Title as name,
        Description as description
      FROM rbac_permissions
    `)

    const permissions = legacyPermissions as any[]

    for (const legacyPermission of permissions) {
      try {
        // Determinar módulo basado en el nombre del permiso
        let module = null
        if (legacyPermission.name.includes('ot')) module = 'ot'
        else if (legacyPermission.name.includes('documento')) module = 'documentos'
        else if (legacyPermission.name.includes('usuario')) module = 'usuarios'
        else if (legacyPermission.name.includes('repertorio')) module = 'repertorio'
        else if (legacyPermission.name.includes('caja')) module = 'caja'

        await prisma.permission.upsert({
          where: { name: legacyPermission.name },
          update: {
            description: legacyPermission.description,
            module: module
          },
          create: {
            name: legacyPermission.name,
            description: legacyPermission.description || null,
            module: module
          }
        })

        console.log(`✅ Permiso migrado: ${legacyPermission.name}`)
      } catch (error) {
        console.error(`❌ Error migrando permiso ${legacyPermission.name}:`, error)
      }
    }

    console.log(`📊 Migración de permisos completada: ${permissions.length} permisos procesados`)

  } catch (error) {
    console.log('⚠️  Tabla rbac_permissions no encontrada, usando permisos del seed...')
  }
}

async function migrateUserRoles(legacyConnection: mysql.Connection) {
  console.log('📥 Migrando relaciones usuario-rol...')

  try {
    const [legacyUserRoles] = await legacyConnection.execute(`
      SELECT 
        ur.UserID as legacy_user_id,
        ur.RoleID as legacy_role_id,
        u.nombre as username,
        r.Title as role_name
      FROM rbac_userroles ur
      JOIN usuarios_not u ON ur.UserID = u.id
      JOIN rbac_roles r ON ur.RoleID = r.ID
      WHERE u.estado = 1
    `)

    const userRoles = legacyUserRoles as any[]

    for (const legacyUserRole of userRoles) {
      try {
        // Buscar usuario y rol en el nuevo sistema
        const user = await prisma.user.findUnique({
          where: { username: legacyUserRole.username.toUpperCase() }
        })

        const role = await prisma.role.findUnique({
          where: { name: legacyUserRole.role_name }
        })

        if (user && role) {
          await prisma.userRole.upsert({
            where: {
              userId_roleId: {
                userId: user.id,
                roleId: role.id
              }
            },
            update: {},
            create: {
              userId: user.id,
              roleId: role.id
            }
          })

          console.log(`✅ Relación migrada: ${user.username} -> ${role.name}`)
        }
      } catch (error) {
        console.error(`❌ Error migrando relación usuario-rol:`, error)
      }
    }

    console.log(`📊 Migración de relaciones completada: ${userRoles.length} relaciones procesadas`)

  } catch (error) {
    console.log('⚠️  No se pudieron migrar relaciones usuario-rol del legacy')
  }
}

// Ejecutar migración si se llama directamente
if (require.main === module) {
  migrateLegacyData()
    .catch((e) => {
      console.error('❌ Error fatal durante la migración:', e)
      process.exit(1)
    })
}

export { migrateLegacyData }
