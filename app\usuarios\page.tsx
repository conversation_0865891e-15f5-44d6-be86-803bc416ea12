"use client"

import { UnifiedLayout } from "@/components/layout/unified-layout"

export default function UsuariosPage() {
  return (
    <UnifiedLayout>
      <div style={{ padding: '24px' }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
          {/* Header de página */}
          <div style={{
            backgroundColor: 'white',
            borderBottom: '1px solid #d1d5db',
            padding: '24px',
            marginBottom: '24px',
            borderRadius: '8px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
          }}>
            <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827' }}>
              Gestión de Usuarios
            </h1>
            <p style={{ marginTop: '4px', fontSize: '14px', color: '#4b5563' }}>
              Administración de usuarios, roles y permisos del sistema
            </p>
          </div>

          {/* Contenido */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            border: '1px solid #d1d5db',
            padding: '24px'
          }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '16px'
            }}>
              <div style={{
                backgroundColor: '#eff6ff',
                padding: '16px',
                borderRadius: '8px',
                border: '1px solid #bfdbfe'
              }}>
                <h3 style={{ fontWeight: '500', color: '#1e3a8a', marginBottom: '8px' }}>
                  Usuarios Activos
                </h3>
                <p style={{ fontSize: '14px', color: '#1e40af' }}>12 usuarios registrados</p>
              </div>

              <div style={{
                backgroundColor: '#f0fdf4',
                padding: '16px',
                borderRadius: '8px',
                border: '1px solid #bbf7d0'
              }}>
                <h3 style={{ fontWeight: '500', color: '#14532d', marginBottom: '8px' }}>
                  Roles Configurados
                </h3>
                <p style={{ fontSize: '14px', color: '#15803d' }}>5 roles disponibles</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  )
}
