"use client"

import { useSession } from "next-auth/react"
import { useRouter, usePathname } from "next/navigation"
import { useEffect, useState, ReactNode } from "react"
import { getAppName, getCompanyName } from "@/lib/config"
import { UserMenuInline } from "@/components/layout/user-menu"

interface UnifiedLayoutProps {
  children: ReactNode
}

export function UnifiedLayout({ children }: UnifiedLayoutProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
    }
  }, [status, router])

  // Manejar fullscreen
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen()
      } else {
        await document.exitFullscreen()
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error)
    }
  }

  if (status === "loading") {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f9fafb'
      }}>
        <div style={{ 
          width: '48px', 
          height: '48px', 
          border: '2px solid #0ea5e9',
          borderTop: '2px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const isCurrentPage = (page: string) => {
    if (page === 'dashboard') return pathname === '/dashboard'
    return pathname.startsWith(`/${page}`)
  }

  const menuItems = [
    { id: 'dashboard', href: '/dashboard', icon: '🏠', label: 'Inicio' },
    { id: 'ot', href: '/ot', icon: '📋', label: 'Órdenes de Trabajo' },
    { id: 'documentos', href: '/documentos', icon: '📄', label: 'Documentos' },
    { id: 'usuarios', href: '/usuarios', icon: '👥', label: 'Usuarios' },
    { id: 'reportes', href: '/reportes', icon: '📊', label: 'Reportes' },
    { id: 'caja', href: '/caja', icon: '💰', label: 'Caja' },
    { id: 'licencias', href: '/licencias', icon: '🔐', label: 'Licencias' },
  ]

  return (
    <div style={{ height: '100vh', display: 'flex', backgroundColor: '#f3f4f6' }}>
      {/* Sidebar */}
      <div style={{ 
        width: sidebarCollapsed ? '60px' : '256px',
        backgroundColor: 'white', 
        borderRight: '1px solid #d1d5db',
        display: 'flex',
        flexDirection: 'column',
        transition: 'width 0.3s ease',
        overflow: 'hidden'
      }}>
        {/* Header del sidebar */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          padding: sidebarCollapsed ? '16px 14px' : '16px', 
          borderBottom: '1px solid #d1d5db',
          minHeight: '64px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', width: '100%' }}>
            <div style={{ 
              width: '32px', 
              height: '32px', 
              backgroundColor: '#0ea5e9', 
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
              flexShrink: 0
            }}>
              <span style={{ color: 'white', fontWeight: 'bold', fontSize: '14px' }}>e</span>
            </div>
            {!sidebarCollapsed && (
              <span style={{ 
                fontSize: '20px', 
                fontWeight: 'bold', 
                color: '#374151',
                whiteSpace: 'nowrap',
                overflow: 'hidden'
              }}>
                {getAppName()}
              </span>
            )}
          </div>
        </div>

        {/* Navegación */}
        <nav style={{ 
          flex: 1, 
          padding: sidebarCollapsed ? '16px 8px' : '16px 12px', 
          display: 'flex', 
          flexDirection: 'column', 
          gap: '4px' 
        }}>
          {menuItems.map((item) => (
            <a
              key={item.id}
              href={item.href}
              style={{ 
                display: 'flex', 
                alignItems: 'center', 
                padding: sidebarCollapsed ? '12px 8px' : '8px 12px', 
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: isCurrentPage(item.id) ? '#dbeafe' : 'transparent',
                color: isCurrentPage(item.id) ? '#1e40af' : '#4b5563',
                textDecoration: 'none',
                transition: 'background-color 0.2s',
                justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
                position: 'relative',
                minHeight: '40px'
              }}
              onMouseEnter={(e) => {
                if (!isCurrentPage(item.id)) {
                  e.target.style.backgroundColor = '#f3f4f6'
                }
              }}
              onMouseLeave={(e) => {
                if (!isCurrentPage(item.id)) {
                  e.target.style.backgroundColor = 'transparent'
                }
              }}
              title={sidebarCollapsed ? item.label : undefined}
            >
              <span style={{ 
                marginRight: sidebarCollapsed ? '0' : '12px',
                fontSize: '16px'
              }}>
                {item.icon}
              </span>
              {!sidebarCollapsed && (
                <span style={{ whiteSpace: 'nowrap', overflow: 'hidden' }}>
                  {item.label}
                </span>
              )}
            </a>
          ))}
        </nav>

        {/* Footer del sidebar */}
        <div style={{ 
          padding: sidebarCollapsed ? '8px' : '16px', 
          borderTop: '1px solid #d1d5db' 
        }}>
          <div style={{ 
            fontSize: '12px', 
            color: '#6b7280', 
            textAlign: 'center',
            whiteSpace: sidebarCollapsed ? 'nowrap' : 'normal',
            overflow: 'hidden'
          }}>
            {sidebarCollapsed ? 'v1.0' : `${getAppName()} v1.0.0`}
          </div>
        </div>
      </div>

      {/* Área de contenido principal */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', minWidth: 0 }}>
        {/* Navbar */}
        <header style={{ 
          backgroundColor: 'white', 
          borderBottom: '1px solid #d1d5db',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
          zIndex: 10
        }}>
          <div style={{ padding: '0 16px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              height: '64px' 
            }}>
              {/* Left side */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                {/* Toggle sidebar button */}
                <button
                  onClick={toggleSidebar}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    backgroundColor: 'transparent',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    color: '#6b7280',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#f3f4f6'
                    e.target.style.color = '#374151'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'transparent'
                    e.target.style.color = '#6b7280'
                  }}
                  title={sidebarCollapsed ? 'Mostrar menú' : 'Ocultar menú'}
                >
                  <span style={{ fontSize: '14px' }}>
                    {sidebarCollapsed ? '☰' : '◀'}
                  </span>
                </button>

                {/* Fullscreen button */}
                <button
                  onClick={toggleFullscreen}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    backgroundColor: 'transparent',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    color: '#6b7280',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#f3f4f6'
                    e.target.style.color = '#374151'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'transparent'
                    e.target.style.color = '#6b7280'
                  }}
                  title={isFullscreen ? 'Salir de pantalla completa' : 'Pantalla completa'}
                >
                  <span style={{ fontSize: '14px' }}>
                    {isFullscreen ? '⛶' : '⛶'}
                  </span>
                </button>

                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ fontSize: '18px', fontWeight: '600', color: '#374151' }}>
                    {getAppName()}
                  </span>
                  <span style={{ fontSize: '14px', color: '#6b7280' }}>
                    - {getCompanyName()}
                  </span>
                </div>
              </div>

              {/* Right side */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <UserMenuInline />
              </div>
            </div>
          </div>
        </header>

        {/* Contenido principal */}
        <main style={{ flex: 1, overflow: 'auto' }}>
          {children}
        </main>
      </div>

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
