// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modelo de Usuario
model User {
  id          String   @id @default(cuid())
  username    String   @unique
  email       String?  @unique
  password    String
  nombreSocial String?
  estado      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLogin   DateTime?

  // Relaciones
  sessions       Session[]
  userRoles      UserRole[]
  activeSessions ActiveSession[]

  @@map("users")
}

// Modelo de Sesión para NextAuth
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Modelo de Cuenta para NextAuth (OAuth providers)
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// Modelo de Token de Verificación
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Sistema RBAC - Roles
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  userRoles   UserRole[]
  rolePermissions RolePermission[]

  @@map("rbac_roles")
}

// Sistema RBAC - Permisos
model Permission {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  module      String?  // Módulo al que pertenece (ot, documentos, etc.)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  rolePermissions RolePermission[]

  @@map("rbac_permissions")
}

// Sistema RBAC - Relación Usuario-Rol
model UserRole {
  id     String @id @default(cuid())
  userId String
  roleId String

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("rbac_user_roles")
}

// Sistema RBAC - Relación Rol-Permiso
model RolePermission {
  id           String @id @default(cuid())
  roleId       String
  permissionId String

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("rbac_role_permissions")
}

// Modelo de Auditoría para Seguridad
model AuditLog {
  id        String   @id @default(cuid())
  action    String   // LOGIN_SUCCESS, LOGIN_FAILED, PERMISSION_DENIED, etc.
  details   String?
  ip        String?
  userAgent String?
  userId    String?
  metadata  Json?    // Información adicional en formato JSON
  createdAt DateTime @default(now())

  @@map("audit_logs")
  @@index([action])
  @@index([userId])
  @@index([createdAt])
}

// Modelo para Control de Intentos de Login
model LoginAttempt {
  id          String   @id @default(cuid())
  ip          String
  username    String?
  successful  Boolean
  userAgent   String?
  createdAt   DateTime @default(now())

  @@map("login_attempts")
  @@index([ip, createdAt])
  @@index([username, createdAt])
}

// Modelo para Configuración del Sistema
model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String
  description String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}

// Modelo para Sesiones Activas (control granular)
model ActiveSession {
  id           String   @id @default(cuid())
  userId       String
  sessionToken String   @unique
  ip           String?
  userAgent    String?
  lastActivity DateTime @default(now())
  expiresAt    DateTime
  createdAt    DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("active_sessions")
  @@index([userId])
  @@index([expiresAt])
}
