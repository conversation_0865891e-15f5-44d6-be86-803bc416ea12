"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { getAppName, getCompanyName } from "@/lib/config"
import { LicenseManager } from "@/components/license/license-manager"
import { LicenseData } from "@/lib/license"

export default function LicenciasPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [currentLicense, setCurrentLicense] = useState<LicenseData | null>(null)

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f9fafb'
      }}>
        <div style={{ 
          width: '48px', 
          height: '48px', 
          border: '2px solid #0ea5e9',
          borderTop: '2px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const handleLicenseValidated = (license: LicenseData) => {
    setCurrentLicense(license)
    console.log('Licencia validada:', license)
  }

  const handleLicenseError = (error: string) => {
    console.error('Error de licencia:', error)
  }

  return (
    <div style={{ height: '100vh', display: 'flex', backgroundColor: '#f3f4f6' }}>
      {/* Sidebar */}
      <div style={{ 
        width: '256px', 
        backgroundColor: 'white', 
        borderRight: '1px solid #d1d5db',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header del sidebar */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          padding: '16px', 
          borderBottom: '1px solid #d1d5db' 
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div style={{ 
              width: '32px', 
              height: '32px', 
              backgroundColor: '#0ea5e9', 
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <span style={{ color: 'white', fontWeight: 'bold', fontSize: '14px' }}>e</span>
            </div>
            <span style={{ fontSize: '20px', fontWeight: 'bold', color: '#374151' }}>
              {getAppName()}
            </span>
          </div>
        </div>

        {/* Navegación */}
        <nav style={{ flex: 1, padding: '16px 12px', display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <a
            href="/dashboard"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>🏠</span>
            <span>Inicio</span>
          </a>
          
          <a
            href="/ot"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>📋</span>
            <span>Órdenes de Trabajo</span>
          </a>
          
          <a
            href="/documentos"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>📄</span>
            <span>Documentos</span>
          </a>
          
          <a
            href="/usuarios"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>👥</span>
            <span>Usuarios</span>
          </a>
          
          <a
            href="/licencias"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: '#dbeafe',
              color: '#1e40af',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>🔐</span>
            <span>Licencias</span>
          </a>
        </nav>

        {/* Footer del sidebar */}
        <div style={{ padding: '16px', borderTop: '1px solid #d1d5db' }}>
          <div style={{ fontSize: '12px', color: '#6b7280', textAlign: 'center' }}>
            {getAppName()} v1.0.0
          </div>
        </div>
      </div>

      {/* Área de contenido principal */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', minWidth: 0 }}>
        {/* Navbar */}
        <header style={{ 
          backgroundColor: 'white', 
          borderBottom: '1px solid #d1d5db',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ padding: '0 16px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              height: '64px' 
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ fontSize: '18px', fontWeight: '600', color: '#374151' }}>
                    Gestión de Licencias
                  </span>
                </div>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <span style={{ fontSize: '14px', color: '#4b5563' }}>
                  {session.user.nombreSocial || session.user.username}
                </span>
                <button style={{ 
                  backgroundColor: '#dc2626', 
                  color: 'white', 
                  padding: '4px 12px', 
                  borderRadius: '4px',
                  fontSize: '14px',
                  border: 'none',
                  cursor: 'pointer'
                }}>
                  Salir
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Contenido principal */}
        <main style={{ flex: 1, overflow: 'auto' }}>
          <div style={{ padding: '24px' }}>
            <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
              
              {/* Información sobre el sistema de licencias */}
              <div style={{ 
                backgroundColor: '#eff6ff', 
                padding: '16px', 
                borderRadius: '8px',
                border: '1px solid #bfdbfe',
                marginBottom: '24px'
              }}>
                <h3 style={{ fontSize: '16px', fontWeight: '500', color: '#1e3a8a', marginBottom: '8px' }}>
                  Sistema de Licencias e-NOTARIA
                </h3>
                <p style={{ fontSize: '14px', color: '#1e40af', marginBottom: '8px' }}>
                  El sistema de licencias de e-NOTARIA utiliza cifrado avanzado y validación online para proteger su software.
                </p>
                <ul style={{ fontSize: '12px', color: '#1e40af', paddingLeft: '16px' }}>
                  <li>Licencias cifradas con datos específicos del cliente</li>
                  <li>Validación online automática cada 24 horas</li>
                  <li>Fingerprint de hardware para prevenir uso no autorizado</li>
                  <li>Diferentes tipos de licencia según las necesidades</li>
                </ul>
              </div>

              {/* Componente de gestión de licencias */}
              <LicenseManager 
                onLicenseValidated={handleLicenseValidated}
                onLicenseError={handleLicenseError}
              />

              {/* Información técnica */}
              {currentLicense && (
                <div style={{ 
                  backgroundColor: '#f0fdf4', 
                  padding: '16px', 
                  borderRadius: '8px',
                  border: '1px solid #bbf7d0',
                  marginTop: '24px'
                }}>
                  <h3 style={{ fontSize: '16px', fontWeight: '500', color: '#14532d', marginBottom: '8px' }}>
                    Información Técnica de la Licencia
                  </h3>
                  <div style={{ fontSize: '12px', color: '#15803d', fontFamily: 'monospace' }}>
                    <div><strong>ID de Instalación:</strong> {currentLicense.systemInfo.installationId}</div>
                    <div><strong>Fingerprint:</strong> {currentLicense.systemInfo.hardwareFingerprint}</div>
                    <div><strong>Versión:</strong> {currentLicense.systemInfo.version}</div>
                    <div><strong>Clave de Licencia:</strong> {currentLicense.licenseKey}</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
