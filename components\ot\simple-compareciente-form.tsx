"use client"

import { useState } from "react"

interface SimpleComparecienteFormProps {
  calidades: Array<{value: string, label: string}>
  onAdd: (compareciente: any) => void
  comparecientes: any[]
  onRemove: (id: string) => void
}

export function SimpleComparecienteForm({ calidades, onAdd, comparecientes, onRemove }: SimpleComparecienteFormProps) {
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState({
    rut: '',
    nombres: '',
    apellidoPaterno: '',
    apellidoMaterno: '',
    calidad: '',
    esChileno: true
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    console.log('SimpleComparecienteForm - Submit with:', formData)
    
    // Basic validation
    if (!formData.rut || !formData.nombres || !formData.apellidoPaterno || !formData.calidad) {
      alert('Por favor complete todos los campos requeridos')
      return
    }

    // Call onAdd
    console.log('SimpleComparecienteForm - Calling onAdd')
    onAdd(formData)
    
    // Reset form
    setFormData({
      rut: '',
      nombres: '',
      apellidoPaterno: '',
      apellidoMaterno: '',
      calidad: '',
      esChileno: true
    })
    setShowForm(false)
  }

  return (
    <div className="space-y-4">
      <div className="bg-green-50 border border-green-200 rounded p-4">
        <h4 className="font-medium text-green-800 mb-2">
          🔧 Simple Form - Comparecientes: {comparecientes.length}
        </h4>
        
        {/* Existing Comparecientes */}
        {comparecientes.length > 0 && (
          <div className="mb-4">
            <h5 className="font-medium text-gray-700 mb-2">Lista actual:</h5>
            {comparecientes.map((comp, index) => (
              <div key={comp.id || index} className="flex justify-between items-center bg-white p-2 rounded border mb-1">
                <span className="text-sm">
                  {comp.nombres} {comp.apellidoPaterno} {comp.apellidoMaterno} ({comp.rut}) - {comp.calidad}
                </span>
                <button
                  onClick={() => onRemove(comp.id)}
                  className="text-red-600 text-sm hover:text-red-800"
                >
                  Eliminar
                </button>
              </div>
            ))}
          </div>
        )}

        {!showForm && (
          <button
            onClick={() => {
              console.log('SimpleComparecienteForm - Show form clicked')
              setShowForm(true)
            }}
            className="bg-green-600 text-white px-3 py-1 rounded text-sm"
          >
            ➕ Agregar Compareciente (Simple)
          </button>
        )}

        {showForm && (
          <form onSubmit={handleSubmit} className="mt-4 space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  RUT *
                </label>
                <input
                  type="text"
                  value={formData.rut}
                  onChange={(e) => setFormData({ ...formData, rut: e.target.value })}
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                  placeholder="12345678-9"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Calidad *
                </label>
                <select
                  value={formData.calidad}
                  onChange={(e) => setFormData({ ...formData, calidad: e.target.value })}
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                  required
                >
                  <option value="">Seleccionar...</option>
                  {calidades.map((calidad) => (
                    <option key={calidad.value} value={calidad.value}>
                      {calidad.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nombres *
                </label>
                <input
                  type="text"
                  value={formData.nombres}
                  onChange={(e) => setFormData({ ...formData, nombres: e.target.value })}
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Apellido Paterno *
                </label>
                <input
                  type="text"
                  value={formData.apellidoPaterno}
                  onChange={(e) => setFormData({ ...formData, apellidoPaterno: e.target.value })}
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Apellido Materno
                </label>
                <input
                  type="text"
                  value={formData.apellidoMaterno}
                  onChange={(e) => setFormData({ ...formData, apellidoMaterno: e.target.value })}
                  className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                />
              </div>

              <div>
                <label className="flex items-center mt-4">
                  <input
                    type="checkbox"
                    checked={formData.esChileno}
                    onChange={(e) => setFormData({ ...formData, esChileno: e.target.checked })}
                    className="mr-2"
                  />
                  <span className="text-sm">Es chileno</span>
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-2">
              <button
                type="button"
                onClick={() => setShowForm(false)}
                className="px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="px-3 py-1 bg-green-600 text-white rounded text-sm"
              >
                Agregar
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}
