"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { MainLayout, PageHeader, PageContent } from "@/components/layout/main-layout"
import { getAppName, getCompanyName } from "@/lib/config"

export default function TestDashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <MainLayout>
      <PageHeader
        title={`Bienvenido, ${session.user.nombreSocial || session.user.username}`}
        description={`Panel de control - ${getCompanyName()}`}
      />
      
      <PageContent>
        <div className="bg-white rounded-lg shadow-soft border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Test del Layout
          </h2>
          <p className="text-gray-600">
            Si puedes ver este contenido con sidebar y navbar, el layout está funcionando correctamente.
          </p>
          
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900">Usuario</h3>
              <p className="text-blue-700">{session.user.username}</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-900">Empresa</h3>
              <p className="text-green-700">{getCompanyName()}</p>
            </div>
          </div>
        </div>
      </PageContent>
    </MainLayout>
  )
}
