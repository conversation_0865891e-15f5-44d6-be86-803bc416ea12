// Domain - License - Repository Interface
import { License } from '../entities/license'
import { LicenseId } from '../../shared/value-objects/id'
import { Email } from '../../shared/value-objects/email'

export interface LicenseRepository {
  // Queries
  findById(id: LicenseId): Promise<License | null>
  findByLicenseKey(licenseKey: string): Promise<License | null>
  findByClientEmail(email: Email): Promise<License[]>
  findActiveLicenses(): Promise<License[]>
  findExpiredLicenses(): Promise<License[]>

  // Commands
  save(license: License): Promise<void>
  update(license: License): Promise<void>
  delete(id: LicenseId): Promise<void>

  // Specific queries
  existsByLicenseKey(licenseKey: string): Promise<boolean>
  countActiveLicenses(): Promise<number>
}
