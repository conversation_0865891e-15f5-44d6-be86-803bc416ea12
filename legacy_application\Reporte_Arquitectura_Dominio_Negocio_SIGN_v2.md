# Reporte de Arquitectura de Código - Dominio de Negocio
## Sistema SIGN+ v2 - Sistema de Gestión Notarial

### Información General
- **Sistema**: SIGN+ v2 (Sistema de Gestión Notarial)
- **Tecnología**: PHP 8.1, MySQL, JavaScript
- **Arquitectura**: Monolítica con patrón MVC
- **Framework**: Custom PHP con librerías especializadas

---

## 1. DOMINIO DE NEGOCIO PRINCIPAL

### 1.1 Contexto del Negocio
El sistema SIGN+ v2 es una plataforma integral para la gestión de servicios notariales que automatiza y digitaliza los procesos de una notaría, incluyendo:

- **Gestión documental notarial**
- **Firma digital y certificación electrónica**
- **Procesos administrativos y financieros**
- **Control de repertorios y registros**
- **Servicios de atención al cliente**

### 1.2 Entidades de Dominio Principales

#### **Usuario** (`entidades/usuario.php`)
- **Responsabilidad**: Gestión de autenticación, autorización y permisos
- **Atributos**: username, password, nombreSocial, estado
- **Funcionalidades**:
  - Autenticación JWT
  - Sistema RBAC (Role-Based Access Control)
  - Gestión de sesiones y tokens cifrados

#### **Orden de Trabajo** (`entidades/ordendeTrabajo.php`)
- **Responsabilidad**: Gestión del flujo de trabajo notarial
- **Atributos**: 
  - Datos principales: numero_ot, tipo_documento, fecha, materia
  - Cliente: rut_cliente, nombres_cliente, apellidos
  - Proceso: gestora, numero_wf, jefa_registro, ayudante
  - Repertorio: repertorio, fecha_repertorio, notario
  - UAF: direccion_flujo_UAF
- **Funcionalidades**:
  - Seguimiento de avances
  - Gestión de comparecientes
  - Control de repertorio

#### **Menu** (`entidades/menu.php`)
- **Responsabilidad**: Generación dinámica de menús basada en roles
- **Funcionalidades**:
  - Aplicación de restricciones RBAC
  - Generación de shortcuts de inicio
  - Configuración modular de accesos

---

## 2. MÓDULOS FUNCIONALES DEL SISTEMA

### 2.1 Módulos Principales (según `layouts/raf.json`)

#### **Módulo de Caja (BHE)**
- **Ubicación**: `app/caja_v2/`
- **Funcionalidad**: Emisión de Boletas de Honorarios Electrónicas
- **Roles**: CAJERO, FUNCIONARIO, JEFE_REGISTRO, REPERTORISTA, NOTARIO, ADMINISTRADOR

#### **Órdenes de Trabajo (OT)**
- **Ubicación**: `app/ot/`
- **Funcionalidad**: Creación, Modificación y Seguimiento de Órdenes de Trabajo
- **Tecnología**: Vue.js frontend + PHP backend
- **Roles**: CAJERO, FUNCIONARIO, JEFE_REGISTRO, REPERTORISTA, NOTARIO

#### **Documentos Privados**
- **Ubicación**: `app/documento_privado/`
- **Sub-módulos**:
  - **Documento Privado Local**: Solicitudes iniciadas en oficina
  - **Documento Privado Online**: Solicitudes web de notarial.cl
  - **Documento Privado Externo**: Integración con sistemas externos
  - **Notas de Cobro**: Generación express de notas de cobro

#### **Escrituras Públicas**
- **Ubicación**: `app/escrituras_publicas/`
- **Sub-módulos**:
  - **Copias de Escrituras**: Emisión basada en número de repertorio
  - **Extractos**: Extractos de escrituras públicas
  - **Repertorio**: Gestión de repertorio de escrituras
  - **Borradores y Certificados**

#### **Vehículos**
- **Ubicación**: `app/vehiculos/`
- **Sub-módulos**:
  - **STEV**: Servicio de Transferencia Electrónica de Vehículos
  - **Repertorio de Vehículos**: Gestión de repertorio vehicular

#### **Letras**
- **Ubicación**: `app/letras/`
- **Funcionalidades**:
  - Ingreso y gestión de letras
  - Generación de citaciones
  - Informes de protestos
  - Boletín comercial

#### **Firma Digital**
- **Ubicación**: `app/firma_digital/`
- **Funcionalidades**:
  - Gestión de certificados digitales
  - Proceso de firma electrónica
  - Bloqueo de documentos
  - Verificación de documentos firmados

---

## 3. ARQUITECTURA DE CAPAS

### 3.1 Capa de Presentación
- **Frontend**: HTML5, CSS3, JavaScript, Vue.js (módulos específicos)
- **Templates**: Sistema de layouts modulares (`layouts/`)
- **Assets**: Recursos estáticos (`assets/`)

### 3.2 Capa de Lógica de Negocio
- **Controladores**: Archivos index.php en cada módulo
- **Servicios**: Clases especializadas en `calls/lib_*.php`
- **APIs**: Endpoints REST en `api/` de cada módulo

### 3.3 Capa de Datos
- **Entidades**: Clases de dominio en `entidades/`
- **Acceso a Datos**: Clase `ingreso` para conexiones MySQL
- **Base de Datos**: MySQL con múltiples esquemas especializados

---

## 4. PATRONES ARQUITECTÓNICOS

### 4.1 Patrón MVC
- **Modelo**: Entidades de dominio y clases de acceso a datos
- **Vista**: Templates HTML con PHP embebido
- **Controlador**: Scripts de procesamiento y APIs

### 4.2 Patrón Repository
- Implementado a través de clases especializadas (`lib_*.php`)
- Separación de lógica de acceso a datos

### 4.3 Patrón Factory
- Utilizado en la generación de menús dinámicos
- Creación de objetos basada en configuración JSON

---

## 5. SISTEMA DE SEGURIDAD Y PERMISOS

### 5.1 RBAC (Role-Based Access Control)
- **Librería**: PhpRbac (`library/PhpRbac/`)
- **Roles Definidos**:
  - ADMINISTRADOR
  - NOTARIO
  - REPERTORISTA
  - JEFE_REGISTRO
  - FUNCIONARIO
  - CAJERO

### 5.2 Autenticación
- **JWT Tokens**: Implementación custom (`library/jwt/`)
- **Cifrado**: AES-128-CTR para tokens
- **Sesiones**: Gestión de sesiones PHP con validación JWT

### 5.3 Autorización
- Control granular por módulo y funcionalidad
- Configuración declarativa en `layouts/raf.json`
- Validación en tiempo de ejecución

---

## 6. INTEGRACIONES EXTERNAS

### 6.1 Servicios Notariales
- **Notarial.cl**: Integración para documentos privados externos
- **STEV**: Servicio de Transferencia Electrónica de Vehículos
- **Webservices**: Comunicación SOAP/REST con entidades externas

### 6.2 Servicios de Firma Digital
- **Certificados PEM**: Gestión de certificados digitales
- **Firma Electrónica**: Proceso de firma con validación criptográfica
- **Timestamping**: Sellado de tiempo para documentos

### 6.3 Servicios Financieros
- **Webpay**: Integración para pagos electrónicos
- **Bancos**: Conectividad con sistemas bancarios
- **Boletas Electrónicas**: Emisión de documentos tributarios

---

## 7. GESTIÓN DE DOCUMENTOS

### 7.1 Tipos de Documentos
- **Escrituras Públicas**: Documentos notariales principales
- **Documentos Privados**: Contratos y acuerdos privados
- **Certificados**: Documentos de certificación
- **Copias**: Reproducciones autenticadas
- **Extractos**: Resúmenes de documentos principales

### 7.2 Flujo de Procesamiento
1. **Ingreso**: Recepción de solicitud
2. **Validación**: Verificación de datos y documentos
3. **Procesamiento**: Generación del documento
4. **Firma**: Proceso de firma digital
5. **Entrega**: Distribución al cliente

---

## 8. TECNOLOGÍAS Y LIBRERÍAS

### 8.1 Backend
- **PHP 8.1**: Lenguaje principal
- **MySQL**: Base de datos relacional
- **Composer**: Gestión de dependencias
- **FPDF/TCPDF**: Generación de PDFs

### 8.2 Frontend
- **Vue.js**: Framework reactivo para módulos específicos
- **jQuery**: Manipulación DOM y AJAX
- **Bootstrap**: Framework CSS
- **TinyAjax**: Librería AJAX custom

### 8.3 Librerías Especializadas
- **PhpRbac**: Control de acceso basado en roles
- **JWT**: Autenticación por tokens
- **Sentry**: Monitoreo de errores
- **HTML2PDF**: Conversión HTML a PDF

---

## 9. CONCLUSIONES Y RECOMENDACIONES

### 9.1 Fortalezas Arquitectónicas
- **Modularidad**: Sistema bien dividido en módulos funcionales
- **Seguridad**: Implementación robusta de RBAC y JWT
- **Escalabilidad**: Arquitectura permite agregar nuevos módulos
- **Especialización**: Cada módulo maneja un dominio específico

### 9.2 Áreas de Mejora
- **Modernización**: Migración a frameworks modernos (Laravel, Symfony)
- **API First**: Implementación de APIs RESTful consistentes
- **Testing**: Implementación de pruebas automatizadas
- **Documentación**: Mejora en documentación técnica

### 9.3 Recomendaciones Técnicas
- Implementar patrón de inyección de dependencias
- Adoptar PSR standards para PHP
- Implementar cache distribuido (Redis)
- Migrar a contenedores (Docker)
- Implementar CI/CD pipeline

---

---

## 10. ANÁLISIS DETALLADO: ORDEN DE TRABAJO - ENTIDAD PRINCIPAL

### 10.1 Justificación como Entidad Principal
La **Orden de Trabajo (OT)** es efectivamente la entidad central del dominio de negocio porque:

1. **Centraliza todos los procesos notariales**
2. **Conecta clientes, documentos, repertorios y pagos**
3. **Controla el flujo de trabajo (workflow)**
4. **Gestiona el ciclo de vida completo del servicio notarial**
5. **Es el punto de referencia para auditoría y seguimiento**

### 10.2 Estructura de Datos Completa

#### **Clase OrdendeTrabajo** (`entidades/ordendeTrabajo.php`)

```php
class OrdendeTrabajo {
    // DATOS PRINCIPALES
    public $numero_ot = 0;
    public $tipo_documento = "";
    public $fecha = null;
    public $materia = "";
    public $materia_detalle = "";

    // CLIENTE PRINCIPAL
    public $rut_cliente = "";
    public $apellido_paterno_cliente = "";
    public $apellido_materno_cliente = "";
    public $nombres_cliente = "";

    // CLIENTE CARGO (Representante)
    public $rut_cliente_cargo = "";
    public $apellido_paterno_cliente_cargo = "";
    public $apellido_materno_cliente_cargo = "";
    public $nombres_cliente_cargo = "";

    // GESTIÓN Y WORKFLOW
    public $gestora = "";
    public $numero_wf = "";
    public $jefa_registro = "";
    public $ayudante = "";
    public $observaciones = "";

    // COMPARECIENTES (Array)
    public $comparecientes = array();

    // AVANCES (Array de seguimiento)
    public $avances = array();

    // REPERTORIO E INFORMACIÓN DIGITAL
    public $repertorio = "";
    public $fecha_repertorio = null;
    public $notario = "";
    public $codigo_fojas = "";

    // UAF (Unidad de Análisis Financiero - ROE)
    public $direccion_flujo_UAF = "";
    public $operacion_UAF = "";
    public $numero_referencia_UAF = "";
    public $fecha_UAF = null;

    // SUJETO CONDUCTOR (Para UAF)
    public $tipo_identificación_sujeto_conductor = "";
    public $numero_identificacion_sujeto_conductor = "";
    public $pais_sujeto_conductor = "";
    public $apellido_paterno_sujeto_conductor = "";
    public $apellido_materno_sujeto_conductor = "";
    public $nombres_sujeto_conductor = "";

    // REPRESENTACIÓN
    public $representacion;

    // REPRESENTADO
    public $tipo_identificación_representado = "";
    public $numero_identificacion_representado = "";
    public $pais_representado = "";
    public $apellido_paterno_representado = "";
    public $apellido_materno_representado = "";
    public $nombres_representado = "";
}
```

### 10.3 Estructura de Base de Datos

#### **Tabla Principal: `ot`**
```sql
CREATE TABLE `ot` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `num_ot` BIGINT(20) NULL,
    `tipo_documento` VARCHAR(80) NULL,
    `repertorio` INT(11) NULL,
    `anho_repertorio` INT(11) NULL,
    `id_materia` BIGINT(20) NULL,
    `detalle_materia` VARCHAR(200) NULL,
    `workflow` VARCHAR(50) NULL,
    `cliente` INT(11) NULL,
    `cliente_cargo` INT(11) NULL,
    `gestora` VARCHAR(150) NULL,
    `jefaregistro` INT(11) NULL,
    `ayudante_registro` INT(11) NULL,
    `observaciones` TEXT NULL,
    `creado` DATETIME NULL,
    `creado_por` VARCHAR(80) NULL,
    `modificado` DATETIME NULL,
    `modificado_por` VARCHAR(80) NULL,
    `eliminado` DATETIME NULL,
    `eliminado_por` VARCHAR(80) NULL,
    `es_roe` VARCHAR(1) NULL DEFAULT 'n',
    `foja_repertorio` VARCHAR(13) NULL,
    `folio_24` VARCHAR(80) NULL,
    `protocolo` VARCHAR(250) NULL,
    `fojas_protocolo` VARCHAR(13) NULL,
    `fecha_repertorio` DATE NULL,
    `notario` VARCHAR(100) NULL
);
```

#### **Tabla Histórica: `cardex`** (Sistema Legacy)
- Mantiene compatibilidad con versiones anteriores
- Contiene los mismos campos que `ot` pero con estructura legacy
- Se sincroniza automáticamente con la tabla `ot`

#### **Tablas Relacionadas:**

**1. `ot_clientes`** - Clientes asociados a la OT
```sql
CREATE TABLE `ot_clientes` (
    `id` BIGINT(21) NOT NULL AUTO_INCREMENT,
    `rut` VARCHAR(15) NOT NULL,
    `nombre` VARCHAR(100) NOT NULL,
    `apellido_paterno` VARCHAR(80) NULL,
    `apellido_materno` VARCHAR(80) NULL,
    `direccion` VARCHAR(200) NULL,
    `comuna` INT(11) NULL,
    `region` INT(11) NULL,
    `email` VARCHAR(150) NULL,
    `fono` VARCHAR(20) NULL,
    `tipo` VARCHAR(100) NOT NULL,
    `id_ot` BIGINT(20) NOT NULL,
    `activo` TINYINT(1) NULL,
    -- Campos de auditoría
    `creado` DATETIME NULL,
    `creado_por` VARCHAR(30) NULL,
    `actualizado` DATETIME NULL,
    `actualizado_por` VARCHAR(30) NULL,
    `eliminado` DATETIME NULL,
    `eliminado_por` VARCHAR(30) NULL
);
```

**2. `ot_avances`** - Seguimiento del workflow
```sql
CREATE TABLE `ot_avances` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `ot` BIGINT(20) NULL,
    `id_workflow` BIGINT(20) NULL,
    `workflow` VARCHAR(150) NULL,
    `id_evento` BIGINT(20) NULL,
    `evento` VARCHAR(150) NULL,
    `id_nota` BIGINT(20) NULL,
    `nota` VARCHAR(150) NULL,
    `observacion` TEXT NULL,
    `alerta` VARCHAR(1) DEFAULT 'n',
    `define_ciclo` VARCHAR(1) DEFAULT 'n',
    `tarea_terminada` VARCHAR(1) DEFAULT 's',
    -- Campos de auditoría
    `creado` DATETIME NULL,
    `creado_por` VARCHAR(50) NULL,
    `modificado` DATETIME NULL,
    `modificado_por` VARCHAR(50) NULL,
    `eliminado` DATETIME NULL,
    `eliminado_por` VARCHAR(50) NULL
);
```

**3. `ot_nota_cobro`** - Facturación y pagos
```sql
CREATE TABLE `ot_nota_cobro` (
    `id` BIGINT(21) NOT NULL AUTO_INCREMENT,
    `OT` BIGINT(21) NULL,
    `total` VARCHAR(100) NULL,
    `total_pago` VARCHAR(100) NULL,
    `cliente` VARCHAR(200) NULL,
    `estado` VARCHAR(80) NULL,
    `bhe` BIGINT(21) NULL,
    `forma_pago` VARCHAR(100) NULL,
    `fecha_vencimiento` DATETIME NULL,
    -- Campos de auditoría
    `creado` DATETIME NULL,
    `creado_por` VARCHAR(30) NULL,
    `modificado` DATETIME NULL,
    `modificado_por` VARCHAR(30) NULL,
    `eliminado` DATETIME NULL,
    `eliminado_por` VARCHAR(30) NULL
);
```

**4. `ot_valorizacion`** - Cálculo de aranceles
```sql
CREATE TABLE `ot_valorizacion` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `id_ot` BIGINT(20) NULL,
    `numero_ot` BIGINT(20) NULL,
    `tipo` VARCHAR(50) NULL,
    `id_detalle` INT(11) NULL,
    `detalle` VARCHAR(150) NULL,
    `base_impuesto` FLOAT NULL,
    `base_impuesto_origen` BIGINT(20) NULL,
    `tipo_moneda` VARCHAR(30) NULL,
    `fecha_vencimiento_impuesto` DATE NULL,
    `monto` BIGINT(20) NULL,
    `mi_valor_ipc` BIGINT(20) NULL,
    `mi_valor_interes` BIGINT(20) NULL,
    `intereses` BIGINT(20) NULL,
    `total` BIGINT(20) NULL,
    `multa` BIGINT(20) NULL,
    `estado` VARCHAR(20) NULL DEFAULT '1',
    -- Campos de auditoría
    `creado` DATETIME NULL,
    `creado_por` VARCHAR(80) NULL,
    `modificado` DATETIME NULL,
    `modificado_por` VARCHAR(80) NULL,
    `eliminado` DATETIME NULL,
    `eliminado_por` VARCHAR(80) NULL
);
```

**5. `ot_comparecientes`** - Personas que comparecen
```sql
CREATE TABLE `ot_comparecientes` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `id_ot` BIGINT(20) NULL,
    `ot` BIGINT(20) NULL,
    `tipo_documento` VARCHAR(50) NULL,
    `nombre` VARCHAR(250) NULL,
    `apellido_materno` VARCHAR(80) NULL,
    `apellido_paterno` VARCHAR(80) NULL,
    `rut` VARCHAR(20) NULL,
    `repertorio` BIGINT(20) NULL,
    `anho` INT(4) NULL,
    `tipo_persona` VARCHAR(30) NULL,
    `es_chileno` VARCHAR(1) NULL,
    `nacionalidad` VARCHAR(100) NULL,
    `codigo_pais` VARCHAR(50) NULL,
    `profesion` VARCHAR(150) NULL,
    `domicilio` VARCHAR(200) NULL,
    `email` VARCHAR(200) NULL,
    `nombre_fantasia` VARCHAR(150) NULL
);
```

### 10.4 Tipos de Documentos Soportados

#### **Escrituras Públicas**
- Compraventa
- Arriendo
- Mutuo
- Mandato
- Donación
- Hipoteca
- Prenda
- Promesa
- Usufructo

#### **Documentos Privados**
- Documento Privado Local
- Documento Privado Online
- Documento Privado Externo
- Notas de Cobro

#### **Otros Documentos**
- Vehículos (STEV)
- Letras
- Instrucciones
- Certificados

### 10.5 Estados y Workflow

#### **Estados de la OT**
1. **Creada**: OT recién ingresada
2. **En Proceso**: En desarrollo por funcionarios
3. **En Repertorio**: Asignada a repertorio
4. **Firmada**: Documento firmado digitalmente
5. **Entregada**: Proceso completado
6. **Anulada**: Cancelada por algún motivo

#### **Workflow de Avances**
- Cada cambio de estado se registra en `ot_avances`
- Incluye alertas y definición de ciclos
- Seguimiento por funcionario responsable
- Observaciones detalladas por etapa

---

**Fecha del Reporte**: 12 de Julio, 2025
**Versión del Sistema**: SIGN+ v2
**Analista**: Augment Agent
