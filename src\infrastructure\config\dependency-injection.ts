// Infrastructure - Dependency Injection Container
import { PrismaClient } from '@prisma/client'

// Repositories
import { PrismaUserRepository } from '../persistence/prisma/user-repository-impl'
import { UserRepository } from '../../domain/user/repositories/user-repository'

// Services
import { LicenseEncryptionService } from '../../domain/license/services/license-encryption-service'

// Use Cases
import { AuthenticateUserUseCase } from '../../application/user/use-cases/authenticate-user'
import { GetUserProfileUseCase } from '../../application/user/use-cases/get-user-profile'
import { ValidateLicenseUseCase } from '../../application/license/use-cases/validate-license'

// Container class for dependency injection
export class DIContainer {
  private static instance: DIContainer
  private prisma: PrismaClient
  private userRepository: UserRepository
  private licenseEncryptionService: LicenseEncryptionService

  private constructor() {
    // Initialize Prisma
    this.prisma = new PrismaClient()

    // Initialize repositories
    this.userRepository = new PrismaUserRepository(this.prisma)

    // Initialize services
    this.licenseEncryptionService = new LicenseEncryptionService(
      process.env.LICENSE_SECRET_KEY || 'default-secret-key'
    )
  }

  public static getInstance(): DIContainer {
    if (!DIContainer.instance) {
      DIContainer.instance = new DIContainer()
    }
    return DIContainer.instance
  }

  // Repository getters
  public getUserRepository(): UserRepository {
    return this.userRepository
  }

  // Service getters
  public getLicenseEncryptionService(): LicenseEncryptionService {
    return this.licenseEncryptionService
  }

  // Use case factories
  public getAuthenticateUserUseCase(): AuthenticateUserUseCase {
    return new AuthenticateUserUseCase(this.userRepository)
  }

  public getGetUserProfileUseCase(): GetUserProfileUseCase {
    return new GetUserProfileUseCase(this.userRepository)
  }

  public getValidateLicenseUseCase(): ValidateLicenseUseCase {
    // Note: License repository would be injected here when implemented
    return new ValidateLicenseUseCase(
      null as any, // TODO: Implement license repository
      this.licenseEncryptionService
    )
  }

  // Cleanup
  public async cleanup(): Promise<void> {
    await this.prisma.$disconnect()
  }
}

// Export singleton instance
export const container = DIContainer.getInstance()
