import { prisma } from './prisma'

interface AuditLogEntry {
  action: string
  details?: string
  ip?: string
  userId?: string
  userAgent?: string
  metadata?: Record<string, any>
}

export async function auditLog(entry: AuditLogEntry) {
  try {
    // En un entorno de producción, esto podría ir a una base de datos separada
    // o a un servicio de logging como CloudWatch, Datadog, etc.
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      action: entry.action,
      details: entry.details || '',
      ip: entry.ip || 'unknown',
      userId: entry.userId || null,
      userAgent: entry.userAgent || 'unknown',
      metadata: entry.metadata || {}
    }

    // Log a consola para desarrollo
    console.log('[AUDIT]', JSON.stringify(logEntry, null, 2))

    // En producción, enviar a servicio de logging
    if (process.env.NODE_ENV === 'production') {
      // Ejemplo: await sendToLoggingService(logEntry)
    }

    // Opcional: Guardar en base de datos para auditorías críticas
    if (entry.action.includes('LOGIN') || entry.action.includes('PERMISSION')) {
      // await prisma.auditLog.create({ data: logEntry })
    }

  } catch (error) {
    console.error('Error logging audit entry:', error)
  }
}
