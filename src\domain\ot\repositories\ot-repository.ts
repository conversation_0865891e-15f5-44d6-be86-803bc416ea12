// Domain - OT - Repository Interface
import { OrdenTrabajo, Priority } from '../entities/orden-trabajo'
import { OTId, UserId } from '../../shared/value-objects/id'
import { OTStatus } from '../value-objects/ot-status'
import { DocumentType } from '../value-objects/document-type'

export interface OTSearchFilters {
  status?: OTStatus[]
  priority?: Priority[]
  documentType?: DocumentType[]
  notarioAsignado?: string
  dateFrom?: Date
  dateTo?: Date
  searchText?: string
}

export interface OTRepository {
  // Queries
  findById(id: OTId): Promise<OrdenTrabajo | null>
  findByNumeroOT(numeroOT: string): Promise<OrdenTrabajo | null>
  findAll(filters?: OTSearchFilters): Promise<OrdenTrabajo[]>
  findByNotario(notarioId: UserId): Promise<OrdenTrabajo[]>
  findActiveOTs(): Promise<OrdenTrabajo[]>
  findOverdueOTs(): Promise<OrdenTrabajo[]>

  // Commands
  save(ot: OrdenTrabajo): Promise<void>
  update(ot: OrdenTrabajo): Promise<void>
  delete(id: OTId): Promise<void>

  // Specific queries
  existsByNumeroOT(numeroOT: string): Promise<boolean>
  countByStatus(status: OTStatus): Promise<number>
  countByNotario(notarioId: UserId): Promise<number>
  getNextOTNumber(): Promise<string>

  // Statistics
  getOTStatistics(): Promise<{
    total: number
    byStatus: Record<OTStatus, number>
    byPriority: Record<Priority, number>
    overdue: number
  }>
}
