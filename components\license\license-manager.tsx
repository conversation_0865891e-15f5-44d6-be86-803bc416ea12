"use client"

import { useState, useEffect } from 'react'
import {
  LicenseData,
  LicenseValidationResponse
} from '@/lib/license'

interface LicenseManagerProps {
  onLicenseValidated?: (license: LicenseData) => void
  onLicenseError?: (error: string) => void
}

export function LicenseManager({ onLicenseValidated, onLicenseError }: LicenseManagerProps) {
  const [license, setLicense] = useState<LicenseData | null>(null)
  const [validation, setValidation] = useState<LicenseValidationResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [licenseKey, setLicenseKey] = useState('')

  // Cargar licencia al montar el componente
  useEffect(() => {
    loadLicense()
  }, [])

  const loadLicense = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/license/validate')
      const validation = await response.json()

      if (validation.license) {
        setLicense(validation.license)
        setValidation(validation)

        if (validation.valid && onLicenseValidated) {
          onLicenseValidated(validation.license)
        } else if (!validation.valid && onLicenseError) {
          onLicenseError(validation.error || 'Licencia inválida')
        }
      }
    } catch (error) {
      console.error('Error al cargar licencia:', error)
      if (onLicenseError) {
        onLicenseError('Error al cargar la licencia')
      }
    } finally {
      setLoading(false)
    }
  }

  const validateLicense = async () => {
    if (!licenseKey.trim()) {
      alert('Por favor ingrese una clave de licencia')
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/license/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ licenseKey }),
      })

      const validation = await response.json()
      setValidation(validation)

      if (validation.valid && validation.license) {
        setLicense(validation.license)

        if (onLicenseValidated) {
          onLicenseValidated(validation.license)
        }

        alert('Licencia validada y guardada correctamente')
      } else {
        if (onLicenseError) {
          onLicenseError(validation.error || 'Licencia inválida')
        }
        alert('Error: ' + (validation.error || 'Licencia inválida'))
      }
    } catch (error) {
      console.error('Error al validar licencia:', error)
      alert('Error al validar la licencia')
    } finally {
      setLoading(false)
    }
  }

  const generateTestLicense = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/license/generate-sample', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientName: 'Notaría de Prueba',
          clientEmail: '<EMAIL>'
        }),
      })

      const result = await response.json()

      if (result.success && result.license) {
        setLicense(result.license)

        if (onLicenseValidated) {
          onLicenseValidated(result.license)
        }

        alert('Licencia de prueba generada correctamente')
      } else {
        alert('Error al generar licencia de prueba')
      }
    } catch (error) {
      console.error('Error al generar licencia de prueba:', error)
      alert('Error al generar licencia de prueba')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-CL')
  }

  const getLicenseStatusColor = () => {
    if (!validation) return '#6b7280'
    if (validation.valid) {
      if (validation.remainingDays && validation.remainingDays <= 30) {
        return '#f59e0b' // Amarillo para advertencia
      }
      return '#10b981' // Verde para válida
    }
    return '#ef4444' // Rojo para inválida
  }

  return (
    <div style={{ 
      backgroundColor: 'white', 
      borderRadius: '8px',
      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      border: '1px solid #d1d5db',
      padding: '24px',
      maxWidth: '600px',
      margin: '0 auto'
    }}>
      <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>
        Gestión de Licencias e-NOTARIA
      </h2>

      {/* Estado actual de la licencia */}
      {license && (
        <div style={{ 
          backgroundColor: '#f9fafb', 
          padding: '16px', 
          borderRadius: '6px',
          marginBottom: '16px',
          border: `2px solid ${getLicenseStatusColor()}`
        }}>
          <h3 style={{ fontSize: '16px', fontWeight: '500', color: '#374151', marginBottom: '8px' }}>
            Estado de la Licencia
          </h3>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', fontSize: '14px' }}>
            <div>
              <strong>Cliente:</strong> {license.clientName}
            </div>
            <div>
              <strong>Tipo:</strong> {license.licenseType}
            </div>
            <div>
              <strong>Emitida:</strong> {formatDate(license.issuedAt)}
            </div>
            <div>
              <strong>Expira:</strong> {formatDate(license.expiresAt)}
            </div>
            <div>
              <strong>Usuarios máx:</strong> {license.features.maxUsers}
            </div>
            <div>
              <strong>Documentos máx:</strong> {license.features.maxDocuments}
            </div>
          </div>

          {validation && (
            <div style={{ marginTop: '12px' }}>
              <div style={{ 
                color: getLicenseStatusColor(), 
                fontWeight: '500',
                fontSize: '14px'
              }}>
                {validation.valid ? '✅ Licencia Válida' : '❌ Licencia Inválida'}
                {validation.remainingDays && ` (${validation.remainingDays} días restantes)`}
              </div>
              
              {validation.warnings && validation.warnings.length > 0 && (
                <div style={{ marginTop: '8px' }}>
                  {validation.warnings.map((warning, index) => (
                    <div key={index} style={{ color: '#f59e0b', fontSize: '12px' }}>
                      ⚠️ {warning}
                    </div>
                  ))}
                </div>
              )}
              
              {validation.error && (
                <div style={{ color: '#ef4444', fontSize: '12px', marginTop: '4px' }}>
                  Error: {validation.error}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Formulario para nueva licencia */}
      <div style={{ marginBottom: '16px' }}>
        <label style={{ 
          display: 'block', 
          fontSize: '14px', 
          fontWeight: '500', 
          color: '#374151',
          marginBottom: '4px'
        }}>
          Clave de Licencia
        </label>
        <input
          type="text"
          value={licenseKey}
          onChange={(e) => setLicenseKey(e.target.value)}
          placeholder="Ingrese su clave de licencia"
          style={{
            width: '100%',
            padding: '8px 12px',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            fontSize: '14px',
            marginBottom: '8px'
          }}
        />
        
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={validateLicense}
            disabled={loading}
            style={{
              backgroundColor: '#0ea5e9',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '6px',
              border: 'none',
              fontSize: '14px',
              fontWeight: '500',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Validando...' : 'Validar Licencia'}
          </button>
          
          <button
            onClick={generateTestLicense}
            disabled={loading}
            style={{
              backgroundColor: '#6b7280',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '6px',
              border: 'none',
              fontSize: '14px',
              fontWeight: '500',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1
            }}
          >
            Generar Licencia de Prueba
          </button>
        </div>
      </div>

      {/* Información adicional */}
      <div style={{ 
        backgroundColor: '#eff6ff', 
        padding: '12px', 
        borderRadius: '6px',
        fontSize: '12px',
        color: '#1e40af'
      }}>
        <strong>Información:</strong>
        <ul style={{ marginTop: '4px', paddingLeft: '16px' }}>
          <li>Las licencias se validan automáticamente cada 24 horas</li>
          <li>La licencia se almacena cifrada localmente</li>
          <li>Se requiere conexión a internet para la validación inicial</li>
          <li>Las licencias de prueba son válidas por 1 año</li>
        </ul>
      </div>
    </div>
  )
}
