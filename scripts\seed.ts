import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Iniciando seed de la base de datos...')

  // Crear roles básicos
  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: 'Administrador del sistema'
    }
  })

  const userRole = await prisma.role.upsert({
    where: { name: 'user' },
    update: {},
    create: {
      name: 'user',
      description: 'Usuario básico del sistema'
    }
  })

  const repertoristaRole = await prisma.role.upsert({
    where: { name: 'repertorista' },
    update: {},
    create: {
      name: 'repertorista',
      description: 'Repertorista'
    }
  })

  // Crear permisos básicos
  const permissions = [
    { name: 'user_management', description: 'Gestión de usuarios', module: 'usuarios' },
    { name: 'ot_access', description: 'Acceso a órdenes de trabajo', module: 'ot' },
    { name: 'ot_create', description: 'Crear órdenes de trabajo', module: 'ot' },
    { name: 'ot_edit', description: 'Editar órdenes de trabajo', module: 'ot' },
    { name: 'documento_privado_access', description: 'Acceso a documentos privados', module: 'documentos' },
    { name: 'escritura_publica_access', description: 'Acceso a escrituras públicas', module: 'documentos' },
    { name: 'repertorio_access', description: 'Acceso al repertorio', module: 'repertorio' },
    { name: 'caja_access', description: 'Acceso a caja', module: 'caja' },
    { name: 'reportes_access', description: 'Acceso a reportes', module: 'reportes' }
  ]

  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { name: permission.name },
      update: {},
      create: permission
    })
  }

  // Asignar todos los permisos al rol admin
  const allPermissions = await prisma.permission.findMany()
  for (const permission of allPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id
        }
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id
      }
    })
  }

  // Asignar permisos básicos al rol user
  const basicPermissions = ['ot_access', 'documento_privado_access', 'escritura_publica_access']
  for (const permissionName of basicPermissions) {
    const permission = await prisma.permission.findUnique({
      where: { name: permissionName }
    })
    if (permission) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: userRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: userRole.id,
          permissionId: permission.id
        }
      })
    }
  }

  // Crear usuario administrador
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const adminUser = await prisma.user.upsert({
    where: { username: 'ADMIN' },
    update: {},
    create: {
      username: 'ADMIN',
      email: '<EMAIL>',
      password: hashedPassword,
      nombreSocial: 'Administrador del Sistema',
      estado: true
    }
  })

  // Asignar rol admin al usuario admin
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id
    }
  })

  // Crear usuario de prueba
  const testPassword = await bcrypt.hash('test123', 12)
  const testUser = await prisma.user.upsert({
    where: { username: 'TESTUSER' },
    update: {},
    create: {
      username: 'TESTUSER',
      email: '<EMAIL>',
      password: testPassword,
      nombreSocial: 'Usuario de Prueba',
      estado: true
    }
  })

  // Asignar rol user al usuario de prueba
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: testUser.id,
        roleId: userRole.id
      }
    },
    update: {},
    create: {
      userId: testUser.id,
      roleId: userRole.id
    }
  })

  console.log('✅ Seed completado exitosamente!')
  console.log('👤 Usuario admin creado: ADMIN / admin123')
  console.log('👤 Usuario test creado: TESTUSER / test123')
  console.log('🏢 Sistema: e-NOTARIA v1.0.0')
  console.log('🔗 Acceso: http://localhost:3000')
}

main()
  .catch((e) => {
    console.error('❌ Error durante el seed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
