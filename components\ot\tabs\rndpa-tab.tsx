"use client"

import { useState } from "react"

interface RNDPATabProps {
  otId?: string
  numeroOT?: string
  comparecientes: Array<{
    id: string
    rut: string
    nombres: string
    apellidoPaterno: string
    apellidoMaterno: string
    calidad: string
    esChileno: boolean
  }>
}

interface RNDPA {
  id: string
  rut: string
  nombreCompleto: string
  fecha: string
  folio: number
  codigoVerificacion: string
  usuarioOficio: string
  estado: 'PENDIENTE' | 'ENVIADO' | 'CONFIRMADO'
}

export function RNDPATab({ otId, numeroOT, comparecientes }: RNDPATabProps) {
  const [rndpaList, setRndpaList] = useState<RNDPA[]>([])
  const [selectedRuts, setSelectedRuts] = useState<string[]>([])

  if (!otId) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">📋</div>
        <p className="text-gray-600">Debe crear la OT primero para acceder a RNDPA</p>
      </div>
    )
  }

  const generateRNDPA = () => {
    const newRNDPAs = selectedRuts.map(rut => {
      const compareciente = comparecientes.find(c => c.rut === rut)
      if (!compareciente) return null

      return {
        id: Date.now().toString() + rut,
        rut: compareciente.rut,
        nombreCompleto: `${compareciente.nombres} ${compareciente.apellidoPaterno} ${compareciente.apellidoMaterno || ''}`.trim(),
        fecha: new Date().toISOString().split('T')[0],
        folio: Math.floor(Math.random() * 999999) + 100000,
        codigoVerificacion: Math.random().toString(36).substring(2, 15).toUpperCase(),
        usuarioOficio: 'NOTARIO_ACTUAL', // TODO: Get from session
        estado: 'PENDIENTE' as const
      }
    }).filter(Boolean) as RNDPA[]

    setRndpaList([...rndpaList, ...newRNDPAs])
    setSelectedRuts([])
  }

  const updateEstado = (id: string, estado: RNDPA['estado']) => {
    setRndpaList(rndpaList.map(r => 
      r.id === id ? { ...r, estado } : r
    ))
  }

  const deleteRNDPA = (id: string) => {
    setRndpaList(rndpaList.filter(r => r.id !== id))
  }

  const availableComparecientes = comparecientes.filter(c => 
    c.esChileno && !rndpaList.some(r => r.rut === c.rut)
  )

  return (
    <div className="space-y-6">
      <div className="bg-teal-600 text-white p-4 rounded-t-lg">
        <h3 className="text-lg font-medium">
          RNDPA - OT: {numeroOT}
        </h3>
        <p className="text-sm opacity-90">
          Registro Nacional de Deudores de Pensiones de Alimentos
        </p>
      </div>

      <div className="bg-teal-50 p-6 rounded-b-lg border border-teal-200">
        {/* Generate RNDPA Section */}
        {availableComparecientes.length > 0 && (
          <div className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">
              Generar Consultas RNDPA
            </h4>
            
            <div className="space-y-3 mb-4">
              {availableComparecientes.map((comp) => (
                <label key={comp.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedRuts.includes(comp.rut)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedRuts([...selectedRuts, comp.rut])
                      } else {
                        setSelectedRuts(selectedRuts.filter(r => r !== comp.rut))
                      }
                    }}
                    className="mr-3"
                  />
                  <div>
                    <p className="font-medium text-gray-900">
                      {comp.nombres} {comp.apellidoPaterno} {comp.apellidoMaterno}
                    </p>
                    <p className="text-sm text-gray-600">
                      RUT: {comp.rut} - {comp.calidad}
                    </p>
                  </div>
                </label>
              ))}
            </div>

            <button
              onClick={generateRNDPA}
              disabled={selectedRuts.length === 0}
              className="bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              📋 Generar RNDPA ({selectedRuts.length})
            </button>
          </div>
        )}

        {/* RNDPA List */}
        {rndpaList.length > 0 ? (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-900">
                Consultas RNDPA Generadas
              </h4>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Compareciente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      RUT
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Folio
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Código
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {rndpaList.map((rndpa) => (
                    <tr key={rndpa.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {rndpa.nombreCompleto}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {rndpa.rut}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(rndpa.fecha).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {rndpa.folio}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                        {rndpa.codigoVerificacion}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={rndpa.estado}
                          onChange={(e) => updateEstado(rndpa.id, e.target.value as RNDPA['estado'])}
                          className={`text-xs font-medium rounded-full px-2 py-1 ${
                            rndpa.estado === 'PENDIENTE' ? 'bg-yellow-100 text-yellow-800' :
                            rndpa.estado === 'ENVIADO' ? 'bg-blue-100 text-blue-800' :
                            'bg-green-100 text-green-800'
                          }`}
                        >
                          <option value="PENDIENTE">Pendiente</option>
                          <option value="ENVIADO">Enviado</option>
                          <option value="CONFIRMADO">Confirmado</option>
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex space-x-2">
                          <button
                            className="text-blue-600 hover:text-blue-800"
                            title="Imprimir"
                          >
                            🖨️
                          </button>
                          <button
                            onClick={() => deleteRNDPA(rndpa.id)}
                            className="text-red-600 hover:text-red-800"
                            title="Eliminar"
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">📋</div>
            <p>No hay consultas RNDPA generadas</p>
            <p className="text-sm">
              {availableComparecientes.length > 0 
                ? 'Seleccione comparecientes para generar consultas'
                : 'No hay comparecientes chilenos disponibles'
              }
            </p>
          </div>
        )}

        {/* Information Panel */}
        <div className="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h4 className="text-sm font-medium text-blue-900 mb-2">
            ℹ️ Información sobre RNDPA
          </h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Solo se puede consultar RUT de personas chilenas</li>
            <li>• La consulta es obligatoria para ciertos actos notariales</li>
            <li>• El certificado tiene validez de 60 días</li>
            <li>• Debe conservarse como respaldo del acto notarial</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
