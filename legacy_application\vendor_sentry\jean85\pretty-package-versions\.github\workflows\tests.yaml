name: Tests

on:
  pull_request: null
  push:
    branches:
      - 2.x

jobs:
  tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php:
          - '7.1'
          - '7.2'
          - '7.3'
          - '7.4'
          - '8.0'
          - '8.1'
        composer_version: ['v2']
        include:
          - description: '(prefer lowest)'
            php: '7.1'
            composer_version: '2.0.0'
            dependencies: 'lowest'

    name: PHP ${{ matrix.php }} tests ${{ matrix.description }}
    steps:
      # checkout git
      - uses: actions/checkout@v2
      # setup PHP
      - uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:${{ matrix.composer_version }}
          coverage: xdebug
      - uses: "ramsey/composer-install@v1"
        with:
          dependency-versions: ${{ matrix.dependencies }}
      - run: vendor/bin/phpunit --coverage-clover=coverage.xml
      - uses: codecov/codecov-action@v1
        with:
          file: './coverage.xml'
          fail_ci_if_error: true
  PHP-CS-Fixer:
    runs-on: ubuntu-latest
    name: Code style
    steps:
      - uses: actions/checkout@v2
      - uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
          coverage: none
      - uses: "ramsey/composer-install@v1"
      - run: vendor/bin/php-cs-fixer fix --ansi --verbose --dry-run
  PHPStan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
          coverage: none
      - uses: "ramsey/composer-install@v1"
      - run: vendor/bin/phpstan analyse
  Psalm:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: shivammathur/setup-php@v2
        with:
          php-version: '7.4'
          coverage: none
      - uses: "ramsey/composer-install@v1"
      - run: vendor/bin/psalm
