"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, ReactNode } from "react"
import { getAppName, getCompanyName } from "@/lib/config"
import { UserMenuInline } from "@/components/layout/user-menu"

interface PageLayoutProps {
  children: ReactNode
  title: string
  currentPage?: string
}

export function PageLayout({ children, title, currentPage }: PageLayoutProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f9fafb'
      }}>
        <div style={{ 
          width: '48px', 
          height: '48px', 
          border: '2px solid #0ea5e9',
          borderTop: '2px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const isCurrentPage = (page: string) => currentPage === page

  return (
    <div style={{ height: '100vh', display: 'flex', backgroundColor: '#f3f4f6' }}>
      {/* Sidebar */}
      <div style={{ 
        width: '256px', 
        backgroundColor: 'white', 
        borderRight: '1px solid #d1d5db',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header del sidebar */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          padding: '16px', 
          borderBottom: '1px solid #d1d5db' 
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div style={{ 
              width: '32px', 
              height: '32px', 
              backgroundColor: '#0ea5e9', 
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <span style={{ color: 'white', fontWeight: 'bold', fontSize: '14px' }}>e</span>
            </div>
            <span style={{ fontSize: '20px', fontWeight: 'bold', color: '#374151' }}>
              {getAppName()}
            </span>
          </div>
        </div>

        {/* Navegación */}
        <nav style={{ flex: 1, padding: '16px 12px', display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <a
            href="/dashboard"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: isCurrentPage('dashboard') ? '#dbeafe' : 'transparent',
              color: isCurrentPage('dashboard') ? '#1e40af' : '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>🏠</span>
            <span>Inicio</span>
          </a>
          
          <a
            href="/ot"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: isCurrentPage('ot') ? '#dbeafe' : 'transparent',
              color: isCurrentPage('ot') ? '#1e40af' : '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>📋</span>
            <span>Órdenes de Trabajo</span>
          </a>
          
          <a
            href="/documentos"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: isCurrentPage('documentos') ? '#dbeafe' : 'transparent',
              color: isCurrentPage('documentos') ? '#1e40af' : '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>📄</span>
            <span>Documentos</span>
          </a>
          
          <a
            href="/usuarios"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: isCurrentPage('usuarios') ? '#dbeafe' : 'transparent',
              color: isCurrentPage('usuarios') ? '#1e40af' : '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>👥</span>
            <span>Usuarios</span>
          </a>
          
          <a
            href="/reportes"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: isCurrentPage('reportes') ? '#dbeafe' : 'transparent',
              color: isCurrentPage('reportes') ? '#1e40af' : '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>📊</span>
            <span>Reportes</span>
          </a>
          
          <a
            href="/caja"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: isCurrentPage('caja') ? '#dbeafe' : 'transparent',
              color: isCurrentPage('caja') ? '#1e40af' : '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>💰</span>
            <span>Caja</span>
          </a>
          
          <a
            href="/licencias"
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              padding: '8px 12px', 
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: isCurrentPage('licencias') ? '#dbeafe' : 'transparent',
              color: isCurrentPage('licencias') ? '#1e40af' : '#4b5563',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>🔐</span>
            <span>Licencias</span>
          </a>
        </nav>

        {/* Footer del sidebar */}
        <div style={{ padding: '16px', borderTop: '1px solid #d1d5db' }}>
          <div style={{ fontSize: '12px', color: '#6b7280', textAlign: 'center' }}>
            {getAppName()} v1.0.0
          </div>
        </div>
      </div>

      {/* Área de contenido principal */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', minWidth: 0 }}>
        {/* Navbar */}
        <header style={{ 
          backgroundColor: 'white', 
          borderBottom: '1px solid #d1d5db',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ padding: '0 16px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              height: '64px' 
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ fontSize: '18px', fontWeight: '600', color: '#374151' }}>
                    {title}
                  </span>
                  <span style={{ fontSize: '14px', color: '#6b7280' }}>
                    - {getCompanyName()}
                  </span>
                </div>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <UserMenuInline />
              </div>
            </div>
          </div>
        </header>

        {/* Contenido principal */}
        <main style={{ flex: 1, overflow: 'auto' }}>
          {children}
        </main>
      </div>

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
