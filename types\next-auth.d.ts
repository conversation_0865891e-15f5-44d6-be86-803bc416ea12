import NextAuth from "next-auth"

interface Permission {
  name: string
  module?: string
  description?: string
}

interface Role {
  id: string
  name: string
  description?: string
}

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      username: string
      email?: string | null
      nombreSocial?: string | null
      permissions: Permission[]
      roles: Role[]
      lastLogin?: Date | null
    }
  }

  interface User {
    id: string
    username: string
    email?: string | null
    nombreSocial?: string | null
    permissions: Permission[]
    roles: Role[]
    lastLogin?: Date | null
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    username: string
    nombreSocial?: string | null
    permissions: Permission[]
    roles: Role[]
    lastLogin?: Date | null
  }
}
