"use client"

import { useSession } from "next-auth/react"
import { getAppName, getCompanyName } from "@/lib/config"

export default function DashboardPage() {
  const { data: session } = useSession()

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ padding: '24px' }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
          {/* Header de página */}
          <div style={{
            backgroundColor: 'white',
            borderBottom: '1px solid #d1d5db',
            padding: '24px',
            marginBottom: '24px',
            borderRadius: '8px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
          }}>
            <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827' }}>
              Bienvenido, {session?.user?.nombreSocial || session?.user?.username || 'Usuario'}
            </h1>
            <p style={{ marginTop: '4px', fontSize: '14px', color: '#4b5563' }}>
              Panel de control - {getCompanyName()}
            </p>
          </div>

          {/* Contenido */}
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            border: '1px solid #d1d5db',
            padding: '24px'
          }}>
            <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>
              Dashboard e-NOTARIA
            </h2>
            <p style={{ color: '#4b5563', marginBottom: '16px' }}>
              Bienvenido al sistema e-NOTARIA. Si puedes ver el sidebar a la izquierda y el navbar arriba, el layout está funcionando correctamente.
            </p>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '16px'
            }}>
              <div style={{
                backgroundColor: '#eff6ff',
                padding: '16px',
                borderRadius: '8px',
                border: '1px solid #bfdbfe'
              }}>
                <h3 style={{ fontWeight: '500', color: '#1e3a8a', marginBottom: '8px' }}>
                  Información del Usuario
                </h3>
                <p style={{ fontSize: '14px', color: '#1e40af' }}>Usuario: {session?.user?.username}</p>
                <p style={{ fontSize: '14px', color: '#1e40af' }}>Nombre: {session?.user?.nombreSocial || 'No definido'}</p>
                <p style={{ fontSize: '14px', color: '#1e40af' }}>Email: {session?.user?.email || 'No definido'}</p>
              </div>

              <div style={{
                backgroundColor: '#f0fdf4',
                padding: '16px',
                borderRadius: '8px',
                border: '1px solid #bbf7d0'
              }}>
                <h3 style={{ fontWeight: '500', color: '#14532d', marginBottom: '8px' }}>
                  Información del Sistema
                </h3>
                <p style={{ fontSize: '14px', color: '#15803d' }}>Sistema: {getAppName()}</p>
                <p style={{ fontSize: '14px', color: '#15803d' }}>Empresa: {getCompanyName()}</p>
                <p style={{ fontSize: '14px', color: '#15803d' }}>Versión: 1.0.0</p>
              </div>
            </div>

            <div style={{
              marginTop: '24px',
              padding: '16px',
              backgroundColor: '#fefce8',
              border: '1px solid #fde047',
              borderRadius: '8px'
            }}>
              <h3 style={{ fontWeight: '500', color: '#a16207', marginBottom: '8px' }}>
                Estado del Layout
              </h3>
              <p style={{ fontSize: '14px', color: '#a16207' }}>
                ✅ Layout con sidebar y navbar implementado
              </p>
              <p style={{ fontSize: '14px', color: '#a16207', marginTop: '4px' }}>
                ✅ Branding e-NOTARIA aplicado
              </p>
              <p style={{ fontSize: '14px', color: '#a16207', marginTop: '4px' }}>
                ✅ Configuración centralizada de empresa
              </p>
              <p style={{ fontSize: '14px', color: '#a16207', marginTop: '4px' }}>
                ✅ Menú de usuario con logout funcional
              </p>
              <p style={{ fontSize: '14px', color: '#a16207', marginTop: '4px' }}>
                ✅ Sistema de licencias cifradas implementado
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

