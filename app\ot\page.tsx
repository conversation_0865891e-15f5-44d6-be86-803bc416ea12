"use client"

import { UnifiedLayout } from "@/components/layout/unified-layout"

export default function OTPage() {
  // Datos de ejemplo
  const otList = [
    {
      id: "OT-2024-001",
      cliente: "<PERSON>",
      tipo: "Escritura de Compraventa",
      estado: "En Proceso",
      fecha: "2024-01-15",
      notario: "<PERSON> Silva",
      prioridad: "Alta"
    },
    {
      id: "OT-2024-002", 
      cliente: "Empresa ABC Ltda.",
      tipo: "Constitución de Sociedad",
      estado: "Pendiente",
      fecha: "2024-01-14",
      notario: "<PERSON>",
      prioridad: "Media"
    },
    {
      id: "OT-2024-003",
      cliente: "<PERSON>",
      tipo: "Testamento",
      estado: "Completada",
      fecha: "2024-01-13",
      notario: "María Silva",
      prioridad: "Baja"
    }
  ]

  const getEstadoColorStyle = (estado: string) => {
    switch (estado) {
      case "Completada":
        return { backgroundColor: '#dcfce7', color: '#166534' }
      case "En Proceso":
        return { backgroundColor: '#dbeafe', color: '#1e40af' }
      case "Pendiente":
        return { backgroundColor: '#fef3c7', color: '#92400e' }
      default:
        return { backgroundColor: '#f3f4f6', color: '#374151' }
    }
  }

  const getPrioridadColorStyle = (prioridad: string) => {
    switch (prioridad) {
      case "Alta":
        return { backgroundColor: '#fecaca', color: '#991b1b' }
      case "Media":
        return { backgroundColor: '#fef3c7', color: '#92400e' }
      case "Baja":
        return { backgroundColor: '#dcfce7', color: '#166534' }
      default:
        return { backgroundColor: '#f3f4f6', color: '#374151' }
    }
  }

  return (
    <UnifiedLayout>
      <div style={{ padding: '24px' }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
          {/* Header de página */}
          <div style={{ 
            backgroundColor: 'white', 
            borderBottom: '1px solid #d1d5db',
            padding: '24px',
            marginBottom: '24px',
            borderRadius: '8px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827' }}>
                Órdenes de Trabajo
              </h1>
              <p style={{ marginTop: '4px', fontSize: '14px', color: '#4b5563' }}>
                Gestión y seguimiento de órdenes de trabajo
              </p>
            </div>
            <a
              href="/ot/crear"
              style={{
                backgroundColor: '#0ea5e9',
                color: 'white',
                padding: '8px 16px',
                borderRadius: '6px',
                border: 'none',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                textDecoration: 'none'
              }}
            >
              <span>➕</span>
              Nueva OT
            </a>
          </div>

          {/* Filtros y búsqueda */}
          <div style={{ 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            border: '1px solid #d1d5db',
            padding: '24px',
            marginBottom: '24px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <div style={{ position: 'relative' }}>
                  <span style={{ 
                    position: 'absolute', 
                    left: '12px', 
                    top: '50%', 
                    transform: 'translateY(-50%)',
                    color: '#9ca3af',
                    fontSize: '14px'
                  }}>🔍</span>
                  <input
                    type="text"
                    placeholder="Buscar por cliente, ID o tipo..."
                    style={{
                      width: '100%',
                      paddingLeft: '40px',
                      paddingRight: '16px',
                      paddingTop: '8px',
                      paddingBottom: '8px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px'
                    }}
                  />
                </div>
              </div>
              
              <div style={{ display: 'flex', gap: '8px' }}>
                <select style={{
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}>
                  <option value="">Todos los estados</option>
                  <option value="pendiente">Pendiente</option>
                  <option value="proceso">En Proceso</option>
                  <option value="completada">Completada</option>
                </select>
                
                <select style={{
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px'
                }}>
                  <option value="">Todas las prioridades</option>
                  <option value="alta">Alta</option>
                  <option value="media">Media</option>
                  <option value="baja">Baja</option>
                </select>
              </div>
            </div>
          </div>

          {/* Lista de OT */}
          <div style={{ 
            backgroundColor: 'white', 
            borderRadius: '8px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            border: '1px solid #d1d5db',
            overflow: 'hidden'
          }}>
            <div style={{ 
              padding: '16px 24px', 
              borderBottom: '1px solid #e5e7eb' 
            }}>
              <h3 style={{ 
                fontSize: '18px', 
                fontWeight: '500', 
                color: '#111827' 
              }}>
                Lista de Órdenes de Trabajo ({otList.length})
              </h3>
            </div>
            
            <div style={{ overflowX: 'auto' }}>
              <table style={{ minWidth: '100%', borderCollapse: 'collapse' }}>
                <thead style={{ backgroundColor: '#f9fafb' }}>
                  <tr>
                    <th style={{ padding: '12px 24px', textAlign: 'left', fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      ID / Cliente
                    </th>
                    <th style={{ padding: '12px 24px', textAlign: 'left', fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Tipo
                    </th>
                    <th style={{ padding: '12px 24px', textAlign: 'left', fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Estado
                    </th>
                    <th style={{ padding: '12px 24px', textAlign: 'left', fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Prioridad
                    </th>
                    <th style={{ padding: '12px 24px', textAlign: 'left', fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Notario
                    </th>
                    <th style={{ padding: '12px 24px', textAlign: 'left', fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Fecha
                    </th>
                    <th style={{ padding: '12px 24px', textAlign: 'right', fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody style={{ backgroundColor: 'white' }}>
                  {otList.map((ot) => (
                    <tr key={ot.id} style={{ borderTop: '1px solid #e5e7eb' }}>
                      <td style={{ padding: '16px 24px', whiteSpace: 'nowrap' }}>
                        <div>
                          <div style={{ fontSize: '14px', fontWeight: '500', color: '#111827' }}>{ot.id}</div>
                          <div style={{ fontSize: '14px', color: '#6b7280' }}>{ot.cliente}</div>
                        </div>
                      </td>
                      <td style={{ padding: '16px 24px', whiteSpace: 'nowrap' }}>
                        <div style={{ fontSize: '14px', color: '#111827' }}>{ot.tipo}</div>
                      </td>
                      <td style={{ padding: '16px 24px', whiteSpace: 'nowrap' }}>
                        <span style={{ 
                          display: 'inline-flex', 
                          padding: '4px 8px', 
                          fontSize: '12px', 
                          fontWeight: '600', 
                          borderRadius: '9999px',
                          ...getEstadoColorStyle(ot.estado)
                        }}>
                          {ot.estado}
                        </span>
                      </td>
                      <td style={{ padding: '16px 24px', whiteSpace: 'nowrap' }}>
                        <span style={{ 
                          display: 'inline-flex', 
                          padding: '4px 8px', 
                          fontSize: '12px', 
                          fontWeight: '600', 
                          borderRadius: '9999px',
                          ...getPrioridadColorStyle(ot.prioridad)
                        }}>
                          {ot.prioridad}
                        </span>
                      </td>
                      <td style={{ padding: '16px 24px', whiteSpace: 'nowrap', fontSize: '14px', color: '#111827' }}>
                        {ot.notario}
                      </td>
                      <td style={{ padding: '16px 24px', whiteSpace: 'nowrap', fontSize: '14px', color: '#6b7280' }}>
                        {new Date(ot.fecha).toLocaleDateString('es-CL')}
                      </td>
                      <td style={{ padding: '16px 24px', whiteSpace: 'nowrap', textAlign: 'right', fontSize: '14px', fontWeight: '500' }}>
                        <button style={{ color: '#0ea5e9', marginRight: '12px', background: 'none', border: 'none', cursor: 'pointer' }}>
                          Ver
                        </button>
                        <button style={{ color: '#6366f1', marginRight: '12px', background: 'none', border: 'none', cursor: 'pointer' }}>
                          Editar
                        </button>
                        <button style={{ color: '#ef4444', background: 'none', border: 'none', cursor: 'pointer' }}>
                          Eliminar
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* Paginación */}
            <div style={{ 
              backgroundColor: 'white', 
              padding: '12px 24px', 
              borderTop: '1px solid #e5e7eb',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ fontSize: '14px', color: '#374151' }}>
                Mostrando <span style={{ fontWeight: '500' }}>1</span> a <span style={{ fontWeight: '500' }}>3</span> de{' '}
                <span style={{ fontWeight: '500' }}>3</span> resultados
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button style={{ 
                  padding: '4px 12px', 
                  border: '1px solid #d1d5db', 
                  borderRadius: '6px',
                  fontSize: '14px',
                  color: '#6b7280',
                  backgroundColor: 'white',
                  cursor: 'not-allowed',
                  opacity: 0.5
                }} disabled>
                  Anterior
                </button>
                <button style={{ 
                  padding: '4px 12px', 
                  border: '1px solid #d1d5db', 
                  borderRadius: '6px',
                  fontSize: '14px',
                  color: '#6b7280',
                  backgroundColor: 'white',
                  cursor: 'not-allowed',
                  opacity: 0.5
                }} disabled>
                  Siguiente
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  )
}
