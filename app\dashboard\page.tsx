// Server Side Dashboard Page - NextJS 15
import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { UnifiedLayout } from "@/components/layout/unified-layout"
import { getAppName, getCompanyName } from "@/lib/config"

// Server Component - No "use client"
export default async function DashboardPage() {
  // Server-side authentication check
  const session = await getServerSession(authOptions)
  
  if (!session) {
    redirect("/auth/login")
  }

  // Server-side data fetching
  const dashboardData = await getDashboardData()

  return (
    <UnifiedLayout>
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Bienvenido, {session.user.nombreSocial || session.user.username}
            </h1>
            <p className="text-gray-600 mt-2">
              Panel de control - {getCompanyName()}
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <DashboardStats data={dashboardData.stats} />
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recent Activity */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Actividad Reciente
                </h3>
                <div className="space-y-4">
                  {dashboardData.recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 text-sm">📋</span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900">{activity.description}</p>
                        <p className="text-xs text-gray-500">
                          {activity.user} - {activity.timestamp.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* System Info */}
            <div>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Información del Sistema
                </h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">Versión</p>
                    <p className="text-sm font-medium text-gray-900">{dashboardData.systemInfo.version}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Tiempo activo</p>
                    <p className="text-sm font-medium text-gray-900">{dashboardData.systemInfo.uptime}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Último respaldo</p>
                    <p className="text-sm font-medium text-gray-900">
                      {dashboardData.systemInfo.lastBackup.toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>

              {/* License Info */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Información de Licencia
                </h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">Tipo</p>
                    <p className="text-sm font-medium text-gray-900">{dashboardData.licenseInfo.type}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Estado</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      dashboardData.licenseInfo.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {dashboardData.licenseInfo.isActive ? 'Activa' : 'Inactiva'}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Expira</p>
                    <p className="text-sm font-medium text-gray-900">
                      {dashboardData.licenseInfo.expiresAt.toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  )
}

// Server-side data fetching function
async function getDashboardData() {
  // This would use your use cases/repositories
  // For now, returning mock data
  return {
    stats: {
      totalOT: 156,
      pendingOT: 23,
      completedOT: 133,
      totalDocuments: 1247
    },
    recentActivities: [
      {
        id: '1',
        type: 'ot_created',
        description: 'Nueva OT creada: Escritura de Compraventa',
        timestamp: new Date(),
        user: 'María Silva'
      },
      {
        id: '2',
        type: 'document_signed',
        description: 'Documento firmado digitalmente',
        timestamp: new Date(Date.now() - 3600000),
        user: 'Carlos Rodríguez'
      },
      {
        id: '3',
        type: 'ot_completed',
        description: 'OT completada: Testamento',
        timestamp: new Date(Date.now() - 7200000),
        user: 'Ana Torres'
      }
    ],
    systemInfo: {
      version: '1.0.0',
      uptime: '15 días',
      lastBackup: new Date(Date.now() - 86400000)
    },
    licenseInfo: {
      type: 'STANDARD',
      expiresAt: new Date(Date.now() + 30 * 86400000),
      isActive: true
    }
  }
}

// Dashboard Stats Component
function DashboardStats({ data }: { data: any }) {
  const stats = [
    {
      name: 'Total OT',
      value: data.totalOT,
      icon: '📋',
      color: 'bg-blue-500'
    },
    {
      name: 'OT Pendientes',
      value: data.pendingOT,
      icon: '⏳',
      color: 'bg-yellow-500'
    },
    {
      name: 'OT Completadas',
      value: data.completedOT,
      icon: '✅',
      color: 'bg-green-500'
    },
    {
      name: 'Documentos',
      value: data.totalDocuments,
      icon: '📄',
      color: 'bg-purple-500'
    }
  ]

  return (
    <>
      {stats.map((stat) => (
        <div
          key={stat.name}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center">
            <div className={`${stat.color} rounded-lg p-3 mr-4`}>
              <span className="text-white text-xl">{stat.icon}</span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.name}</p>
              <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
            </div>
          </div>
        </div>
      ))}
    </>
  )
}
