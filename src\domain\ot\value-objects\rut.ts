// Domain - OT - Value Objects
export class RUT {
  private readonly value: string
  private readonly isChilean: boolean

  constructor(value: string, isChilean: boolean = true) {
    this.isChilean = isChilean
    
    if (isChilean) {
      if (!this.isValidChileanRUT(value)) {
        throw new Error('Invalid Chilean RUT format')
      }
      this.value = this.cleanRUT(value)
    } else {
      if (!value || value.trim().length === 0) {
        throw new Error('Foreign identification cannot be empty')
      }
      this.value = value.trim()
    }
  }

  private cleanRUT(rut: string): string {
    return rut.replace(/[^0-9kK]/g, '').toUpperCase()
  }

  private isValidChileanRUT(rut: string): boolean {
    const cleanRut = this.cleanRUT(rut)
    
    if (cleanRut.length < 8 || cleanRut.length > 9) {
      return false
    }

    const body = cleanRut.slice(0, -1)
    const dv = cleanRut.slice(-1)

    if (!/^\d+$/.test(body)) {
      return false
    }

    return this.calculateDV(body) === dv
  }

  private calculateDV(rut: string): string {
    let sum = 0
    let multiplier = 2

    for (let i = rut.length - 1; i >= 0; i--) {
      sum += parseInt(rut[i]) * multiplier
      multiplier = multiplier === 7 ? 2 : multiplier + 1
    }

    const remainder = sum % 11
    const dv = 11 - remainder

    if (dv === 11) return '0'
    if (dv === 10) return 'K'
    return dv.toString()
  }

  public getValue(): string {
    return this.value
  }

  public getFormattedValue(): string {
    if (!this.isChilean) {
      return this.value
    }

    const cleanRut = this.value
    const body = cleanRut.slice(0, -1)
    const dv = cleanRut.slice(-1)

    // Format: XX.XXX.XXX-X
    const formattedBody = body.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
    return `${formattedBody}-${dv}`
  }

  public isChileanRUT(): boolean {
    return this.isChilean
  }

  public equals(other: RUT): boolean {
    return this.value === other.value && this.isChilean === other.isChilean
  }

  public toString(): string {
    return this.getFormattedValue()
  }

  static fromString(value: string, isChilean: boolean = true): RUT {
    return new RUT(value, isChilean)
  }

  static createChilean(rut: string): RUT {
    return new RUT(rut, true)
  }

  static createForeign(identification: string): RUT {
    return new RUT(identification, false)
  }
}
