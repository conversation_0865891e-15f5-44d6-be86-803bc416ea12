"use client"

import { getAppName, getCompanyName } from "@/lib/config"

interface BasicLayoutProps {
  children: React.ReactNode
}

export function BasicLayout({ children }: BasicLayoutProps) {
  return (
    <div className="h-screen flex bg-gray-50">
      {/* Sidebar básico */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        {/* Header del sidebar */}
        <div className="flex items-center p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center shadow-soft">
              <span className="text-white font-bold text-sm">e</span>
            </div>
            <span className="text-xl font-bold text-gray-700">
              {getAppName()}
            </span>
          </div>
        </div>

        {/* Navegación básica */}
        <nav className="flex-1 px-3 py-4 space-y-1">
          <a
            href="/dashboard"
            className="flex items-center px-3 py-2 rounded-md text-sm font-medium bg-primary-100 text-primary-700 border-r-2 border-primary-500"
          >
            <span>🏠</span>
            <span className="ml-3">Inicio</span>
          </a>
          
          <a
            href="/app/ot"
            className="flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-700"
          >
            <span>📋</span>
            <span className="ml-3">Órdenes de Trabajo</span>
          </a>
          
          <a
            href="/app/documentos"
            className="flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-700"
          >
            <span>📄</span>
            <span className="ml-3">Documentos</span>
          </a>
          
          <a
            href="/app/usuarios"
            className="flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-700"
          >
            <span>👥</span>
            <span className="ml-3">Usuarios</span>
          </a>
        </nav>

        {/* Footer del sidebar */}
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            {getAppName()} v1.0.0
          </div>
        </div>
      </div>

      {/* Área de contenido principal */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Navbar básico */}
        <header className="bg-white border-b border-gray-200 shadow-soft">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              {/* Left side */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-semibold text-gray-700">
                    {getAppName()}
                  </span>
                  <span className="text-sm text-gray-500 hidden sm:block">
                    - {getCompanyName()}
                  </span>
                </div>
              </div>

              {/* Right side */}
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">Usuario Conectado</span>
                <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                  Salir
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Contenido principal */}
        <main className="flex-1 overflow-auto">
          <div className="h-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

// Componentes auxiliares básicos
interface BasicPageHeaderProps {
  title: string
  description?: string
}

export function BasicPageHeader({ title, description }: BasicPageHeaderProps) {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
        {description && (
          <p className="mt-1 text-sm text-gray-600">{description}</p>
        )}
      </div>
    </div>
  )
}

interface BasicPageContentProps {
  children: React.ReactNode
  className?: string
}

export function BasicPageContent({ children, className = "" }: BasicPageContentProps) {
  return (
    <div className={`p-6 ${className}`}>
      <div className="max-w-7xl mx-auto">
        {children}
      </div>
    </div>
  )
}
