// Domain - Shared - Permission Service
export interface Permission {
  module: string
  actions: string[]
}

export enum UserRole {
  ADMIN = 'ADMIN',
  NOTARIO = 'NOTARIO',
  USUARIO = 'USUARIO',
  CONSULTA = 'CONSULTA'
}

export class PermissionService {
  private static readonly ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
    [UserRole.ADMIN]: [
      // Admin tiene acceso completo a todo
      { module: 'dashboard', actions: ['view', 'stats'] },
      { module: 'ot', actions: ['view', 'create', 'edit', 'delete', 'assign', 'change_status'] },
      { module: 'documentos', actions: ['view', 'create', 'edit', 'delete', 'sign', 'export'] },
      { module: 'usuarios', actions: ['view', 'create', 'edit', 'delete', 'assign_roles'] },
      { module: 'licencias', actions: ['view', 'validate', 'manage'] },
      { module: 'reportes', actions: ['view', 'export', 'advanced'] },
      { module: 'caja', actions: ['view', 'create', 'edit', 'close'] },
      { module: 'configuracion', actions: ['view', 'edit'] }
    ],
    [UserRole.NOTARIO]: [
      { module: 'dashboard', actions: ['view', 'stats'] },
      { module: 'ot', actions: ['view', 'create', 'edit', 'change_status'] },
      { module: 'documentos', actions: ['view', 'create', 'edit', 'sign', 'export'] },
      { module: 'usuarios', actions: ['view'] },
      { module: 'licencias', actions: ['view'] },
      { module: 'reportes', actions: ['view', 'export'] },
      { module: 'caja', actions: ['view', 'create', 'edit'] }
    ],
    [UserRole.USUARIO]: [
      { module: 'dashboard', actions: ['view'] },
      { module: 'ot', actions: ['view'] },
      { module: 'documentos', actions: ['view'] },
      { module: 'usuarios', actions: [] },
      { module: 'licencias', actions: [] },
      { module: 'reportes', actions: ['view'] },
      { module: 'caja', actions: [] }
    ],
    [UserRole.CONSULTA]: [
      { module: 'dashboard', actions: ['view'] },
      { module: 'ot', actions: ['view'] },
      { module: 'documentos', actions: ['view'] },
      { module: 'usuarios', actions: [] },
      { module: 'licencias', actions: [] },
      { module: 'reportes', actions: ['view'] },
      { module: 'caja', actions: [] }
    ]
  }

  public static hasPermission(
    userRole: UserRole | string, 
    module: string, 
    action: string
  ): boolean {
    // Admin siempre tiene todos los permisos
    if (userRole === UserRole.ADMIN) {
      return true
    }

    // Convertir string a enum si es necesario
    const role = typeof userRole === 'string' ? userRole as UserRole : userRole
    
    const rolePermissions = this.ROLE_PERMISSIONS[role]
    if (!rolePermissions) {
      return false
    }

    const modulePermission = rolePermissions.find(p => p.module === module)
    if (!modulePermission) {
      return false
    }

    return modulePermission.actions.includes(action)
  }

  public static getUserPermissions(userRole: UserRole | string): Permission[] {
    // Admin tiene todos los permisos
    if (userRole === UserRole.ADMIN) {
      return this.ROLE_PERMISSIONS[UserRole.ADMIN]
    }

    const role = typeof userRole === 'string' ? userRole as UserRole : userRole
    return this.ROLE_PERMISSIONS[role] || []
  }

  public static getAvailableModules(userRole: UserRole | string): string[] {
    const permissions = this.getUserPermissions(userRole)
    return permissions
      .filter(p => p.actions.length > 0)
      .map(p => p.module)
  }

  public static canAccessModule(userRole: UserRole | string, module: string): boolean {
    const permissions = this.getUserPermissions(userRole)
    const modulePermission = permissions.find(p => p.module === module)
    return modulePermission ? modulePermission.actions.length > 0 : false
  }

  public static getModuleActions(userRole: UserRole | string, module: string): string[] {
    const permissions = this.getUserPermissions(userRole)
    const modulePermission = permissions.find(p => p.module === module)
    return modulePermission ? modulePermission.actions : []
  }

  // Helper para verificar múltiples permisos
  public static hasAnyPermission(
    userRole: UserRole | string,
    checks: Array<{ module: string; action: string }>
  ): boolean {
    return checks.some(check => this.hasPermission(userRole, check.module, check.action))
  }

  public static hasAllPermissions(
    userRole: UserRole | string,
    checks: Array<{ module: string; action: string }>
  ): boolean {
    return checks.every(check => this.hasPermission(userRole, check.module, check.action))
  }
}
