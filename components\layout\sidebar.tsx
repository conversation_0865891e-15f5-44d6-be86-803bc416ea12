"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { usePermissions } from "@/hooks/use-permissions"
import { getAppName } from "@/lib/config"
import {
  HomeIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  CogIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  BookOpenIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline"

interface MenuItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  permission?: string
  children?: MenuItem[]
}

const menuItems: MenuItem[] = [
  {
    name: "Inicio",
    href: "/dashboard",
    icon: HomeIcon,
  },
  {
    name: "<PERSON><PERSON><PERSON> de Trabajo",
    href: "/app/ot",
    icon: ClipboardDocumentListIcon,
    permission: "ot_access",
    children: [
      { name: "<PERSON>rea<PERSON> OT", href: "/app/ot/crear", icon: DocumentTextIcon, permission: "ot_create" },
      { name: "Listar OT", href: "/app/ot/listar", icon: ClipboardDocumentListIcon, permission: "ot_access" },
    ]
  },
  {
    name: "Documentos",
    href: "/app/documentos",
    icon: DocumentTextIcon,
    permission: "documento_privado_access",
    children: [
      { name: "Documentos Privados", href: "/app/documentos/privados", icon: DocumentTextIcon, permission: "documento_privado_access" },
      { name: "Escrituras Públicas", href: "/app/documentos/publicas", icon: BookOpenIcon, permission: "escritura_publica_access" },
    ]
  },
  {
    name: "Repertorio",
    href: "/app/repertorio",
    icon: BookOpenIcon,
    permission: "repertorio_access",
  },
  {
    name: "Caja",
    href: "/app/caja",
    icon: CurrencyDollarIcon,
    permission: "caja_access",
  },
  {
    name: "Reportes",
    href: "/app/reportes",
    icon: ChartBarIcon,
    permission: "reportes_access",
  },
  {
    name: "Usuarios",
    href: "/app/usuarios",
    icon: UsersIcon,
    permission: "user_management",
  },
  {
    name: "Configuración",
    href: "/app/configuracion",
    icon: CogIcon,
    permission: "admin_access",
  },
]

interface SidebarProps {
  collapsed?: boolean
  onToggle?: () => void
}

export function Sidebar({ collapsed = false, onToggle }: SidebarProps) {
  const pathname = usePathname()
  const { hasPermission, isAdmin } = usePermissions()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isItemVisible = (item: MenuItem) => {
    if (!item.permission) return true
    return hasPermission(item.permission) || isAdmin()
  }

  const isItemActive = (href: string) => {
    if (href === "/dashboard") {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  const renderMenuItem = (item: MenuItem, level = 0) => {
    if (!isItemVisible(item)) return null

    const isActive = isItemActive(item.href)
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.name)
    const visibleChildren = item.children?.filter(isItemVisible) || []

    return (
      <div key={item.name}>
        <div
          className={`
            flex items-center justify-between px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200
            ${level > 0 ? 'ml-4' : ''}
            ${isActive 
              ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-500' 
              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-700'
            }
          `}
        >
          <Link
            href={item.href}
            className="flex items-center flex-1 min-w-0"
          >
            <item.icon 
              className={`
                ${collapsed ? 'h-6 w-6' : 'h-5 w-5'} 
                ${collapsed ? 'mx-auto' : 'mr-3'} 
                flex-shrink-0
                ${isActive ? 'text-primary-600' : 'text-gray-400'}
              `} 
            />
            {!collapsed && (
              <span className="truncate">{item.name}</span>
            )}
          </Link>
          
          {!collapsed && hasChildren && visibleChildren.length > 0 && (
            <button
              onClick={() => toggleExpanded(item.name)}
              className="p-1 rounded hover:bg-gray-200 transition-colors duration-200"
            >
              <ChevronRightIcon 
                className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
                  isExpanded ? 'rotate-90' : ''
                }`}
              />
            </button>
          )}
        </div>

        {!collapsed && hasChildren && isExpanded && visibleChildren.length > 0 && (
          <div className="mt-1 space-y-1">
            {visibleChildren.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`
      bg-white border-r border-gray-200 transition-all duration-300 ease-in-out
      ${collapsed ? 'w-16' : 'w-64'}
      flex flex-col h-full
    `}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center shadow-soft">
              <span className="text-white font-bold text-sm">e</span>
            </div>
            <span className="text-xl font-bold text-gray-700">
              {getAppName()}
            </span>
          </div>
        )}
        
        {collapsed && (
          <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center shadow-soft mx-auto">
            <span className="text-white font-bold text-sm">e</span>
          </div>
        )}

        {onToggle && (
          <button
            onClick={onToggle}
            className="p-1 rounded-md hover:bg-gray-100 transition-colors duration-200"
            title={collapsed ? "Expandir sidebar" : "Contraer sidebar"}
          >
            {collapsed ? (
              <ChevronRightIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronLeftIcon className="h-5 w-5 text-gray-500" />
            )}
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
        {menuItems.map(item => renderMenuItem(item))}
      </nav>

      {/* Footer */}
      {!collapsed && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500 text-center">
            {getAppName()} v1.0.0
          </div>
        </div>
      )}
    </div>
  )
}
