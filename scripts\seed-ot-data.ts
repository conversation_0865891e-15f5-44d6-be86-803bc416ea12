// Script to seed OT system data
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedOTData() {
  console.log('🌱 Seeding OT system data...')

  try {
    // 1. Seed Tipos de Materia
    console.log('📋 Creating tipos de materia...')
    
    const materias = [
      {
        tipo: 'COMPRAVENTA',
        descripcion: 'Compraventa de bienes raíces',
        calidades: ['COMPRADOR', 'VENDEDOR', 'BANCO', 'TESTIGO']
      },
      {
        tipo: 'HIPOTECA',
        descripcion: 'Constitución de hipoteca',
        calidades: ['DEUDOR', 'ACREEDOR', 'BANCO', 'TESTIGO']
      },
      {
        tipo: 'MANDATO',
        descripcion: 'Otorgamiento de mandato',
        calidades: ['MANDANTE', 'MANDATARIO', 'TESTIGO']
      },
      {
        tipo: 'DONACION',
        descripcion: 'Donación de bienes',
        calidades: ['DONAN<PERSON>', 'DON<PERSON>AR<PERSON>', 'TESTIGO']
      },
      {
        tipo: 'ARRIENDO',
        descripcion: 'Contrato de arriendo',
        calidades: ['ARRENDADOR', 'ARRENDATARIO', 'TESTIGO']
      },
      {
        tipo: 'MUTUO',
        descripcion: 'Contrato de mutuo',
        calidades: ['MUTUANTE', 'MUTUARIO', 'BANCO', 'TESTIGO']
      },
      {
        tipo: 'PRENDA',
        descripcion: 'Constitución de prenda',
        calidades: ['PRENDARIO', 'PRENDADOR', 'TESTIGO']
      },
      {
        tipo: 'PROMESA',
        descripcion: 'Promesa de compraventa',
        calidades: ['PROMITENTE_COMPRADOR', 'PROMITENTE_VENDEDOR', 'TESTIGO']
      }
    ]

    for (const materia of materias) {
      const tipoMateria = await prisma.tipoMateria.upsert({
        where: { tipo: materia.tipo },
        update: { descripcion: materia.descripcion },
        create: {
          tipo: materia.tipo,
          descripcion: materia.descripcion
        }
      })

      // Create calidades for this materia
      for (const calidad of materia.calidades) {
        const existing = await prisma.tipoMateriaCalidad.findFirst({
          where: {
            tipoMateriaId: tipoMateria.id,
            nombre: calidad
          }
        })

        if (!existing) {
          await prisma.tipoMateriaCalidad.create({
            data: {
              tipoMateriaId: tipoMateria.id,
              nombre: calidad
            }
          })
        }
      }
    }

    // 2. Seed Workflows
    console.log('🔄 Creating workflows...')
    
    const workflows = [
      {
        tipo: 'escritura_publica',
        workflow: 'ESCRITURA PUBLICA',
        orden: 1,
        estados: [
          { estado: 'INGRESO', orden: 1, icon: 'ingreso' },
          { estado: 'REVISION', orden: 2, icon: 'revision' },
          { estado: 'PREPARACION', orden: 3, icon: 'preparacion' },
          { estado: 'FIRMA', orden: 4, icon: 'firma' },
          { estado: 'REPERTORIO', orden: 5, icon: 'repertorio' },
          { estado: 'ENTREGA', orden: 6, icon: 'entrega' }
        ]
      },
      {
        tipo: 'documento_privado',
        workflow: 'DOCUMENTO PRIVADO',
        orden: 2,
        estados: [
          { estado: 'INGRESO', orden: 1, icon: 'ingreso' },
          { estado: 'REVISION', orden: 2, icon: 'revision' },
          { estado: 'PREPARACION', orden: 3, icon: 'preparacion' },
          { estado: 'FIRMA', orden: 4, icon: 'firma' },
          { estado: 'ENTREGA', orden: 5, icon: 'entrega' }
        ]
      },
      {
        tipo: 'repertorio_vehiculo',
        workflow: 'VEHICULO',
        orden: 3,
        estados: [
          { estado: 'INGRESO', orden: 1, icon: 'ingreso' },
          { estado: 'REVISION', orden: 2, icon: 'revision' },
          { estado: 'TRAMITE', orden: 3, icon: 'tramite' },
          { estado: 'ENTREGA', orden: 4, icon: 'entrega' }
        ]
      },
      {
        tipo: 'instruccion',
        workflow: 'INSTRUCCION',
        orden: 4,
        estados: [
          { estado: 'INGRESO', orden: 1, icon: 'ingreso' },
          { estado: 'PROCESAMIENTO', orden: 2, icon: 'procesamiento' },
          { estado: 'ENTREGA', orden: 3, icon: 'entrega' }
        ]
      }
    ]

    for (const workflowData of workflows) {
      const existing = await prisma.workflow.findFirst({
        where: {
          tipo: workflowData.tipo,
          workflow: workflowData.workflow
        }
      })

      const workflow = existing || await prisma.workflow.create({
        data: {
          tipo: workflowData.tipo,
          workflow: workflowData.workflow,
          orden: workflowData.orden
        }
      })

      // Create estados for this workflow
      for (const estadoData of workflowData.estados) {
        const existingEstado = await prisma.workflowEstado.findFirst({
          where: {
            workflowId: workflow.id,
            estado: estadoData.estado
          }
        })

        const estado = existingEstado || await prisma.workflowEstado.create({
          data: {
            workflowId: workflow.id,
            estado: estadoData.estado,
            orden: estadoData.orden,
            icon: estadoData.icon
          }
        })

        // Create default notas for each estado
        const notas = [
          `${estadoData.estado} - Inicio`,
          `${estadoData.estado} - En proceso`,
          `${estadoData.estado} - Completado`
        ]

        for (let i = 0; i < notas.length; i++) {
          const existingNota = await prisma.workflowNota.findFirst({
            where: {
              estadoId: estado.id,
              nota: notas[i]
            }
          })

          if (!existingNota) {
            await prisma.workflowNota.create({
              data: {
                estadoId: estado.id,
                nota: notas[i],
                defineCiclo: i === notas.length - 1 // Last note defines cycle
              }
            })
          }
        }
      }
    }

    // 3. Create sample OT data
    console.log('📄 Creating sample OT...')
    
    const adminUser = await prisma.user.findUnique({ where: { username: 'ADMIN' } })
    if (adminUser) {
      // Check if sample OT already exists
      const existingOT = await prisma.ordenTrabajo.findUnique({ where: { numeroOT: 'OT-2025-001' } })
      if (existingOT) {
        console.log('✅ Sample OT already exists, skipping creation')
        return
      }

      const sampleOT = await prisma.ordenTrabajo.create({
        data: {
          numeroOT: 'OT-2025-001',
          tipoDocumento: 'escritura_publica',
          materiaDetalle: 'Compraventa de casa habitación ubicada en Santiago, Región Metropolitana',
          estado: 'CREADA',
          prioridad: 'MEDIA',
          gestora: 'INMOBILIARIA EJEMPLO S.A.',
          numeroWorkflow: 'WF-2025-001',
          observaciones: 'OT de ejemplo para pruebas del sistema',
          creadoPor: adminUser.id,
          comparecientes: {
            create: [
              {
                rut: '12345678-9',
                nombres: 'JUAN CARLOS',
                apellidoPaterno: 'PEREZ',
                apellidoMaterno: 'GONZALEZ',
                calidad: 'COMPRADOR',
                esChileno: true
              },
              {
                rut: '98765432-1',
                nombres: 'MARIA ELENA',
                apellidoPaterno: 'RODRIGUEZ',
                apellidoMaterno: 'SILVA',
                calidad: 'VENDEDOR',
                esChileno: true
              }
            ]
          },
          historial: {
            create: {
              accion: 'CREADA',
              descripcion: 'OT creada en el sistema',
              estadoNuevo: 'CREADA',
              creadoPor: adminUser.id
            }
          }
        }
      })

      console.log(`✅ Created sample OT: ${sampleOT.numeroOT}`)
    }

    console.log('✅ OT system data seeded successfully!')
    console.log(`📋 Created ${materias.length} tipos de materia`)
    console.log(`🔄 Created ${workflows.length} workflows`)
    console.log('📄 Created sample OT data')

  } catch (error) {
    console.error('❌ Error seeding OT data:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
if (require.main === module) {
  seedOTData()
    .then(() => {
      console.log('🎉 OT data seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 OT data seeding failed:', error)
      process.exit(1)
    })
}

export { seedOTData }
