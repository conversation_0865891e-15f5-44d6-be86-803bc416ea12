// Domain - OT - Value Objects
export enum OTStatus {
  CREADA = 'CREADA',
  EN_PROCESO = 'EN_PROCESO',
  EN_REPERTORIO = 'EN_REPERTORIO',
  FIRMADA = 'FIRMADA',
  ENTREGADA = 'ENTREGADA',
  ANULADA = 'ANULADA',
  SUSPENDIDA = 'SUSPENDIDA'
}

export class OTStatusVO {
  private readonly value: OTStatus

  constructor(value: string) {
    if (!Object.values(OTStatus).includes(value as OTStatus)) {
      throw new Error(`Invalid OT status: ${value}`)
    }
    this.value = value as OTStatus
  }

  public getValue(): OTStatus {
    return this.value
  }

  public getDisplayName(): string {
    const displayNames = {
      [OTStatus.CREADA]: 'Creada',
      [OTStatus.EN_PROCESO]: 'En Proceso',
      [OTStatus.EN_REPERTORIO]: 'En Repertorio',
      [OTStatus.FIRMADA]: 'Firmada',
      [OTStatus.ENTREGADA]: 'Entregada',
      [OTStatus.ANULADA]: 'Anulada',
      [OTStatus.SUSPENDIDA]: 'Suspendida'
    }
    return displayNames[this.value]
  }

  public getColor(): string {
    const colors = {
      [OTStatus.CREADA]: 'bg-blue-100 text-blue-800',
      [OTStatus.EN_PROCESO]: 'bg-yellow-100 text-yellow-800',
      [OTStatus.EN_REPERTORIO]: 'bg-purple-100 text-purple-800',
      [OTStatus.FIRMADA]: 'bg-green-100 text-green-800',
      [OTStatus.ENTREGADA]: 'bg-gray-100 text-gray-800',
      [OTStatus.ANULADA]: 'bg-red-100 text-red-800',
      [OTStatus.SUSPENDIDA]: 'bg-orange-100 text-orange-800'
    }
    return colors[this.value]
  }

  public canTransitionTo(newStatus: OTStatus): boolean {
    const allowedTransitions = {
      [OTStatus.CREADA]: [OTStatus.EN_PROCESO, OTStatus.ANULADA],
      [OTStatus.EN_PROCESO]: [OTStatus.EN_REPERTORIO, OTStatus.SUSPENDIDA, OTStatus.ANULADA],
      [OTStatus.EN_REPERTORIO]: [OTStatus.FIRMADA, OTStatus.EN_PROCESO, OTStatus.ANULADA],
      [OTStatus.FIRMADA]: [OTStatus.ENTREGADA, OTStatus.ANULADA],
      [OTStatus.ENTREGADA]: [],
      [OTStatus.ANULADA]: [],
      [OTStatus.SUSPENDIDA]: [OTStatus.EN_PROCESO, OTStatus.ANULADA]
    }
    return allowedTransitions[this.value].includes(newStatus)
  }

  public isActive(): boolean {
    return ![OTStatus.ENTREGADA, OTStatus.ANULADA].includes(this.value)
  }

  public equals(other: OTStatusVO): boolean {
    return this.value === other.value
  }

  public toString(): string {
    return this.value
  }

  static fromString(value: string): OTStatusVO {
    return new OTStatusVO(value)
  }

  static getAvailableStatuses(): Array<{value: OTStatus, label: string, color: string}> {
    return Object.values(OTStatus).map(status => {
      const statusVO = new OTStatusVO(status)
      return {
        value: status,
        label: statusVO.getDisplayName(),
        color: statusVO.getColor()
      }
    })
  }
}
