"use client"

import { useState } from "react"

interface WorkflowStep {
  id: string
  estado: string
  orden: number
  icon: string
  completed: boolean
  current: boolean
  description?: string
}

interface WorkflowVisualProps {
  tipoDocumento: string
  estadoActual: string
  className?: string
}

export function WorkflowVisual({ tipoDocumento, estadoActual, className = "" }: WorkflowVisualProps) {
  const getWorkflowSteps = (tipo: string): WorkflowStep[] => {
    const workflows = {
      'escritura_publica': [
        { id: 'ingreso', estado: 'INGRESO', orden: 1, icon: '📝', description: 'Ingreso de documentos' },
        { id: 'revision', estado: 'REVISION', orden: 2, icon: '🔍', description: 'Revisión legal' },
        { id: 'preparacion', estado: 'PREPARACION', orden: 3, icon: '📋', description: 'Preparación de escritura' },
        { id: 'firma', estado: 'FIRMA', orden: 4, icon: '✍️', description: 'Firma de partes' },
        { id: 'repertorio', estado: 'REPERTORIO', orden: 5, icon: '📚', description: 'Ingreso a repertorio' },
        { id: 'entrega', estado: 'ENTREGA', orden: 6, icon: '📤', description: 'Entrega final' }
      ],
      'documento_privado': [
        { id: 'ingreso', estado: 'INGRESO', orden: 1, icon: '📝', description: 'Ingreso de documentos' },
        { id: 'revision', estado: 'REVISION', orden: 2, icon: '🔍', description: 'Revisión legal' },
        { id: 'preparacion', estado: 'PREPARACION', orden: 3, icon: '📋', description: 'Preparación de documento' },
        { id: 'firma', estado: 'FIRMA', orden: 4, icon: '✍️', description: 'Firma de partes' },
        { id: 'entrega', estado: 'ENTREGA', orden: 5, icon: '📤', description: 'Entrega final' }
      ],
      'repertorio_vehiculo': [
        { id: 'ingreso', estado: 'INGRESO', orden: 1, icon: '📝', description: 'Ingreso de documentos' },
        { id: 'revision', estado: 'REVISION', orden: 2, icon: '🔍', description: 'Revisión documentos' },
        { id: 'tramite', estado: 'TRAMITE', orden: 3, icon: '🚗', description: 'Trámite vehicular' },
        { id: 'entrega', estado: 'ENTREGA', orden: 4, icon: '📤', description: 'Entrega final' }
      ],
      'instruccion': [
        { id: 'ingreso', estado: 'INGRESO', orden: 1, icon: '📝', description: 'Ingreso de instrucción' },
        { id: 'procesamiento', estado: 'PROCESAMIENTO', orden: 2, icon: '⚙️', description: 'Procesamiento' },
        { id: 'entrega', estado: 'ENTREGA', orden: 3, icon: '📤', description: 'Entrega final' }
      ],
      'certificado': [
        { id: 'ingreso', estado: 'INGRESO', orden: 1, icon: '📝', description: 'Solicitud de certificado' },
        { id: 'procesamiento', estado: 'PROCESAMIENTO', orden: 2, icon: '⚙️', description: 'Generación' },
        { id: 'entrega', estado: 'ENTREGA', orden: 3, icon: '📤', description: 'Entrega final' }
      ],
      'letra': [
        { id: 'ingreso', estado: 'INGRESO', orden: 1, icon: '📝', description: 'Ingreso de letra' },
        { id: 'procesamiento', estado: 'PROCESAMIENTO', orden: 2, icon: '⚙️', description: 'Procesamiento' },
        { id: 'entrega', estado: 'ENTREGA', orden: 3, icon: '📤', description: 'Entrega final' }
      ]
    }

    return workflows[tipo as keyof typeof workflows] || workflows['documento_privado']
  }

  const mapEstadoToStep = (estado: string): string => {
    const mapping = {
      'BORRADOR': 'ingreso',
      'CREADA': 'ingreso',
      'EN_PROCESO': 'revision',
      'EN_REPERTORIO': 'repertorio',
      'FIRMADA': 'firma',
      'ENTREGADA': 'entrega',
      'ANULADA': '',
      'SUSPENDIDA': ''
    }
    return mapping[estado as keyof typeof mapping] || 'ingreso'
  }

  const steps = getWorkflowSteps(tipoDocumento)
  const currentStepId = mapEstadoToStep(estadoActual)
  const currentStepIndex = steps.findIndex(step => step.id === currentStepId)

  // Marcar pasos como completados/actuales
  const stepsWithStatus = steps.map((step, index) => ({
    ...step,
    completed: index < currentStepIndex,
    current: step.id === currentStepId
  }))

  const getStepClasses = (step: WorkflowStep, index: number) => {
    if (estadoActual === 'ANULADA') {
      return 'bg-red-100 border-red-300 text-red-700'
    }
    if (estadoActual === 'SUSPENDIDA') {
      return 'bg-orange-100 border-orange-300 text-orange-700'
    }
    if (step.completed) {
      return 'bg-green-100 border-green-300 text-green-700'
    }
    if (step.current) {
      return 'bg-blue-100 border-blue-300 text-blue-700 ring-2 ring-blue-500'
    }
    return 'bg-gray-100 border-gray-300 text-gray-500'
  }

  const getConnectorClasses = (index: number) => {
    if (estadoActual === 'ANULADA' || estadoActual === 'SUSPENDIDA') {
      return 'border-gray-300'
    }
    if (index < currentStepIndex) {
      return 'border-green-400'
    }
    return 'border-gray-300'
  }

  if (!tipoDocumento || estadoActual === 'BORRADOR') {
    return (
      <div className={`p-4 bg-yellow-50 border border-yellow-200 rounded-lg ${className}`}>
        <div className="flex items-center">
          <div className="w-8 h-8 bg-yellow-100 border border-yellow-300 rounded-full flex items-center justify-center text-yellow-700 text-sm">
            📝
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-yellow-800">Borrador</p>
            <p className="text-xs text-yellow-600">Complete los datos para ver el workflow</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`p-4 bg-white border border-gray-200 rounded-lg ${className}`}>
      <div className="mb-3">
        <h4 className="text-sm font-medium text-gray-900">Workflow - {tipoDocumento.replace('_', ' ').toUpperCase()}</h4>
        <p className="text-xs text-gray-500">Estado actual: {estadoActual}</p>
      </div>

      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
        {stepsWithStatus.map((step, index) => (
          <div key={step.id} className="flex items-center flex-shrink-0">
            {/* Step Circle */}
            <div className="relative group">
              <div className={`
                w-10 h-10 border-2 rounded-full flex items-center justify-center text-sm font-medium transition-all
                ${getStepClasses(step, index)}
              `}>
                <span>{step.icon}</span>
              </div>
              
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                {step.description}
              </div>
            </div>

            {/* Connector Line */}
            {index < stepsWithStatus.length - 1 && (
              <div className={`
                w-8 h-0.5 border-t-2 transition-colors
                ${getConnectorClasses(index)}
              `} />
            )}
          </div>
        ))}
      </div>

      {/* Step Labels */}
      <div className="flex items-start space-x-2 mt-2 overflow-x-auto">
        {stepsWithStatus.map((step, index) => (
          <div key={`label-${step.id}`} className="flex flex-col items-center flex-shrink-0 min-w-[80px]">
            <span className={`
              text-xs font-medium text-center
              ${step.current ? 'text-blue-700' : step.completed ? 'text-green-700' : 'text-gray-500'}
            `}>
              {step.estado}
            </span>
            {index < stepsWithStatus.length - 1 && <div className="w-8" />}
          </div>
        ))}
      </div>

      {/* Estado especial */}
      {(estadoActual === 'ANULADA' || estadoActual === 'SUSPENDIDA') && (
        <div className={`
          mt-3 p-2 rounded text-center text-sm font-medium
          ${estadoActual === 'ANULADA' ? 'bg-red-50 text-red-700' : 'bg-orange-50 text-orange-700'}
        `}>
          OT {estadoActual}
        </div>
      )}
    </div>
  )
}
