// Application - User - Use Cases
import { UserRepository } from '../../../domain/user/repositories/user-repository'
import { User } from '../../../domain/user/entities/user'
import { UserId } from '../../../domain/shared/value-objects/id'

export interface GetUserProfileRequest {
  userId: string
}

export interface GetUserProfileResponse {
  success: boolean
  user?: User
  error?: string
}

export class GetUserProfileUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(request: GetUserProfileRequest): Promise<GetUserProfileResponse> {
    try {
      const userId = new UserId(request.userId)
      const user = await this.userRepository.findById(userId)

      if (!user) {
        return {
          success: false,
          error: 'User not found'
        }
      }

      return {
        success: true,
        user
      }
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get user profile'
      }
    }
  }
}
