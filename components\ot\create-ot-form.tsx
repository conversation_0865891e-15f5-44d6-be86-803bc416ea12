"use client"

import { useState, useTransition } from "react"
import { createOT } from "@/src/presentation/actions/ot-actions"
import { ComparecienteForm } from "./compareciente-form"
import { ErrorDisplay, PermissionError, useErrorHandler } from "../ui/error-display"

interface FormData {
  documentTypes: Array<{value: string, label: string}>
  priorities: Array<{value: string, label: string, color: string}>
  calidades: Array<{value: string, label: string}>
  currentUser: {id: string, name: string}
}

interface CreateOTFormProps {
  formData: FormData
  currentUser: any
}

interface Compareciente {
  id: string
  rut: string
  nombres: string
  apellidoPaterno: string
  apellidoMaterno: string
  calidad: string
  esChileno: boolean
}

export function CreateOTForm({ formData, currentUser }: CreateOTFormProps) {
  const [isPending, startTransition] = useTransition()
  const [currentStep, setCurrentStep] = useState(1)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [success, setSuccess] = useState<string>("")
  const [errorId, setErrorId] = useState<string>("")
  const { error, handleError, clearError, renderError } = useErrorHandler()
  
  // Form state
  const [documentType, setDocumentType] = useState("")
  const [materia, setMateria] = useState("")
  const [priority, setPriority] = useState("MEDIA")
  const [fechaVencimiento, setFechaVencimiento] = useState("")
  const [observaciones, setObservaciones] = useState("")
  const [comparecientes, setComparecientes] = useState<Compareciente[]>([])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors({})
    setSuccess("")
    clearError()

    if (comparecientes.length === 0) {
      setErrors({ comparecientes: "Al menos un compareciente es requerido" })
      setCurrentStep(2)
      return
    }

    const formData = new FormData()
    formData.append('documentType', documentType)
    formData.append('materia', materia)
    formData.append('priority', priority)
    formData.append('fechaVencimiento', fechaVencimiento)
    formData.append('observaciones', observaciones)
    formData.append('comparecientes', JSON.stringify(comparecientes))

    startTransition(async () => {
      const result = await createOT(formData)

      if (result.success) {
        setSuccess(result.message || "OT creada exitosamente")
        // Reset form or redirect
        setTimeout(() => {
          window.location.href = "/ot"
        }, 2000)
      } else {
        if (result.error === 'Permisos insuficientes') {
          handleError(result.error, result.errorId, 'permission')
        } else if (result.fieldErrors) {
          setErrors(result.fieldErrors)
        } else {
          handleError(result.error || "Error desconocido", result.errorId)
        }
      }
    })
  }

  const addCompareciente = (compareciente: Omit<Compareciente, 'id'>) => {
    const newCompareciente = {
      ...compareciente,
      id: Date.now().toString()
    }
    setComparecientes([...comparecientes, newCompareciente])
  }

  const removeCompareciente = (id: string) => {
    setComparecientes(comparecientes.filter(c => c.id !== id))
  }

  const nextStep = () => {
    if (currentStep === 1) {
      // Validate step 1
      const stepErrors: Record<string, string> = {}
      if (!documentType) stepErrors.documentType = "Tipo de documento es requerido"
      if (!materia || materia.length < 10) stepErrors.materia = "Materia debe tener al menos 10 caracteres"
      
      if (Object.keys(stepErrors).length > 0) {
        setErrors(stepErrors)
        return
      }
    }
    
    setErrors({})
    setCurrentStep(currentStep + 1)
  }

  const prevStep = () => {
    setCurrentStep(currentStep - 1)
  }

  return (
    <form onSubmit={handleSubmit} className="p-6">
      {/* Success Message */}
      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {renderError("mb-6")}

      {/* General Error */}
      {errors.general && (
        <ErrorDisplay
          error={errors.general}
          errorId={errorId}
          className="mb-6"
        />
      )}

      {/* Step 1: Datos Generales */}
      {currentStep === 1 && (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Datos Generales</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Tipo de Documento */}
            <div>
              <label htmlFor="documentType" className="block text-sm font-medium text-gray-700 mb-2">
                Tipo de Documento *
              </label>
              <select
                id="documentType"
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.documentType ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              >
                <option value="">Seleccionar tipo...</option>
                {formData.documentTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
              {errors.documentType && (
                <p className="mt-1 text-sm text-red-600">{errors.documentType}</p>
              )}
            </div>

            {/* Prioridad */}
            <div>
              <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-2">
                Prioridad *
              </label>
              <select
                id="priority"
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                {formData.priorities.map((p) => (
                  <option key={p.value} value={p.value}>
                    {p.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Materia */}
          <div>
            <label htmlFor="materia" className="block text-sm font-medium text-gray-700 mb-2">
              Materia *
            </label>
            <textarea
              id="materia"
              value={materia}
              onChange={(e) => setMateria(e.target.value)}
              rows={4}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.materia ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Describa detalladamente la materia de la orden de trabajo..."
              required
            />
            <div className="mt-1 flex justify-between">
              {errors.materia && (
                <p className="text-sm text-red-600">{errors.materia}</p>
              )}
              <p className="text-sm text-gray-500">
                {materia.length}/10 caracteres mínimo
              </p>
            </div>
          </div>

          {/* Fecha Vencimiento */}
          <div>
            <label htmlFor="fechaVencimiento" className="block text-sm font-medium text-gray-700 mb-2">
              Fecha de Vencimiento (Opcional)
            </label>
            <input
              type="date"
              id="fechaVencimiento"
              value={fechaVencimiento}
              onChange={(e) => setFechaVencimiento(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Observaciones */}
          <div>
            <label htmlFor="observaciones" className="block text-sm font-medium text-gray-700 mb-2">
              Observaciones (Opcional)
            </label>
            <textarea
              id="observaciones"
              value={observaciones}
              onChange={(e) => setObservaciones(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Observaciones adicionales..."
            />
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={nextStep}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Siguiente →
            </button>
          </div>
        </div>
      )}

      {/* Step 2: Comparecientes */}
      {currentStep === 2 && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Comparecientes</h3>
            <span className="text-sm text-gray-500">
              {comparecientes.length} compareciente(s) agregado(s)
            </span>
          </div>

          {errors.comparecientes && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.comparecientes}</p>
            </div>
          )}

          <ComparecienteForm
            calidades={formData.calidades}
            onAdd={addCompareciente}
            comparecientes={comparecientes}
            onRemove={removeCompareciente}
          />

          <div className="flex justify-between">
            <button
              type="button"
              onClick={prevStep}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              ← Anterior
            </button>
            <button
              type="button"
              onClick={nextStep}
              disabled={comparecientes.length === 0}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente →
            </button>
          </div>
        </div>
      )}

      {/* Step 3: Revisión */}
      {currentStep === 3 && (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900">Revisión y Confirmación</h3>
          
          {/* Summary */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h4 className="font-medium text-gray-900 mb-4">Resumen de la OT</h4>
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <dt className="font-medium text-gray-700">Tipo de Documento:</dt>
                <dd className="text-gray-900">
                  {formData.documentTypes.find(t => t.value === documentType)?.label}
                </dd>
              </div>
              <div>
                <dt className="font-medium text-gray-700">Prioridad:</dt>
                <dd>
                  <span className={`px-2 py-1 rounded text-xs ${
                    formData.priorities.find(p => p.value === priority)?.color
                  }`}>
                    {formData.priorities.find(p => p.value === priority)?.label}
                  </span>
                </dd>
              </div>
              <div className="md:col-span-2">
                <dt className="font-medium text-gray-700">Materia:</dt>
                <dd className="text-gray-900">{materia}</dd>
              </div>
              <div>
                <dt className="font-medium text-gray-700">Comparecientes:</dt>
                <dd className="text-gray-900">{comparecientes.length} persona(s)</dd>
              </div>
              {fechaVencimiento && (
                <div>
                  <dt className="font-medium text-gray-700">Fecha Vencimiento:</dt>
                  <dd className="text-gray-900">{new Date(fechaVencimiento).toLocaleDateString()}</dd>
                </div>
              )}
            </dl>
          </div>

          <div className="flex justify-between">
            <button
              type="button"
              onClick={prevStep}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              ← Anterior
            </button>
            <button
              type="submit"
              disabled={isPending}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isPending ? "Creando..." : "Crear OT"}
            </button>
          </div>
        </div>
      )}
    </form>
  )
}
