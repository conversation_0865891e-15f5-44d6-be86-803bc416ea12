// Domain - OT - Value Objects
export enum DocumentType {
  ESCRITURA_PUBLICA = 'escritura_publica',
  DOCUMENTO_PRIVADO = 'documento_privado',
  REPERTORIO_VEHICULO = 'repertorio_vehiculo',
  INSTRUCCION = 'instruccion',
  CERTIFICADO = 'certificado',
  LETRA = 'letra'
}

export class DocumentTypeVO {
  private readonly value: DocumentType

  constructor(value: string) {
    if (!Object.values(DocumentType).includes(value as DocumentType)) {
      throw new Error(`Invalid document type: ${value}`)
    }
    this.value = value as DocumentType
  }

  public getValue(): DocumentType {
    return this.value
  }

  public getDisplayName(): string {
    const displayNames = {
      [DocumentType.ESCRITURA_PUBLICA]: 'Escritura Pública',
      [DocumentType.DOCUMENTO_PRIVADO]: 'Documento Privado',
      [DocumentType.REPERTORIO_VEHICULO]: 'Vehí<PERSON>lo',
      [DocumentType.INSTRUCCION]: 'Instrucción',
      [DocumentType.CERTIFICADO]: 'Certificado',
      [DocumentType.LETRA]: 'Letra'
    }
    return displayNames[this.value]
  }

  public equals(other: DocumentTypeVO): boolean {
    return this.value === other.value
  }

  public toString(): string {
    return this.value
  }

  static fromString(value: string): DocumentTypeVO {
    return new DocumentTypeVO(value)
  }

  static getAvailableTypes(): Array<{value: DocumentType, label: string}> {
    return Object.values(DocumentType).map(type => ({
      value: type,
      label: new DocumentTypeVO(type).getDisplayName()
    }))
  }
}
