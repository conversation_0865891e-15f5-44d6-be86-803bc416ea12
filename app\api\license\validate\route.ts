import { NextRequest, NextResponse } from 'next/server'
import { 
  validateLicenseOnline, 
  loadLocalLicense,
  saveLocalLicense,
  decryptLicenseData,
  validateLicenseLocally 
} from '@/lib/license'

export async function POST(request: NextRequest) {
  try {
    const { licenseKey } = await request.json()
    
    if (!licenseKey) {
      return NextResponse.json(
        { error: 'Clave de licencia requerida' },
        { status: 400 }
      )
    }

    // Intentar validar online primero
    try {
      const validation = await validateLicenseOnline(licenseKey)
      
      if (validation.valid && validation.license) {
        // Guardar licencia válida localmente
        await saveLocalLicense(validation.license)
      }
      
      return NextResponse.json(validation)
    } catch (onlineError) {
      // Si falla la validación online, intentar descifrar la licencia directamente
      try {
        const licenseData = decryptLicenseData(licenseKey)
        const localValidation = validateLicenseLocally(licenseData)
        
        if (localValidation.valid) {
          await saveLocalLicense(licenseData)
        }
        
        return NextResponse.json(localValidation)
      } catch (decryptError) {
        return NextResponse.json({
          valid: false,
          error: 'Clave de licencia inválida o corrupta'
        })
      }
    }
  } catch (error) {
    console.error('Error validating license:', error)
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const license = await loadLocalLicense()
    
    if (!license) {
      return NextResponse.json({
        valid: false,
        error: 'No hay licencia instalada'
      })
    }
    
    const validation = validateLicenseLocally(license)
    return NextResponse.json(validation)
  } catch (error) {
    console.error('Error loading license:', error)
    return NextResponse.json(
      { error: 'Error al cargar la licencia' },
      { status: 500 }
    )
  }
}
