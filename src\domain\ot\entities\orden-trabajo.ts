// Domain - OT - Entities
import { BaseEntity } from '../../shared/entities/base-entity'
import { OTId, UserId } from '../../shared/value-objects/id'
import { DocumentTypeVO } from '../value-objects/document-type'
import { OTStatusVO } from '../value-objects/ot-status'
import { Compareciente } from './compareciente'

export enum Priority {
  BAJA = 'BAJA',
  MEDIA = 'MEDIA',
  ALTA = 'ALTA',
  URGENTE = 'URGENTE'
}

export interface OTMetadata {
  numeroRepertorio?: string
  fechaRepertorio?: Date
  numeroEscritura?: string
  fechaEscritura?: Date
  folios?: number
  observaciones?: string
}

export class OrdenTrabajo extends BaseEntity<OTId> {
  private numeroOT: string
  private documentType: DocumentTypeVO
  private materia: string
  private status: OTStatusVO
  private priority: Priority
  private notarioAsignado: UserId
  private comparecientes: Compareciente[]
  private metadata: OTMetadata
  private fechaVencimiento?: Date
  private fechaCompletada?: Date

  constructor(
    id: OTId,
    numeroOT: string,
    documentType: DocumentTypeVO,
    materia: string,
    status: OTStatusVO,
    priority: Priority,
    notarioAsignado: UserId,
    comparecientes: Compareciente[] = [],
    metadata: OTMetadata = {},
    fechaVencimiento?: Date,
    fechaCompletada?: Date,
    createdAt?: Date,
    updatedAt?: Date
  ) {
    super(id, createdAt, updatedAt)
    
    this.validateInputs(numeroOT, materia, comparecientes)
    
    this.numeroOT = numeroOT
    this.documentType = documentType
    this.materia = materia.trim()
    this.status = status
    this.priority = priority
    this.notarioAsignado = notarioAsignado
    this.comparecientes = [...comparecientes]
    this.metadata = { ...metadata }
    this.fechaVencimiento = fechaVencimiento
    this.fechaCompletada = fechaCompletada
  }

  private validateInputs(numeroOT: string, materia: string, comparecientes: Compareciente[]): void {
    if (!numeroOT || numeroOT.trim().length === 0) {
      throw new Error('Número de OT is required')
    }

    if (!materia || materia.trim().length === 0) {
      throw new Error('Materia is required')
    }

    if (materia.trim().length < 10) {
      throw new Error('Materia must be at least 10 characters long')
    }

    if (comparecientes.length === 0) {
      throw new Error('At least one compareciente is required')
    }
  }

  // Getters
  public getNumeroOT(): string {
    return this.numeroOT
  }

  public getDocumentType(): DocumentTypeVO {
    return this.documentType
  }

  public getMateria(): string {
    return this.materia
  }

  public getStatus(): OTStatusVO {
    return this.status
  }

  public getPriority(): Priority {
    return this.priority
  }

  public getNotarioAsignado(): UserId {
    return this.notarioAsignado
  }

  public getComparecientes(): Compareciente[] {
    return [...this.comparecientes]
  }

  public getMetadata(): OTMetadata {
    return { ...this.metadata }
  }

  public getFechaVencimiento(): Date | undefined {
    return this.fechaVencimiento
  }

  public getFechaCompletada(): Date | undefined {
    return this.fechaCompletada
  }

  // Business methods
  public isOverdue(): boolean {
    if (!this.fechaVencimiento || this.fechaCompletada) {
      return false
    }
    return new Date() > this.fechaVencimiento
  }

  public isActive(): boolean {
    return this.status.isActive()
  }

  public changeStatus(newStatus: OTStatusVO): void {
    if (!this.status.canTransitionTo(newStatus.getValue())) {
      throw new Error(`Cannot transition from ${this.status.getValue()} to ${newStatus.getValue()}`)
    }

    this.status = newStatus
    
    if (newStatus.getValue() === 'ENTREGADA') {
      this.fechaCompletada = new Date()
    }
    
    this.updateTimestamp()
  }

  public addCompareciente(compareciente: Compareciente): void {
    // Check if compareciente already exists
    const exists = this.comparecientes.some(c => c.equals(compareciente))
    if (exists) {
      throw new Error('Compareciente already exists in this OT')
    }

    this.comparecientes.push(compareciente)
    this.updateTimestamp()
  }

  public removeCompareciente(compareciente: Compareciente): void {
    const index = this.comparecientes.findIndex(c => c.equals(compareciente))
    if (index === -1) {
      throw new Error('Compareciente not found in this OT')
    }

    if (this.comparecientes.length === 1) {
      throw new Error('Cannot remove the last compareciente')
    }

    this.comparecientes.splice(index, 1)
    this.updateTimestamp()
  }

  public updateMetadata(metadata: Partial<OTMetadata>): void {
    this.metadata = { ...this.metadata, ...metadata }
    this.updateTimestamp()
  }

  public assignNotario(notarioId: UserId): void {
    this.notarioAsignado = notarioId
    this.updateTimestamp()
  }

  public updatePriority(priority: Priority): void {
    this.priority = priority
    this.updateTimestamp()
  }

  public updateMateria(materia: string): void {
    if (!materia || materia.trim().length < 10) {
      throw new Error('Materia must be at least 10 characters long')
    }
    this.materia = materia.trim()
    this.updateTimestamp()
  }

  public getPriorityDisplayName(): string {
    const displayNames = {
      [Priority.BAJA]: 'Baja',
      [Priority.MEDIA]: 'Media',
      [Priority.ALTA]: 'Alta',
      [Priority.URGENTE]: 'Urgente'
    }
    return displayNames[this.priority]
  }

  public getPriorityColor(): string {
    const colors = {
      [Priority.BAJA]: 'bg-green-100 text-green-800',
      [Priority.MEDIA]: 'bg-yellow-100 text-yellow-800',
      [Priority.ALTA]: 'bg-orange-100 text-orange-800',
      [Priority.URGENTE]: 'bg-red-100 text-red-800'
    }
    return colors[this.priority]
  }

  // Factory method
  static create(
    numeroOT: string,
    documentType: string,
    materia: string,
    notarioAsignado: string,
    comparecientes: Compareciente[],
    priority: Priority = Priority.MEDIA,
    fechaVencimiento?: Date
  ): OrdenTrabajo {
    return new OrdenTrabajo(
      OTId.generate(),
      numeroOT,
      DocumentTypeVO.fromString(documentType),
      materia,
      OTStatusVO.fromString('CREADA'),
      priority,
      new UserId(notarioAsignado),
      comparecientes,
      {},
      fechaVencimiento
    )
  }

  static getAvailablePriorities(): Array<{value: Priority, label: string, color: string}> {
    return Object.values(Priority).map(priority => {
      const ot = new OrdenTrabajo(
        OTId.generate(),
        'TEST',
        DocumentTypeVO.fromString('escritura_publica'),
        'Test materia for priority display',
        OTStatusVO.fromString('CREADA'),
        priority,
        new UserId('test'),
        []
      )
      return {
        value: priority,
        label: ot.getPriorityDisplayName(),
        color: ot.getPriorityColor()
      }
    })
  }
}
