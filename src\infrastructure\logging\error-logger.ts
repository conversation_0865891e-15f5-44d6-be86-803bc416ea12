// Infrastructure - Logging - Error Logger
import { randomUUID } from 'crypto'
import fs from 'fs/promises'
import path from 'path'

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum ErrorCategory {
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  DATABASE = 'DATABASE',
  EXTERNAL_API = 'EXTERNAL_API',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN'
}

export interface ErrorContext {
  userId?: string
  sessionId?: string
  userAgent?: string
  ip?: string
  url?: string
  method?: string
  requestId?: string
  additionalData?: Record<string, any>
}

export interface LoggedError {
  id: string
  timestamp: Date
  message: string
  stack?: string
  severity: ErrorSeverity
  category: ErrorCategory
  context: ErrorContext
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
}

export class ErrorLogger {
  private static instance: ErrorLogger
  private logDirectory: string

  private constructor() {
    this.logDirectory = path.join(process.cwd(), 'logs', 'errors')
    this.ensureLogDirectory()
  }

  public static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger()
    }
    return ErrorLogger.instance
  }

  private async ensureLogDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.logDirectory, { recursive: true })
    } catch (error) {
      console.error('Failed to create log directory:', error)
    }
  }

  public async logError(
    error: Error | string,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    context: ErrorContext = {}
  ): Promise<string> {
    const errorId = randomUUID()
    const timestamp = new Date()

    const loggedError: LoggedError = {
      id: errorId,
      timestamp,
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'string' ? undefined : error.stack,
      severity,
      category,
      context,
      resolved: false
    }

    try {
      // Log to file
      await this.writeToFile(loggedError)
      
      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        this.logToConsole(loggedError)
      }

      // For critical errors, could send to external monitoring service
      if (severity === ErrorSeverity.CRITICAL) {
        await this.handleCriticalError(loggedError)
      }

    } catch (logError) {
      console.error('Failed to log error:', logError)
    }

    return errorId
  }

  private async writeToFile(loggedError: LoggedError): Promise<void> {
    const fileName = `${loggedError.timestamp.toISOString().split('T')[0]}.log`
    const filePath = path.join(this.logDirectory, fileName)
    
    const logEntry = {
      ...loggedError,
      timestamp: loggedError.timestamp.toISOString()
    }

    const logLine = JSON.stringify(logEntry) + '\n'

    try {
      await fs.appendFile(filePath, logLine, 'utf8')
    } catch (error) {
      console.error('Failed to write to log file:', error)
    }
  }

  private logToConsole(loggedError: LoggedError): void {
    const colors = {
      [ErrorSeverity.LOW]: '\x1b[32m',      // Green
      [ErrorSeverity.MEDIUM]: '\x1b[33m',   // Yellow
      [ErrorSeverity.HIGH]: '\x1b[31m',     // Red
      [ErrorSeverity.CRITICAL]: '\x1b[35m'  // Magenta
    }

    const reset = '\x1b[0m'
    const color = colors[loggedError.severity]

    console.error(
      `${color}[${loggedError.severity}] ${loggedError.category} - ${loggedError.id}${reset}`
    )
    console.error(`Message: ${loggedError.message}`)
    if (loggedError.stack) {
      console.error(`Stack: ${loggedError.stack}`)
    }
    if (Object.keys(loggedError.context).length > 0) {
      console.error(`Context:`, loggedError.context)
    }
    console.error('---')
  }

  private async handleCriticalError(loggedError: LoggedError): Promise<void> {
    // Here you could integrate with external services like:
    // - Sentry
    // - Slack notifications
    // - Email alerts
    // - SMS alerts
    
    console.error('🚨 CRITICAL ERROR DETECTED:', loggedError.id)
    
    // For now, just ensure it's logged prominently
    const criticalLogPath = path.join(this.logDirectory, 'critical.log')
    const criticalEntry = {
      ...loggedError,
      timestamp: loggedError.timestamp.toISOString()
    }
    
    try {
      await fs.appendFile(
        criticalLogPath, 
        JSON.stringify(criticalEntry) + '\n', 
        'utf8'
      )
    } catch (error) {
      console.error('Failed to log critical error:', error)
    }
  }

  public async getErrorById(errorId: string): Promise<LoggedError | null> {
    try {
      const files = await fs.readdir(this.logDirectory)
      
      for (const file of files) {
        if (file.endsWith('.log')) {
          const filePath = path.join(this.logDirectory, file)
          const content = await fs.readFile(filePath, 'utf8')
          const lines = content.split('\n').filter(line => line.trim())
          
          for (const line of lines) {
            try {
              const logEntry = JSON.parse(line)
              if (logEntry.id === errorId) {
                return {
                  ...logEntry,
                  timestamp: new Date(logEntry.timestamp),
                  resolvedAt: logEntry.resolvedAt ? new Date(logEntry.resolvedAt) : undefined
                }
              }
            } catch (parseError) {
              // Skip invalid JSON lines
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to search for error:', error)
    }
    
    return null
  }

  public async markErrorAsResolved(
    errorId: string, 
    resolvedBy: string
  ): Promise<boolean> {
    // In a real implementation, you might want to update the log entry
    // For now, we'll just log the resolution
    const resolutionLog = {
      type: 'RESOLUTION',
      errorId,
      resolvedBy,
      resolvedAt: new Date().toISOString()
    }

    const resolutionPath = path.join(this.logDirectory, 'resolutions.log')
    
    try {
      await fs.appendFile(
        resolutionPath,
        JSON.stringify(resolutionLog) + '\n',
        'utf8'
      )
      return true
    } catch (error) {
      console.error('Failed to mark error as resolved:', error)
      return false
    }
  }
}

// Singleton instance
export const errorLogger = ErrorLogger.getInstance()
