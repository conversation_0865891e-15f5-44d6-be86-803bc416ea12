#!/usr/bin/env tsx

import { 
  LicenseData, 
  encryptLicenseData, 
  generateHardwareFingerprint,
  generateSampleLicense 
} from '../lib/license'
import crypto from 'crypto'

// Configuración de tipos de licencia
const LICENSE_TYPES = {
  trial: {
    maxUsers: 3,
    maxDocuments: 100,
    hasReports: false,
    hasBackup: false,
    hasApiAccess: false,
    hasCustomBranding: false,
    duration: 30 // días
  },
  standard: {
    maxUsers: 10,
    maxDocuments: 1000,
    hasReports: true,
    hasBackup: true,
    hasApiAccess: false,
    hasCustomBranding: true,
    duration: 365 // días
  },
  premium: {
    maxUsers: 25,
    maxDocuments: 5000,
    hasReports: true,
    hasBackup: true,
    hasApiAccess: true,
    hasCustomBranding: true,
    duration: 365 // días
  },
  enterprise: {
    maxUsers: -1, // ilimitado
    maxDocuments: -1, // ilimitado
    hasReports: true,
    hasBackup: true,
    hasApiAccess: true,
    hasCustomBranding: true,
    duration: 365 // días
  }
}

interface GenerateLicenseOptions {
  clientName: string
  clientEmail: string
  clientRut?: string
  licenseType: keyof typeof LICENSE_TYPES
  duration?: number // días personalizados
  output?: string // archivo de salida
}

function generateLicense(options: GenerateLicenseOptions): string {
  const licenseConfig = LICENSE_TYPES[options.licenseType]
  const now = new Date()
  const duration = options.duration || licenseConfig.duration
  const expiresAt = new Date(now.getTime() + duration * 24 * 60 * 60 * 1000)

  const licenseData: LicenseData = {
    clientId: crypto.randomUUID(),
    clientName: options.clientName,
    clientEmail: options.clientEmail,
    clientRut: options.clientRut,
    licenseKey: generateLicenseKey(),
    licenseType: options.licenseType,
    issuedAt: now.toISOString(),
    expiresAt: expiresAt.toISOString(),
    features: {
      maxUsers: licenseConfig.maxUsers,
      maxDocuments: licenseConfig.maxDocuments,
      hasReports: licenseConfig.hasReports,
      hasBackup: licenseConfig.hasBackup,
      hasApiAccess: licenseConfig.hasApiAccess,
      hasCustomBranding: licenseConfig.hasCustomBranding
    },
    systemInfo: {
      version: '1.0.0',
      installationId: crypto.randomUUID(),
      // No incluir fingerprint en la generación inicial
    }
  }

  return encryptLicenseData(licenseData)
}

function generateLicenseKey(): string {
  // Generar una clave de licencia con formato: XXXX-XXXX-XXXX-XXXX
  const segments = []
  for (let i = 0; i < 4; i++) {
    segments.push(crypto.randomBytes(2).toString('hex').toUpperCase())
  }
  return segments.join('-')
}

function displayLicenseInfo(encryptedLicense: string, options: GenerateLicenseOptions) {
  const licenseConfig = LICENSE_TYPES[options.licenseType]
  const duration = options.duration || licenseConfig.duration
  
  console.log('\n🔐 LICENCIA e-NOTARIA GENERADA EXITOSAMENTE')
  console.log('=' .repeat(50))
  console.log(`Cliente: ${options.clientName}`)
  console.log(`Email: ${options.clientEmail}`)
  if (options.clientRut) {
    console.log(`RUT: ${options.clientRut}`)
  }
  console.log(`Tipo: ${options.licenseType.toUpperCase()}`)
  console.log(`Duración: ${duration} días`)
  console.log(`Usuarios máx: ${licenseConfig.maxUsers === -1 ? 'Ilimitado' : licenseConfig.maxUsers}`)
  console.log(`Documentos máx: ${licenseConfig.maxDocuments === -1 ? 'Ilimitado' : licenseConfig.maxDocuments}`)
  console.log(`Reportes: ${licenseConfig.hasReports ? 'Sí' : 'No'}`)
  console.log(`Backup: ${licenseConfig.hasBackup ? 'Sí' : 'No'}`)
  console.log(`API: ${licenseConfig.hasApiAccess ? 'Sí' : 'No'}`)
  console.log(`Branding personalizado: ${licenseConfig.hasCustomBranding ? 'Sí' : 'No'}`)
  console.log('\n📄 LICENCIA CIFRADA:')
  console.log('-' .repeat(50))
  console.log(encryptedLicense)
  console.log('-' .repeat(50))
  console.log('\n💡 INSTRUCCIONES:')
  console.log('1. Copie la licencia cifrada completa')
  console.log('2. En e-NOTARIA, vaya a Licencias > Validar Licencia')
  console.log('3. Pegue la licencia cifrada y haga clic en "Validar"')
  console.log('4. La licencia se guardará automáticamente si es válida')
}

async function saveLicenseToFile(encryptedLicense: string, filename: string) {
  const fs = require('fs').promises
  const path = require('path')
  
  const fullPath = path.resolve(filename)
  await fs.writeFile(fullPath, encryptedLicense, 'utf8')
  console.log(`\n💾 Licencia guardada en: ${fullPath}`)
}

// Función principal
async function main() {
  const args = process.argv.slice(2)
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    console.log(`
🔐 Generador de Licencias e-NOTARIA

Uso:
  npm run generate-license -- [opciones]

Opciones:
  --name <nombre>        Nombre del cliente (requerido)
  --email <email>        Email del cliente (requerido)
  --rut <rut>           RUT del cliente (opcional)
  --type <tipo>         Tipo de licencia: trial, standard, premium, enterprise (default: standard)
  --duration <días>     Duración en días (opcional, usa el default del tipo)
  --output <archivo>    Guardar en archivo (opcional)
  --sample             Generar licencia de muestra rápida

Ejemplos:
  npm run generate-license -- --name "Notaría San Miguel" --email "<EMAIL>" --type premium
  npm run generate-license -- --name "Notaría Prueba" --email "<EMAIL>" --type trial --duration 15
  npm run generate-license -- --sample

Tipos de licencia disponibles:
  trial      - 3 usuarios, 100 docs, 30 días, funciones básicas
  standard   - 10 usuarios, 1000 docs, 1 año, reportes + backup
  premium    - 25 usuarios, 5000 docs, 1 año, todas las funciones
  enterprise - Ilimitado, 1 año, todas las funciones
`)
    return
  }

  // Generar licencia de muestra rápida
  if (args.includes('--sample')) {
    console.log('Generando licencia de muestra...')
    const sampleLicense = generateSampleLicense('Notaría de Prueba', '<EMAIL>')
    const encrypted = encryptLicenseData(sampleLicense)
    
    console.log('\n🔐 LICENCIA DE MUESTRA GENERADA')
    console.log('=' .repeat(50))
    console.log('Cliente: Notaría de Prueba')
    console.log('Email: <EMAIL>')
    console.log('Tipo: STANDARD')
    console.log('Clave:', sampleLicense.licenseKey)
    console.log('\n📄 LICENCIA CIFRADA:')
    console.log('-' .repeat(50))
    console.log(encrypted)
    console.log('-' .repeat(50))
    return
  }

  // Parsear argumentos
  const options: Partial<GenerateLicenseOptions> = {}
  
  for (let i = 0; i < args.length; i += 2) {
    const flag = args[i]
    const value = args[i + 1]
    
    switch (flag) {
      case '--name':
        options.clientName = value
        break
      case '--email':
        options.clientEmail = value
        break
      case '--rut':
        options.clientRut = value
        break
      case '--type':
        if (!LICENSE_TYPES[value as keyof typeof LICENSE_TYPES]) {
          console.error(`❌ Tipo de licencia inválido: ${value}`)
          console.error(`Tipos válidos: ${Object.keys(LICENSE_TYPES).join(', ')}`)
          return
        }
        options.licenseType = value as keyof typeof LICENSE_TYPES
        break
      case '--duration':
        options.duration = parseInt(value)
        if (isNaN(options.duration) || options.duration <= 0) {
          console.error(`❌ Duración inválida: ${value}`)
          return
        }
        break
      case '--output':
        options.output = value
        break
    }
  }

  // Validar argumentos requeridos
  if (!options.clientName) {
    console.error('❌ Error: --name es requerido')
    return
  }
  
  if (!options.clientEmail) {
    console.error('❌ Error: --email es requerido')
    return
  }
  
  if (!options.licenseType) {
    options.licenseType = 'standard'
  }

  try {
    console.log('Generando licencia...')
    const encryptedLicense = generateLicense(options as GenerateLicenseOptions)
    
    displayLicenseInfo(encryptedLicense, options as GenerateLicenseOptions)
    
    if (options.output) {
      await saveLicenseToFile(encryptedLicense, options.output)
    }
    
    console.log('\n✅ Licencia generada exitosamente!')
    
  } catch (error) {
    console.error('❌ Error al generar la licencia:', error.message)
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main().catch(console.error)
}
