// Domain - Shared - Base Entity
import { Id } from '../value-objects/id'

export abstract class BaseEntity<T extends Id> {
  protected readonly id: T
  protected readonly createdAt: Date
  protected updatedAt: Date

  constructor(id: T, createdAt?: Date, updatedAt?: Date) {
    this.id = id
    this.createdAt = createdAt || new Date()
    this.updatedAt = updatedAt || new Date()
  }

  public getId(): T {
    return this.id
  }

  public getCreatedAt(): Date {
    return this.createdAt
  }

  public getUpdatedAt(): Date {
    return this.updatedAt
  }

  protected updateTimestamp(): void {
    this.updatedAt = new Date()
  }

  public equals(other: BaseEntity<T>): boolean {
    return this.id.equals(other.id)
  }
}
