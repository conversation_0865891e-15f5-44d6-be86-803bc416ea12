// <PERSON>ript to initialize permissions and roles in the database
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function initializePermissions() {
  console.log('🚀 Initializing permissions and roles...')

  try {
    // Create permissions
    const permissions = [
      // Dashboard
      { name: 'dashboard:view', module: 'dashboard', description: 'Ver dashboard' },
      { name: 'dashboard:stats', module: 'dashboard', description: 'Ver estadísticas' },
      
      // OT
      { name: 'ot:view', module: 'ot', description: 'Ver órdenes de trabajo' },
      { name: 'ot:create', module: 'ot', description: 'Crear órdenes de trabajo' },
      { name: 'ot:edit', module: 'ot', description: 'Editar órdenes de trabajo' },
      { name: 'ot:delete', module: 'ot', description: 'Eliminar órdenes de trabajo' },
      { name: 'ot:assign', module: 'ot', description: 'Asignar órdenes de trabajo' },
      { name: 'ot:change_status', module: 'ot', description: '<PERSON>biar estado de OT' },
      
      // Documentos
      { name: 'documentos:view', module: 'documentos', description: 'Ver documentos' },
      { name: 'documentos:create', module: 'documentos', description: 'Crear documentos' },
      { name: 'documentos:edit', module: 'documentos', description: 'Editar documentos' },
      { name: 'documentos:delete', module: 'documentos', description: 'Eliminar documentos' },
      { name: 'documentos:sign', module: 'documentos', description: 'Firmar documentos' },
      { name: 'documentos:export', module: 'documentos', description: 'Exportar documentos' },
      
      // Usuarios
      { name: 'usuarios:view', module: 'usuarios', description: 'Ver usuarios' },
      { name: 'usuarios:create', module: 'usuarios', description: 'Crear usuarios' },
      { name: 'usuarios:edit', module: 'usuarios', description: 'Editar usuarios' },
      { name: 'usuarios:delete', module: 'usuarios', description: 'Eliminar usuarios' },
      { name: 'usuarios:assign_roles', module: 'usuarios', description: 'Asignar roles' },
      
      // Licencias
      { name: 'licencias:view', module: 'licencias', description: 'Ver licencias' },
      { name: 'licencias:validate', module: 'licencias', description: 'Validar licencias' },
      { name: 'licencias:manage', module: 'licencias', description: 'Gestionar licencias' },
      
      // Reportes
      { name: 'reportes:view', module: 'reportes', description: 'Ver reportes' },
      { name: 'reportes:export', module: 'reportes', description: 'Exportar reportes' },
      { name: 'reportes:advanced', module: 'reportes', description: 'Reportes avanzados' },
      
      // Caja
      { name: 'caja:view', module: 'caja', description: 'Ver caja' },
      { name: 'caja:create', module: 'caja', description: 'Crear movimientos de caja' },
      { name: 'caja:edit', module: 'caja', description: 'Editar movimientos de caja' },
      { name: 'caja:close', module: 'caja', description: 'Cerrar caja' },
      
      // Configuración
      { name: 'configuracion:view', module: 'configuracion', description: 'Ver configuración' },
      { name: 'configuracion:edit', module: 'configuracion', description: 'Editar configuración' }
    ]

    console.log('📝 Creating permissions...')
    for (const permission of permissions) {
      await prisma.permission.upsert({
        where: { name: permission.name },
        update: permission,
        create: permission
      })
    }

    // Create roles
    const roles = [
      { name: 'ADMIN', description: 'Administrador del sistema' },
      { name: 'NOTARIO', description: 'Notario' },
      { name: 'USUARIO', description: 'Usuario estándar' },
      { name: 'CONSULTA', description: 'Solo consulta' }
    ]

    console.log('👥 Creating roles...')
    for (const role of roles) {
      await prisma.role.upsert({
        where: { name: role.name },
        update: role,
        create: role
      })
    }

    // Assign permissions to roles
    console.log('🔗 Assigning permissions to roles...')

    // ADMIN - All permissions
    const adminRole = await prisma.role.findUnique({ where: { name: 'ADMIN' } })
    const allPermissions = await prisma.permission.findMany()
    
    if (adminRole) {
      // Clear existing permissions
      await prisma.rolePermission.deleteMany({
        where: { roleId: adminRole.id }
      })
      
      // Add all permissions
      for (const permission of allPermissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: adminRole.id,
            permissionId: permission.id
          }
        })
      }
    }

    // NOTARIO - Most permissions except user management
    const notarioRole = await prisma.role.findUnique({ where: { name: 'NOTARIO' } })
    const notarioPermissions = allPermissions.filter(p => 
      !p.name.startsWith('usuarios:') || p.name === 'usuarios:view'
    )
    
    if (notarioRole) {
      await prisma.rolePermission.deleteMany({
        where: { roleId: notarioRole.id }
      })
      
      for (const permission of notarioPermissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: notarioRole.id,
            permissionId: permission.id
          }
        })
      }
    }

    // USUARIO - Limited permissions
    const usuarioRole = await prisma.role.findUnique({ where: { name: 'USUARIO' } })
    const usuarioPermissions = allPermissions.filter(p => 
      p.name.includes(':view') || p.name === 'reportes:export'
    )
    
    if (usuarioRole) {
      await prisma.rolePermission.deleteMany({
        where: { roleId: usuarioRole.id }
      })
      
      for (const permission of usuarioPermissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: usuarioRole.id,
            permissionId: permission.id
          }
        })
      }
    }

    // CONSULTA - Only view permissions
    const consultaRole = await prisma.role.findUnique({ where: { name: 'CONSULTA' } })
    const consultaPermissions = allPermissions.filter(p => 
      p.name.includes(':view') && !p.name.includes('usuarios:view')
    )
    
    if (consultaRole) {
      await prisma.rolePermission.deleteMany({
        where: { roleId: consultaRole.id }
      })
      
      for (const permission of consultaPermissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: consultaRole.id,
            permissionId: permission.id
          }
        })
      }
    }

    // Assign ADMIN role to ADMIN user
    console.log('👤 Assigning roles to users...')
    const adminUser = await prisma.user.findUnique({ where: { username: 'ADMIN' } })
    if (adminUser && adminRole) {
      await prisma.userRole.upsert({
        where: {
          userId_roleId: {
            userId: adminUser.id,
            roleId: adminRole.id
          }
        },
        update: {},
        create: {
          userId: adminUser.id,
          roleId: adminRole.id
        }
      })
    }

    // Assign USUARIO role to testuser
    const testUser = await prisma.user.findUnique({ where: { username: 'TESTUSER' } })
    if (testUser && usuarioRole) {
      await prisma.userRole.upsert({
        where: {
          userId_roleId: {
            userId: testUser.id,
            roleId: usuarioRole.id
          }
        },
        update: {},
        create: {
          userId: testUser.id,
          roleId: usuarioRole.id
        }
      })
    }

    console.log('✅ Permissions and roles initialized successfully!')
    console.log(`📊 Created ${permissions.length} permissions`)
    console.log(`👥 Created ${roles.length} roles`)
    console.log('🔐 ADMIN user has full access')
    console.log('👤 TESTUSER has limited access')

  } catch (error) {
    console.error('❌ Error initializing permissions:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
if (require.main === module) {
  initializePermissions()
    .then(() => {
      console.log('🎉 Initialization completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Initialization failed:', error)
      process.exit(1)
    })
}

export { initializePermissions }
