// Domain - License - Entities
import { BaseEntity } from '../../shared/entities/base-entity'
import { LicenseId } from '../../shared/value-objects/id'
import { Email } from '../../shared/value-objects/email'

export enum LicenseType {
  BASIC = 'BASIC',
  STANDARD = 'STANDARD',
  PREMIUM = 'PREMIUM',
  ENTERPRISE = 'ENTERPRISE'
}

export enum LicenseStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  SUSPENDED = 'SUSPENDED',
  REVOKED = 'REVOKED'
}

export interface SystemInfo {
  installationId: string
  hardwareFingerprint: string
  version: string
  platform: string
}

export interface LicenseFeatures {
  maxUsers: number
  maxDocuments: number
  hasAdvancedReports: boolean
  hasApiAccess: boolean
  hasCustomBranding: boolean
}

export class License extends BaseEntity<LicenseId> {
  private clientName: string
  private clientEmail: Email
  private licenseKey: string
  private type: LicenseType
  private status: LicenseStatus
  private features: LicenseFeatures
  private systemInfo: SystemInfo
  private issuedAt: Date
  private expiresAt: Date
  private lastValidatedAt?: Date

  constructor(
    id: LicenseId,
    clientName: string,
    clientEmail: Email,
    licenseKey: string,
    type: LicenseType,
    status: LicenseStatus,
    features: LicenseFeatures,
    systemInfo: SystemInfo,
    issuedAt: Date,
    expiresAt: Date,
    createdAt?: Date,
    updatedAt?: Date,
    lastValidatedAt?: Date
  ) {
    super(id, createdAt, updatedAt)
    this.clientName = clientName
    this.clientEmail = clientEmail
    this.licenseKey = licenseKey
    this.type = type
    this.status = status
    this.features = features
    this.systemInfo = systemInfo
    this.issuedAt = issuedAt
    this.expiresAt = expiresAt
    this.lastValidatedAt = lastValidatedAt
  }

  // Getters
  public getClientName(): string {
    return this.clientName
  }

  public getClientEmail(): Email {
    return this.clientEmail
  }

  public getLicenseKey(): string {
    return this.licenseKey
  }

  public getType(): LicenseType {
    return this.type
  }

  public getStatus(): LicenseStatus {
    return this.status
  }

  public getFeatures(): LicenseFeatures {
    return this.features
  }

  public getSystemInfo(): SystemInfo {
    return this.systemInfo
  }

  public getIssuedAt(): Date {
    return this.issuedAt
  }

  public getExpiresAt(): Date {
    return this.expiresAt
  }

  public getLastValidatedAt(): Date | undefined {
    return this.lastValidatedAt
  }

  // Business methods
  public isActive(): boolean {
    return this.status === LicenseStatus.ACTIVE && !this.isExpired()
  }

  public isExpired(): boolean {
    return new Date() > this.expiresAt
  }

  public isValidForSystem(systemInfo: SystemInfo): boolean {
    return this.systemInfo.installationId === systemInfo.installationId &&
           this.systemInfo.hardwareFingerprint === systemInfo.hardwareFingerprint
  }

  public validate(): void {
    if (this.isExpired()) {
      this.status = LicenseStatus.EXPIRED
    }
    this.lastValidatedAt = new Date()
    this.updateTimestamp()
  }

  public suspend(): void {
    this.status = LicenseStatus.SUSPENDED
    this.updateTimestamp()
  }

  public revoke(): void {
    this.status = LicenseStatus.REVOKED
    this.updateTimestamp()
  }

  public reactivate(): void {
    if (!this.isExpired()) {
      this.status = LicenseStatus.ACTIVE
      this.updateTimestamp()
    }
  }

  // Factory method
  static create(
    clientName: string,
    clientEmail: string,
    licenseKey: string,
    type: LicenseType,
    features: LicenseFeatures,
    systemInfo: SystemInfo,
    validityDays: number = 365
  ): License {
    const now = new Date()
    const expiresAt = new Date(now.getTime() + (validityDays * 24 * 60 * 60 * 1000))

    return new License(
      LicenseId.generate(),
      clientName,
      new Email(clientEmail),
      licenseKey,
      type,
      LicenseStatus.ACTIVE,
      features,
      systemInfo,
      now,
      expiresAt
    )
  }
}
