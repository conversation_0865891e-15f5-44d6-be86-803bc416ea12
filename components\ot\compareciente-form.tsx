"use client"

import { useState } from "react"

interface Calidad {
  value: string
  label: string
}

interface Compareciente {
  id: string
  rut: string
  nombres: string
  apellidoPaterno: string
  apellidoMaterno: string
  calidad: string
  esChileno: boolean
}

interface ComparecienteFormProps {
  calidades: Calidad[]
  onAdd: (compareciente: Omit<Compareciente, 'id'>) => void
  comparecientes: Compareciente[]
  onRemove: (id: string) => void
}

export function ComparecienteForm({ calidades, onAdd, comparecientes, onRemove }: ComparecienteFormProps) {
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState({
    rut: '',
    nombres: '',
    apellidoPaterno: '',
    apellidoMaterno: '',
    calidad: '',
    esChileno: true
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateRUT = (rut: string): boolean => {
    const cleanRut = rut.replace(/[^0-9kK]/g, '').toUpperCase()
    
    if (cleanRut.length < 8 || cleanRut.length > 9) {
      return false
    }

    const body = cleanRut.slice(0, -1)
    const dv = cleanRut.slice(-1)

    if (!/^\d+$/.test(body)) {
      return false
    }

    return calculateDV(body) === dv
  }

  const calculateDV = (rut: string): string => {
    let sum = 0
    let multiplier = 2

    for (let i = rut.length - 1; i >= 0; i--) {
      sum += parseInt(rut[i]) * multiplier
      multiplier = multiplier === 7 ? 2 : multiplier + 1
    }

    const remainder = sum % 11
    const dv = 11 - remainder

    if (dv === 11) return '0'
    if (dv === 10) return 'K'
    return dv.toString()
  }

  const formatRUT = (rut: string): string => {
    const cleanRut = rut.replace(/[^0-9kK]/g, '').toUpperCase()
    if (cleanRut.length <= 1) return cleanRut
    
    const body = cleanRut.slice(0, -1)
    const dv = cleanRut.slice(-1)
    
    const formattedBody = body.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
    return `${formattedBody}-${dv}`
  }

  const handleRutChange = (value: string) => {
    const cleanValue = value.replace(/[^0-9kK]/g, '').toUpperCase()
    setFormData({ ...formData, rut: cleanValue })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const newErrors: Record<string, string> = {}
    
    // Validations
    if (!formData.rut) {
      newErrors.rut = 'RUT es requerido'
    } else if (formData.esChileno && !validateRUT(formData.rut)) {
      newErrors.rut = 'RUT inválido'
    }
    
    if (!formData.nombres.trim()) {
      newErrors.nombres = 'Nombres son requeridos'
    }
    
    if (!formData.apellidoPaterno.trim()) {
      newErrors.apellidoPaterno = 'Apellido paterno es requerido'
    }
    
    if (formData.esChileno && !formData.apellidoMaterno.trim()) {
      newErrors.apellidoMaterno = 'Apellido materno es requerido para chilenos'
    }
    
    if (!formData.calidad) {
      newErrors.calidad = 'Calidad es requerida'
    }

    // Check if RUT already exists
    const rutExists = comparecientes.some(c => c.rut === formData.rut)
    if (rutExists) {
      newErrors.rut = 'Este RUT ya está agregado'
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    onAdd(formData)
    setFormData({
      rut: '',
      nombres: '',
      apellidoPaterno: '',
      apellidoMaterno: '',
      calidad: '',
      esChileno: true
    })
    setErrors({})
    setShowForm(false)
  }

  const handleCancel = () => {
    setFormData({
      rut: '',
      nombres: '',
      apellidoPaterno: '',
      apellidoMaterno: '',
      calidad: '',
      esChileno: true
    })
    setErrors({})
    setShowForm(false)
  }

  return (
    <div className="space-y-4">
      {/* Existing Comparecientes */}
      {comparecientes.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Comparecientes Agregados</h4>
          {comparecientes.map((comp) => (
            <div key={comp.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
              <div className="flex-1">
                <div className="flex items-center space-x-4">
                  <div>
                    <p className="font-medium text-gray-900">
                      {comp.nombres} {comp.apellidoPaterno} {comp.apellidoMaterno}
                    </p>
                    <p className="text-sm text-gray-600">
                      {formatRUT(comp.rut)} • {calidades.find(c => c.value === comp.calidad)?.label}
                      {!comp.esChileno && ' • Extranjero'}
                    </p>
                  </div>
                </div>
              </div>
              <button
                type="button"
                onClick={() => onRemove(comp.id)}
                className="ml-4 text-red-600 hover:text-red-800 focus:outline-none"
                title="Eliminar compareciente"
              >
                <span className="sr-only">Eliminar</span>
                ✗
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Add Button */}
      {!showForm && (
        <button
          type="button"
          onClick={() => setShowForm(true)}
          className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <span className="text-2xl">+</span>
          <p className="mt-1">Agregar Compareciente</p>
        </button>
      )}

      {/* Add Form */}
      {showForm && (
        <div className="p-6 border border-gray-200 rounded-lg bg-white">
          <h4 className="font-medium text-gray-900 mb-4">Nuevo Compareciente</h4>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Nacionalidad */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nacionalidad
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={formData.esChileno}
                    onChange={() => setFormData({ ...formData, esChileno: true, apellidoMaterno: '' })}
                    className="mr-2"
                  />
                  Chileno
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={!formData.esChileno}
                    onChange={() => setFormData({ ...formData, esChileno: false })}
                    className="mr-2"
                  />
                  Extranjero
                </label>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* RUT */}
              <div>
                <label htmlFor="rut" className="block text-sm font-medium text-gray-700 mb-1">
                  {formData.esChileno ? 'RUT' : 'Identificación'} *
                </label>
                <input
                  type="text"
                  id="rut"
                  value={formData.esChileno ? formatRUT(formData.rut) : formData.rut}
                  onChange={(e) => handleRutChange(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.rut ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder={formData.esChileno ? "12.345.678-9" : "Número de identificación"}
                  required
                />
                {errors.rut && <p className="mt-1 text-sm text-red-600">{errors.rut}</p>}
              </div>

              {/* Calidad */}
              <div>
                <label htmlFor="calidad" className="block text-sm font-medium text-gray-700 mb-1">
                  Calidad *
                </label>
                <select
                  id="calidad"
                  value={formData.calidad}
                  onChange={(e) => setFormData({ ...formData, calidad: e.target.value })}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.calidad ? 'border-red-300' : 'border-gray-300'
                  }`}
                  required
                >
                  <option value="">Seleccionar...</option>
                  {calidades.map((calidad) => (
                    <option key={calidad.value} value={calidad.value}>
                      {calidad.label}
                    </option>
                  ))}
                </select>
                {errors.calidad && <p className="mt-1 text-sm text-red-600">{errors.calidad}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Nombres */}
              <div>
                <label htmlFor="nombres" className="block text-sm font-medium text-gray-700 mb-1">
                  Nombres *
                </label>
                <input
                  type="text"
                  id="nombres"
                  value={formData.nombres}
                  onChange={(e) => setFormData({ ...formData, nombres: e.target.value })}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.nombres ? 'border-red-300' : 'border-gray-300'
                  }`}
                  required
                />
                {errors.nombres && <p className="mt-1 text-sm text-red-600">{errors.nombres}</p>}
              </div>

              {/* Apellido Paterno */}
              <div>
                <label htmlFor="apellidoPaterno" className="block text-sm font-medium text-gray-700 mb-1">
                  Apellido Paterno *
                </label>
                <input
                  type="text"
                  id="apellidoPaterno"
                  value={formData.apellidoPaterno}
                  onChange={(e) => setFormData({ ...formData, apellidoPaterno: e.target.value })}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.apellidoPaterno ? 'border-red-300' : 'border-gray-300'
                  }`}
                  required
                />
                {errors.apellidoPaterno && <p className="mt-1 text-sm text-red-600">{errors.apellidoPaterno}</p>}
              </div>

              {/* Apellido Materno */}
              <div>
                <label htmlFor="apellidoMaterno" className="block text-sm font-medium text-gray-700 mb-1">
                  Apellido Materno {formData.esChileno && '*'}
                </label>
                <input
                  type="text"
                  id="apellidoMaterno"
                  value={formData.apellidoMaterno}
                  onChange={(e) => setFormData({ ...formData, apellidoMaterno: e.target.value })}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.apellidoMaterno ? 'border-red-300' : 'border-gray-300'
                  }`}
                  required={formData.esChileno}
                />
                {errors.apellidoMaterno && <p className="mt-1 text-sm text-red-600">{errors.apellidoMaterno}</p>}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Agregar
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  )
}
