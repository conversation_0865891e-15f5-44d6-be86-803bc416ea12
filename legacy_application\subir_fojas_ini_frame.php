<?php
session_start();
ini_set("memory_limit","2024M");
define('TINYAJAX_PATH', '/calls');
require_once(TINYAJAX_PATH . "/TinyAjax.php"); 
require_once("calls/lib.php");
require_once("calls/soap/nusoap.php");	
require_once 'calls/PDFMerger.php';		



	function detalle_firma_return( )
	{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$link_firma=$conect->Conectarse_firma();
		$ff= new fechas();
		$ii2 = 0; $ii = 0;
		$r222 = "SELECT numero_certificado as numero_firma  FROM certificados_firmados ORDER BY LENGTH(datos) ASC;";
		$fin2222=mysqli_query($link_firma, $r222);
		while($row = mysqli_fetch_array($fin2222))
			{
			$ii++;
			$r2_dd2 = "SELECT  id, tipo, caratula, numero_firma, libro, folio_real, foja, numero, anho, hora, usuario, revisor  FROM certificados_listo WHERE numero_firma = '".$row["numero_firma"]."'  ";
			$fin_dd2=mysqli_query($link, $r2_dd2);
			$row2 = mysqli_fetch_array($fin_dd2);
			if($row2["numero_firma"]=="")
				{		
				$r2_dd = "SELECT  id, tipo, caratula, numero_firma, libro, folio_real, foja, numero, anho, hora, usuario, revisor  FROM certificados_firmas WHERE numero_firma = '".$row["numero_firma"]."'  ";
				$fin_dd=mysqli_query($link, $r2_dd);
				$row2 = mysqli_fetch_array($fin_dd);
				$img="<img src='imagenes/vig_n.jpg'>";
				}
			else
				{
				$img="<img src='imagenes/vig_s.jpg'>";
				$ii2++;
				}


			$dv.="
				<tr>
				<td class='td1' >$ii</td>
				<td class='td1' ><div align='center' >$img</div></td>
				<td class='td1' align='center'>".$row2["numero_firma"]."</td>";
				$mms="";
				$r_e = "SELECT * FROM certificados_metadata WHERE id_certificado='".$row["numero_firma"]."'  ";
				$fin_e=mysqli_query($link, $r_e);			
				$row_e = mysqli_fetch_array($fin_e);

				$dv.="<td class='td1'><a href='calls/ver_pdf.php?iadsfd=".$row["numero_firma"]."&org=d8fsu89fhfg8' >".$row2["tipo"]."</td>";


			
			
			
			}
			
			$quedan = $ii - $ii2;
			
			$dv2="
			<table class='table1' width='100%' align='center' border='1'>
			<tr>";
			if( intval($ii) == intval($ii2) )
				{$dv2.="<td class='headtablac' colspan='3' >DOCUMENTOS LISTOS EN FOJAS.CL <img src='imagenes/vig_s.jpg'> </td><td class='headtablac' ></td>";}
			else
				{$dv2.="<td class='headtablac' colspan='3' >SUBIENDO A FOJAS.CL ( Restantes $quedan de $ii ) - ".date("H:i:s")."</td><td class='headtablac' ></td>";}
			$dv2.="<tr>
			<td class='headtablac'  ></td>
			<td class='headtablac'  ></td>
			<td class='headtabla'  >N� Doc</div></td>
			<td class='headtabla' ><div align='left'>Documento</div></td>

			
			</tr>
			<tr>".$dv."</table><br>";;
			
			
		return $dv2;	
	}	

	function detalle_firma_reload( )
		{
		$dv = detalle_firma_return();
		$tab = new TinyAjaxBehavior();	
		$tab->add( TabInnerHtml::getBehavior("div_detalle", $dv));
		return $tab->getString();	
		}			

	
	$ajax = new TinyAjax();   
	$ajax->showLoading();
	$ajax->exportFunction("detalle_firma_reload");    
	$ajax->process();	
	echo"<html>
	<head>";
	echo"<meta http-equiv='content-type' content='text/html; charset=ISO-8859-1'>";
	$ajax->drawJavaScript(); 
	echo"
	<title>Sign</title>";

	echo"<script language=\"javascript\" type=\"text/javascript\" >
	window.setInterval(\"detalle_firma_reload() \",5000); 
	</script>";






	echo "</head>
	<script language='javascript' type='text/javascript' src='calls/libreria_java.js'></script>";
	$ddtt= new datos;
	$conect= new ingreso;
	$link=$conect->Conectarse();
	$linkfs=$conect->Coneccion_fs();
	$dis = new diseno;
	$dd = new datos;
	$dis->ingresar_body("doc_elect");

	
	ECHO"<div id='container'>
	<table class='container_table'>
		<tr>
			<td width='440' valign='top'>
			<div class='page-header-home2'><span class='header2'>".$msg_ini."</span></div>
		<div id='central'> ";  

	$conect= new ingreso;
	$link=$conect->Conectarse();
	$usur = new usuario;	
	$dis = new diseno();

	$ddv=detalle_firma_return();
	echo"
	<div id='cuerpo_centro'>
<div id='div_detalle'>".$ddv."</div>

</div>

</body></html>";





	$cons= new diseno;
	$cons->ingresar_fin();
	