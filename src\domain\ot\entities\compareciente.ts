// Domain - OT - Entities
import { RUT } from '../value-objects/rut'

export enum ComparecienteQuality {
  COMPRADOR = 'COMPRADOR',
  VENDEDOR = 'VENDEDOR',
  BANCO = 'BANCO',
  MANDANTE = 'MANDANTE',
  MANDATARIO = 'MANDATARIO',
  TESTIGO = 'TESTIGO',
  NOTARIO = 'NOTARIO',
  OTRO = 'OTRO'
}

export class Compareciente {
  private rut: RUT
  private nombres: string
  private apellidoPaterno: string
  private apellidoMaterno: string
  private calidad: ComparecienteQuality
  private esChileno: boolean

  constructor(
    rut: RUT,
    nombres: string,
    apellidoPaterno: string,
    apellidoMaterno: string,
    calidad: ComparecienteQuality,
    esChileno: boolean = true
  ) {
    this.validateNames(nombres, apellidoPaterno, apellidoMaterno, esChileno)
    
    this.rut = rut
    this.nombres = nombres.trim().toUpperCase()
    this.apellidoPaterno = apellidoPaterno.trim().toUpperCase()
    this.apellidoMaterno = apellidoMaterno.trim().toUpperCase()
    this.calidad = calidad
    this.esChileno = esChileno
  }

  private validateNames(nombres: string, apellidoPaterno: string, apellidoMaterno: string, esChileno: boolean): void {
    if (!nombres || nombres.trim().length === 0) {
      throw new Error('Nombres are required')
    }

    if (!apellidoPaterno || apellidoPaterno.trim().length === 0) {
      throw new Error('Apellido paterno is required')
    }

    // Para chilenos, apellido materno es obligatorio, para extranjeros es opcional
    if (esChileno && (!apellidoMaterno || apellidoMaterno.trim().length === 0)) {
      throw new Error('Apellido materno is required for Chilean citizens')
    }
  }

  // Getters
  public getRut(): RUT {
    return this.rut
  }

  public getNombres(): string {
    return this.nombres
  }

  public getApellidoPaterno(): string {
    return this.apellidoPaterno
  }

  public getApellidoMaterno(): string {
    return this.apellidoMaterno
  }

  public getCalidad(): ComparecienteQuality {
    return this.calidad
  }

  public isChileno(): boolean {
    return this.esChileno
  }

  public getFullName(): string {
    if (this.esChileno) {
      return `${this.nombres} ${this.apellidoPaterno} ${this.apellidoMaterno}`.trim()
    } else {
      return `${this.nombres} ${this.apellidoPaterno}${this.apellidoMaterno ? ' ' + this.apellidoMaterno : ''}`.trim()
    }
  }

  public getCalidadDisplayName(): string {
    const displayNames = {
      [ComparecienteQuality.COMPRADOR]: 'Comprador',
      [ComparecienteQuality.VENDEDOR]: 'Vendedor',
      [ComparecienteQuality.BANCO]: 'Banco',
      [ComparecienteQuality.MANDANTE]: 'Mandante',
      [ComparecienteQuality.MANDATARIO]: 'Mandatario',
      [ComparecienteQuality.TESTIGO]: 'Testigo',
      [ComparecienteQuality.NOTARIO]: 'Notario',
      [ComparecienteQuality.OTRO]: 'Otro'
    }
    return displayNames[this.calidad]
  }

  // Business methods
  public updateCalidad(newCalidad: ComparecienteQuality): void {
    this.calidad = newCalidad
  }

  public updatePersonalInfo(nombres: string, apellidoPaterno: string, apellidoMaterno: string): void {
    this.validateNames(nombres, apellidoPaterno, apellidoMaterno, this.esChileno)
    this.nombres = nombres.trim().toUpperCase()
    this.apellidoPaterno = apellidoPaterno.trim().toUpperCase()
    this.apellidoMaterno = apellidoMaterno.trim().toUpperCase()
  }

  public equals(other: Compareciente): boolean {
    return this.rut.equals(other.rut) && this.calidad === other.calidad
  }

  // Factory methods
  static create(
    rutValue: string,
    nombres: string,
    apellidoPaterno: string,
    apellidoMaterno: string,
    calidad: ComparecienteQuality,
    esChileno: boolean = true
  ): Compareciente {
    const rut = esChileno ? RUT.createChilean(rutValue) : RUT.createForeign(rutValue)
    return new Compareciente(rut, nombres, apellidoPaterno, apellidoMaterno, calidad, esChileno)
  }

  static getAvailableQualities(): Array<{value: ComparecienteQuality, label: string}> {
    const displayNames = {
      [ComparecienteQuality.COMPRADOR]: 'Comprador',
      [ComparecienteQuality.VENDEDOR]: 'Vendedor',
      [ComparecienteQuality.BANCO]: 'Banco',
      [ComparecienteQuality.MANDANTE]: 'Mandante',
      [ComparecienteQuality.MANDATARIO]: 'Mandatario',
      [ComparecienteQuality.TESTIGO]: 'Testigo',
      [ComparecienteQuality.NOTARIO]: 'Notario',
      [ComparecienteQuality.OTRO]: 'Otro'
    }

    return Object.values(ComparecienteQuality).map(quality => ({
      value: quality,
      label: displayNames[quality]
    }))
  }
}
