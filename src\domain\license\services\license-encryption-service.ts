// Domain - License - Services
import crypto from 'crypto'

export interface EncryptedLicenseData {
  encryptedData: string
  iv: string
  authTag: string
}

export class LicenseEncryptionService {
  private readonly algorithm = 'aes-256-gcm'
  private readonly secretKey: string

  constructor(secretKey: string) {
    this.secretKey = secretKey
  }

  public encrypt(data: object): string {
    try {
      const iv = crypto.randomBytes(16)
      const cipher = crypto.createCipher(this.algorithm, this.secretKey)
      cipher.setAAD(Buffer.from('e-notaria-license', 'utf8'))

      const jsonData = JSON.stringify(data)
      let encrypted = cipher.update(jsonData, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      const authTag = cipher.getAuthTag()

      const result = {
        encryptedData: encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex')
      }

      return Buffer.from(JSON.stringify(result)).toString('base64')
    } catch (error) {
      throw new Error('Failed to encrypt license data')
    }
  }

  public decrypt(encryptedLicense: string): object {
    try {
      const jsonString = Buffer.from(encryptedLicense, 'base64').toString('utf8')
      const { encryptedData, iv, authTag }: EncryptedLicenseData = JSON.parse(jsonString)

      const decipher = crypto.createDecipher(this.algorithm, this.secretKey)
      decipher.setAAD(Buffer.from('e-notaria-license', 'utf8'))
      decipher.setAuthTag(Buffer.from(authTag, 'hex'))

      let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return JSON.parse(decrypted)
    } catch (error) {
      throw new Error('Failed to decrypt license data')
    }
  }

  public generateLicenseKey(): string {
    return crypto.randomBytes(16).toString('hex').toUpperCase()
  }

  public generateSystemFingerprint(): string {
    const os = require('os')
    const data = {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      cpus: os.cpus().length,
      totalmem: os.totalmem()
    }
    
    return crypto
      .createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex')
      .substring(0, 32)
      .toUpperCase()
  }
}
