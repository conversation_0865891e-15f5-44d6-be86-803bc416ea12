"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { getAppName } from "@/lib/config"

export default function Home() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return // Aún cargando

    if (session) {
      // Usuario autenticado, redirigir al dashboard
      router.push("/dashboard")
    } else {
      // Usuario no autenticado, redirigir al login
      router.push("/auth/login")
    }
  }, [session, status, router])

  // Mostrar loading mientras se determina el estado de autenticación
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <div className="flex items-center justify-center space-x-2 mb-2">
          <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center shadow-soft">
            <span className="text-white font-bold text-sm">e</span>
          </div>
          <span className="text-2xl font-bold text-gray-700">
            {getAppName()}
          </span>
        </div>
        <p className="text-gray-500">Cargando sistema...</p>
      </div>
    </div>
  )
}
