"use client"

import { useState } from "react"
import { ComparecienteForm } from "../compareciente-form"
import { TestCompareciente } from "../test-compareciente"
import { SimpleComparecienteForm } from "../simple-compareciente-form"

interface FormData {
  documentTypes: Array<{value: string, label: string}>
  calidades: Array<{value: string, label: string}>
  currentUser: {id: string, name: string}
}

interface OTFormState {
  id?: string
  numeroOT?: string
  tipoDocumento: string
  fechaOT: string
  materiaDetalle: string
  gestora?: string
  numeroWorkflow?: string
  observaciones?: string
  comparecientes: Array<{
    id: string
    rut: string
    nombres: string
    apellidoPaterno: string
    apellidoMaterno: string
    calidad: string
    esChileno: boolean
  }>
  estado: string
  fechaVencimiento?: string
  isDraft: boolean
}

interface DatosOTTabProps {
  formData: FormData
  formState: OTFormState
  updateFormState: (updates: Partial<OTFormState>) => void
  onCreateOT: (data: any) => void
  isPending: boolean
  isEditing: boolean
}

export function DatosOTTab({ 
  formData, 
  formState, 
  updateFormState, 
  onCreateOT, 
  isPending, 
  isEditing 
}: DatosOTTabProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setErrors({})

    console.log('Form submission - Current state:', formState)

    // Validations
    const newErrors: Record<string, string> = {}

    if (!formState.tipoDocumento) {
      newErrors.tipoDocumento = 'Tipo de documento es requerido'
    }

    if (!formState.materiaDetalle || formState.materiaDetalle.length < 10) {
      newErrors.materiaDetalle = 'Materia debe tener al menos 10 caracteres'
    }

    if (formState.comparecientes.length === 0) {
      newErrors.comparecientes = 'Al menos un compareciente es requerido'
    }

    console.log('Validation errors:', newErrors)

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    console.log('Calling onCreateOT with:', formState)
    onCreateOT(formState)
  }

  const addCompareciente = (compareciente: any) => {
    const newCompareciente = {
      ...compareciente,
      id: Date.now().toString()
    }
    console.log('Adding compareciente:', newCompareciente)
    console.log('Current comparecientes:', formState.comparecientes)

    const updatedComparecientes = [...formState.comparecientes, newCompareciente]
    console.log('Updated comparecientes:', updatedComparecientes)

    updateFormState({
      comparecientes: updatedComparecientes
    })
  }

  const removeCompareciente = (id: string) => {
    updateFormState({
      comparecientes: formState.comparecientes.filter(c => c.id !== id)
    })
  }

  return (
    <div className="space-y-6">
      {/* Debug Info - Remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded p-3 text-xs">
          <strong>Debug:</strong> Comparecientes en estado: {formState.comparecientes.length}
          <pre className="mt-1 text-xs overflow-auto max-h-20">
            {JSON.stringify(formState.comparecientes, null, 2)}
          </pre>
        </div>
      )}
      <div className="bg-blue-600 text-white p-4 rounded-t-lg">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium">
              Datos principales de la Orden de Trabajo
            </h3>
          </div>
          {formState.numeroOT && (
            <div className="flex space-x-2">
              <button
                type="button"
                className="bg-yellow-500 hover:bg-yellow-600 text-black px-3 py-1 rounded text-sm font-medium"
              >
                Imprimir OT
              </button>
              <div className="relative">
                <button
                  type="button"
                  className="bg-yellow-500 hover:bg-yellow-600 text-black px-2 py-1 rounded text-sm"
                >
                  ▼
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="bg-blue-50 p-6 rounded-b-lg border border-blue-200">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Primera fila */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
            {/* Número OT */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                N° de OT
              </label>
              <input
                type="text"
                value={formState.numeroOT || 'Se asignará al crear'}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600 text-sm"
              />
            </div>

            {/* Tipo de Documento */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tipo de Documento *
              </label>
              <select
                value={formState.tipoDocumento}
                onChange={(e) => updateFormState({ tipoDocumento: e.target.value })}
                disabled={!formState.isDraft}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.tipoDocumento ? 'border-red-300' : 'border-gray-300'
                } ${!formState.isDraft ? 'bg-gray-100' : 'bg-white'}`}
                required
              >
                <option value="">Seleccionar tipo...</option>
                {formData.documentTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
              {errors.tipoDocumento && (
                <p className="mt-1 text-sm text-red-600">{errors.tipoDocumento}</p>
              )}
            </div>

            {/* Fecha OT - Siempre HOY, no editable */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Fecha OT
              </label>
              <input
                type="text"
                value={new Date(formState.fechaOT).toLocaleDateString('es-CL')}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
              />
              <p className="text-xs text-gray-500 mt-1">Fecha automática (HOY)</p>
            </div>

            {/* Estado */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Estado
              </label>
              <input
                type="text"
                value={formState.estado}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
              />
            </div>
          </div>

          {/* Segunda fila - Materia */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Materia *
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white">
                <option value="">Seleccionar materia...</option>
                <option value="COMPRAVENTA">Compraventa</option>
                <option value="HIPOTECA">Hipoteca</option>
                <option value="MANDATO">Mandato</option>
                <option value="DONACION">Donación</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Detalle de la Materia *
              </label>
              <input
                type="text"
                value={formState.materiaDetalle}
                onChange={(e) => updateFormState({ materiaDetalle: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.materiaDetalle ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Describa detalladamente la materia..."
                maxLength={200}
                required
              />
              <div className="mt-1 flex justify-between">
                {errors.materiaDetalle && (
                  <p className="text-sm text-red-600">{errors.materiaDetalle}</p>
                )}
                <p className="text-sm text-gray-500">
                  {formState.materiaDetalle.length}/200 caracteres
                </p>
              </div>
            </div>
          </div>

          {/* Comparecientes */}
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-lg font-medium text-gray-900">COMPARECIENTES</h4>
              <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2 py-1 rounded">
                {formState.comparecientes.length} agregado(s)
              </span>
            </div>
            
            {errors.comparecientes && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{errors.comparecientes}</p>
              </div>
            )}

            {/* Test Component - Remove in production */}
            <TestCompareciente
              onAdd={addCompareciente}
              comparecientes={formState.comparecientes}
              onRemove={removeCompareciente}
            />

            {/* Simple Component - Remove in production */}
            <SimpleComparecienteForm
              calidades={formData.calidades}
              onAdd={addCompareciente}
              comparecientes={formState.comparecientes}
              onRemove={removeCompareciente}
            />

            <ComparecienteForm
              calidades={formData.calidades}
              onAdd={addCompareciente}
              comparecientes={formState.comparecientes}
              onRemove={removeCompareciente}
            />
          </div>

          {/* Gestión y otros datos */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Empresa Gestora
                </label>
                <input
                  type="text"
                  value={formState.gestora || ''}
                  onChange={(e) => updateFormState({ gestora: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Nombre de la empresa gestora"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Número de Workflow
                </label>
                <input
                  type="text"
                  value={formState.numeroWorkflow || ''}
                  onChange={(e) => updateFormState({ numeroWorkflow: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Número de workflow interno"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fecha de Vencimiento
                </label>
                <input
                  type="date"
                  value={formState.fechaVencimiento || ''}
                  onChange={(e) => updateFormState({ fechaVencimiento: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Observaciones
              </label>
              <textarea
                value={formState.observaciones || ''}
                onChange={(e) => updateFormState({ observaciones: e.target.value })}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md resize-none"
                placeholder="Observaciones adicionales sobre la OT..."
              />
            </div>
          </div>

          {/* Submit Button */}
          {!isEditing && (
            <div className="flex justify-end pt-4">
              <button
                type="submit"
                disabled={isPending}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isPending ? "Creando..." : "Crear OT"}
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  )
}
