<?php
session_start();
// Include archivos config
require_once "layouts/config.php";
//require_once "config/constantes.php";
require_once "appa/autoload.php";
if(isset($_REQUEST['p']) && $_REQUEST['p'] <> 's'){
    $user            = new entidades\usuario();
    $resp_validacion = $user->logoutUser($_SESSION['id']);
}

if(!isset($_REQUEST['p'])){
    $user            = new entidades\usuario();
    $resp_validacion = $user->logoutUser($_SESSION['id']);
}

unset($_SESSION['loggedin']);
unset($_SESSION['id']);
unset($_SESSION['username']);
unset($_SESSION['token']);
$_SESSION = array();

if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

session_destroy();

    echo "
    <script>
    localStorage.removeItem('token');  
    </script>
    ";




header("Location: login.php");
exit;