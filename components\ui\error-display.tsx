"use client"

import { useState } from "react"

interface ErrorDisplayProps {
  error: string
  errorId?: string
  title?: string
  showDetails?: boolean
  onRetry?: () => void
  className?: string
}

export function ErrorDisplay({ 
  error, 
  errorId, 
  title = "Error", 
  showDetails = false,
  onRetry,
  className = ""
}: ErrorDisplayProps) {
  const [showFullError, setShowFullError] = useState(false)
  const [copied, setCopied] = useState(false)

  const copyErrorId = async () => {
    if (errorId) {
      try {
        await navigator.clipboard.writeText(errorId)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('Failed to copy error ID:', err)
      }
    }
  }

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-red-800">
            {title}
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <p>{error}</p>
          </div>
          
          {errorId && (
            <div className="mt-3 flex items-center space-x-2">
              <span className="text-xs text-red-600">ID del Error:</span>
              <code 
                className="text-xs bg-red-100 px-2 py-1 rounded cursor-pointer hover:bg-red-200 transition-colors"
                onClick={copyErrorId}
                title="Click para copiar"
              >
                {errorId}
              </code>
              {copied && (
                <span className="text-xs text-green-600">✓ Copiado</span>
              )}
            </div>
          )}

          {showDetails && (
            <div className="mt-3">
              <button
                onClick={() => setShowFullError(!showFullError)}
                className="text-xs text-red-600 hover:text-red-800 underline"
              >
                {showFullError ? 'Ocultar detalles' : 'Mostrar detalles'}
              </button>
              
              {showFullError && (
                <div className="mt-2 p-2 bg-red-100 rounded text-xs font-mono text-red-800 overflow-auto max-h-32">
                  <pre>{error}</pre>
                  {errorId && (
                    <div className="mt-2 pt-2 border-t border-red-200">
                      <strong>Error ID:</strong> {errorId}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {onRetry && (
            <div className="mt-4">
              <button
                onClick={onRetry}
                className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Reintentar
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Componente específico para errores de permisos
export function PermissionError({ 
  requiredPermission, 
  errorId,
  className = ""
}: { 
  requiredPermission?: string
  errorId?: string
  className?: string 
}) {
  return (
    <ErrorDisplay
      title="Permisos Insuficientes"
      error={`No tienes permisos para realizar esta acción.${requiredPermission ? ` Se requiere: ${requiredPermission}` : ''}`}
      errorId={errorId}
      className={className}
    />
  )
}

// Componente específico para errores de validación
export function ValidationError({ 
  errors, 
  errorId,
  className = ""
}: { 
  errors: Record<string, string>
  errorId?: string
  className?: string 
}) {
  const errorList = Object.entries(errors).map(([field, message]) => `${field}: ${message}`)
  
  return (
    <ErrorDisplay
      title="Errores de Validación"
      error={errorList.join(', ')}
      errorId={errorId}
      showDetails={true}
      className={className}
    />
  )
}

// Componente específico para errores de autenticación
export function AuthenticationError({ 
  errorId,
  className = ""
}: { 
  errorId?: string
  className?: string 
}) {
  return (
    <ErrorDisplay
      title="Error de Autenticación"
      error="Tu sesión ha expirado o no tienes acceso. Por favor, inicia sesión nuevamente."
      errorId={errorId}
      className={className}
    />
  )
}

// Hook para manejar errores de forma consistente
export function useErrorHandler() {
  const [error, setError] = useState<{
    message: string
    errorId?: string
    type?: 'validation' | 'permission' | 'authentication' | 'general'
  } | null>(null)

  const handleError = (
    errorMessage: string, 
    errorId?: string, 
    type: 'validation' | 'permission' | 'authentication' | 'general' = 'general'
  ) => {
    setError({
      message: errorMessage,
      errorId,
      type
    })
  }

  const clearError = () => {
    setError(null)
  }

  const renderError = (className?: string) => {
    if (!error) return null

    switch (error.type) {
      case 'permission':
        return <PermissionError errorId={error.errorId} className={className} />
      case 'authentication':
        return <AuthenticationError errorId={error.errorId} className={className} />
      case 'validation':
        // Para validation errors, necesitarías pasar los errores específicos
        return <ErrorDisplay error={error.message} errorId={error.errorId} className={className} />
      default:
        return <ErrorDisplay error={error.message} errorId={error.errorId} className={className} />
    }
  }

  return {
    error,
    handleError,
    clearError,
    renderError
  }
}
