<?php
require_once 'layouts/config.php';
include 'calls/lib_anexos.php';
define('TINYAJAX_PATH', 'library/TinyAjax');
include_once(TINYAJAX_PATH . "/TinyAjax.php");
require_once PHYSICAL_PATH."/appa/autoload.php";
$user = new entidades\usuario();
$datos_licencia = new datos;

$texto1 = $datos_licencia->conservador() . "\n" .
         $datos_licencia->tipo_reportes() . "\n" .
         $datos_licencia->numeracion_notaria() . "\n" .
         strtoupper($datos_licencia->direccion()) . ' - ' . strtoupper($datos_licencia->comuna()) . "\n" .
         $datos_licencia->telefono();
$texto = $datos_licencia->conservador() . "/" .
         $datos_licencia->tipo_reportes() . "/" .
         $datos_licencia->numeracion_notaria() . "/" .
         strtoupper($datos_licencia->direccion()) . ' - ' . strtoupper($datos_licencia->comuna()) . "/" .
         $datos_licencia->telefono();
         $base64Licencia = base64_encode(utf8_encode($texto));
// SE AGREGA KEY IN LOCALSTORAGE con TOKEN JWT
if (isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] === true) {
    
    
    $token  = $_SESSION["token"];
    $pms    = $user->configurarPermisosRbac();
    /* $pmsw   = json_decode($pms);
    echo '<pre>';
    foreach($pmsw as $perm){
        echo base64_decode($perm)."<br>";
    }
    echo '</pre>';
    die(); */

    echo "
        <script>
            if(!localStorage.getItem('token')){
                localStorage.setItem('token', '$token');  
                localStorage.setItem('pms', '$pms');  
                console.log('$texto')
                localStorage.setItem('lc', '$texto ');  
            }             
        </script>
    ";
}

$token2             = $_SESSION["token"];
$token_valido       =  $user->getUncipheredToken($token2);
if($token_valido == "Token JWT no válido")
{
    header("location: " . BASE_PATH . "logout.php");
    exit;
}

/*
// VALIDACIÓN DE SEGURIDAD
*/

require_once PHYSICAL_PATH . '/library/PhpRbac/autoload.php';

use entidades\Menu;
use PhpRbac\Rbac;

$rbac = new Rbac();

// Verificar si el usuario está logueado, si no, redirección a Login

if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    header("location: " . BASE_PATH . "login.php");
    exit;
}

// VALORES DE SESIÓN
//$id_usuario = $_SESSION["id"];
//$nombreSocial= $_SESSION["username"];
$id_usuario = $_SESSION["id"];
$nombreSocial = $_SESSION["username"];

//AUTORIZACIÓN RBAC
//$role_id = $rbac->Roles->returnId('repertorista');
//$rbac->enforce('asignar_repertorio', $id_usuario);

/*
// CONFIGURACIÓN DE VARIABLES DE LA PLANTILLA
*/
$head_title = "SIGN+ Sistema de Gestión Notarial";
$body_title = "Panel de Inicio";
$body_breadcrumb = "Panel de Inicio";

/*
// INCLUDES Y REQUIRES
*/

//require_once $_SERVER['DOCUMENT_ROOT']."/config/constantes.php";

function saludarError()
{
    $tab = new TinyAjaxBehavior();
    $tab->add(TabInnerHtml2::getBehavior("msg", "Hola"));
    return $tab->getString();
}

function saludarOK()
{

    return saludo();
}

/**
 * @return string
 */
function saludo()
{
    $timezone_original = date_default_timezone_get();
    date_default_timezone_set('America/Santiago');

    $tab = new TinyAjaxBehavior();
    $tab->add(TabInnerHtml::getBehavior("msg", "Hola " . date('d/m/Y h:i:s a', time()) . ' Timezone -> ' . date_default_timezone_get()));
    //sleep for 3 seconds
    sleep(3);
    return $tab->getString();
}


$ajax = new TinyAjax();
$ajax->showLoading();
$ajax->exportFunction("saludarError",   array());
$ajax->exportFunction("saludarOK",   array());

$ajax->process();

?>

<!DOCTYPE html>
<html lang="es">

<head>
    <title><?php echo $head_title; ?></title>

    <?php include_once PHYSICAL_PATH . "/layouts/head.php"; ?>

    <!-- Custom Css-->

    <style>
        @import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css");

        /* ---------------Animation---------------- */

        .slit-in-vertical {
            -webkit-animation: slit-in-vertical 0.45s ease-out both;
            animation: slit-in-vertical 0.45s ease-out both;
        }

        @-webkit-keyframes slit-in-vertical {
            0% {
                -webkit-transform: translateZ(-800px) rotateY(90deg);
                transform: translateZ(-800px) rotateY(90deg);
                opacity: 0;
            }

            54% {
                -webkit-transform: translateZ(-160px) rotateY(87deg);
                transform: translateZ(-160px) rotateY(87deg);
                opacity: 1;
            }

            100% {
                -webkit-transform: translateZ(0) rotateY(0);
                transform: translateZ(0) rotateY(0);
            }
        }

        @keyframes slit-in-vertical {
            0% {
                -webkit-transform: translateZ(-800px) rotateY(90deg);
                transform: translateZ(-800px) rotateY(90deg);
                opacity: 0;
            }

            54% {
                -webkit-transform: translateZ(-160px) rotateY(87deg);
                transform: translateZ(-160px) rotateY(87deg);
                opacity: 1;
            }

            100% {
                -webkit-transform: translateZ(0) rotateY(0);
                transform: translateZ(0) rotateY(0);
            }
        }

        /*---------------#region Alert--------------- */

        #dialogoverlay {
            display: none;
            opacity: .8;
            position: fixed;
            top: 0px;
            left: 0px;
            background: #707070;
            width: 100%;
            z-index: 10;
        }

        #dialogbox {
            display: none;
            position: absolute;
            background: rgb(0, 26, 47);
            border-radius: 7px;
            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.575);
            transition: 0.3s;
            width: 40%;
            z-index: 10;
            top: 0;
            left: 0;
            right: 0;
            margin: auto;
        }

        #dialogbox:hover {
            box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.911);
        }

        .container {
            padding: 2px 16px;
        }

        .pure-material-button-contained {
            position: relative;
            display: inline-block;
            box-sizing: border-box;
            border: none;
            border-radius: 4px;
            padding: 0 16px;
            min-width: 64px;
            height: 36px;
            vertical-align: middle;
            text-align: center;
            text-overflow: ellipsis;
            text-transform: uppercase;
            color: rgb(var(--pure-material-onprimary-rgb, 255, 255, 255));
            background-color: rgb(var(--pure-material-primary-rgb, 0, 65, 118));
            /* background-color: rgb(1, 47, 61) */
            box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            font-family: var(--pure-material-font, "Roboto", "Segoe UI", BlinkMacSystemFont, system-ui, -apple-system);
            font-size: 14px;
            font-weight: 500;
            line-height: 36px;
            overflow: hidden;
            outline: none;
            cursor: pointer;
            transition: box-shadow 0.2s;
        }

        .pure-material-button-contained::-moz-focus-inner {
            border: none;
        }

        /* ---------------Overlay--------------- */

        .pure-material-button-contained::before {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgb(var(--pure-material-onprimary-rgb, 255, 255, 255));
            opacity: 0;
            transition: opacity 0.2s;
        }

        /* Ripple */
        .pure-material-button-contained::after {
            content: "";
            position: absolute;
            left: 50%;
            top: 50%;
            border-radius: 50%;
            padding: 50%;
            width: 32px;
            /* Safari */
            height: 32px;
            /* Safari */
            background-color: rgb(var(--pure-material-onprimary-rgb, 255, 255, 255));
            opacity: 0;
            transform: translate(-50%, -50%) scale(1);
            transition: opacity 1s, transform 0.5s;
        }

        /* Hover, Focus */
        .pure-material-button-contained:hover,
        .pure-material-button-contained:focus {
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
        }

        .pure-material-button-contained:hover::before {
            opacity: 0.08;
        }

        .pure-material-button-contained:focus::before {
            opacity: 0.24;
        }

        .pure-material-button-contained:hover:focus::before {
            opacity: 0.3;
        }

        /* Active */
        .pure-material-button-contained:active {
            box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
        }

        .pure-material-button-contained:active::after {
            opacity: 0.32;
            transform: translate(-50%, -50%) scale(0);
            transition: transform 0s;
        }

        /* Disabled */
        .pure-material-button-contained:disabled {
            color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.38);
            background-color: rgba(var(--pure-material-onsurface-rgb, 0, 0, 0), 0.12);
            box-shadow: none;
            cursor: initial;
        }

        .pure-material-button-contained:disabled::before {
            opacity: 0;
        }

        .pure-material-button-contained:disabled::after {
            opacity: 0;
        }

        #dialogbox>div {
            background: #FFF;
            margin: 8px;
        }

        #dialogbox>div>#dialogboxhead {
            background: rgb(0, 65, 118);
            font-size: 19px;
            padding: 10px;
            color: rgb(255, 255, 255);
            font-family: Verdana, Geneva, Tahoma, sans-serif;
        }

        #dialogbox>div>#dialogboxbody {
            background: rgb(0, 39, 72);
            padding: 20px;
            color: #FFF;
            font-family: Verdana, Geneva, Tahoma, sans-serif;
        }

        #dialogbox>div>#dialogboxfoot {
            background: rgb(0, 39, 72);
            padding: 10px;
            text-align: right;
        }

        /*#endregion Alert*/
    </style>

    <style>
        .flip-card {
            background-color: transparent;
            width: 150px;
            height: 150px;
            perspective: 1000px;
        }

        .flip-card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.6s;
            transform-style: preserve-3d;
            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
        }

        .flip-card:hover .flip-card-inner {
            transform: rotateY(180deg);
        }

        .flip-card-front,
        .flip-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        .flip-card-front {
            background-color: #bbb;
            color: black;
        }

        .flip-card-back {
            background-color: #0b2c5b;
            color: white;
            transform: rotateY(180deg);
        }
    </style>

    <!-- Custom Scripts-->
    <?php $ajax->drawJavaScript(); ?>
</head>

<body data-sidebar="dark" style="background-image:url('assets/images/bg-sign.png');background-color: #cccccc;background-position: center; /* Center the image */
  background-repeat: no-repeat; /* Do not repeat the image */
  background-size: 100% 95%; /* Resize the background image to cover the entire container *" />

<!-- Wrapper Principal -->
<div id="layout-wrapper">

    <!-- ========== Barra de Navegación Superior ========== -->
    <?php include_once PHYSICAL_PATH . "/layouts/body-navbar-header.php"; ?>
    <!-- ========== / Barra de Navegación Superior ========== -->

    <!-- ========== Menú Izquierda ========== -->
    <div class="vertical-menu">

        <div data-simplebar class="h-100">

            <!--- Menu Izquierda -->
            <?php include_once PHYSICAL_PATH . "/layouts/body-left-menu.php"; ?>
            <!-- / Menú Izquierda -->
        </div>
    </div>
    <!-- / Menú Izquierda-->

    <div class="main-content">

        <div class="page-content">
            <div class="container-fluid">

                <!-- Titulo y BreadCrumb de la Página -->
                <?php /* include_once PHYSICAL_PATH . "/layouts/body-breadcrumb.php"; */ ?>
                <!-- / Titulo y BreadCrumb de la Página -->
                <div id="msg"></div>
                <!-- ============================================================== -->
                <!-- Inicio del Contenido Personalizado                             -->
                <!-- ============================================================== -->

                <!-- <button onclick="saludarError()">Llama tinyajax Error</button>
                    <button onclick="saludarOK()">Llama tinyajax OK</button>

                    <?php //echo '<br>USERNAME: '.$_SESSION["username"]; 
                    ?>
                    <?php //echo '<br>ID: '.$_SESSION["id"]; 
                    ?> -->


                <!-- <div style='display: flex;'>


                    <a href="app/caja/" style='margin-right:10px'>
                        <div class="flip-card">
                            <div class="flip-card-inner">
                                <div class="flip-card-front d-flex align-items-center justify-content-center" style='background-color: #34495E;'>
                                    <h1 style='font-size:64px;color:white;'>BHE</h1>
                                   
                                </div>
                                <div class="flip-card-back">
                                    <p style='padding-top:10px;'><strong>Módulo BHE</strong></p>
                                    <p><small>Emisión de Boletas de Honorarios Electrónicas</small></p>
                                </div>
                            </div>
                        </div>
                    </a>

  
                    <a href="app/ot/" style='margin-right:10px'>
                        <div class="flip-card">
                            <div class="flip-card-inner">
                                <div class="flip-card-front d-flex align-items-center justify-content-center" style='background-color: #F8C471;'>
                                    <h1 style='font-size:64px;'>OT</h1>
                                   
                                </div>
                                <div class="flip-card-back">
                                    <p style='padding-top:10px;'><strong>Módulo OT</strong></p>
                                    <p><small>Creación, Modificación y Seguimiento de Órdenes de Trabajo</small></p>
                                </div>
                            </div>
                        </div>
                    </a>

                    <a href="app/escrituras_publicas/" style='margin-right:10px'>
                        <div class="flip-card">
                            <div class="flip-card-inner">
                                <div class="flip-card-front d-flex align-items-center justify-content-center" style='background-color: #DAF7A6;'>
                                    <h1 style='font-size:64px;'>EP</h1>
                                   
                                </div>
                                <div class="flip-card-back">
                                    <p style='padding-top:10px;'><strong>Copia de Escritura Pública</strong></p>
                                    <p><small>Emisión de Copias de Escrituras Públicas basados en un Número de Repertorio</small></p>
                                </div>
                            </div>
                        </div>
                    </a>


                    <a href="app/documento_privado_local/" style='margin-right:10px'>
                        <div class="flip-card">
                            <div class="flip-card-inner">
                                <div class="flip-card-front d-flex align-items-center justify-content-center" style='background-color: #5D6D7E;'>
                                    <h1 style='font-size:64px;color:white;'>DPL</h1>
                                   
                                </div>
                                <div class="flip-card-back">
                                    <p style='padding-top:10px;'><strong>Documento Privado Local</strong></p>
                                    <p><small>Generación de Documentos Privados con solicitud iniciada en el oficio</small></p>
                                </div>
                            </div>
                        </div>
                    </a>

                </div>

                <div style='display: flex; margin-top:10px;'>

                    <a href="app/caja/" style='margin-right:10px'>
                        <div class="flip-card">
                            <div class="flip-card-inner">
                                <div class="flip-card-front d-flex align-items-center justify-content-center" style='background-color: #AEF3E7;'>
                                    <h1 style='font-size:64px;'>DPF</h1>
                                  
                                </div>
                                <div class="flip-card-back">
                                    <p style='padding-top:10px;'><strong>Documentos Por Firmar</strong></p>
                                    <p><small>Gestión de Documentos Privados y Públicos listos para el proceso de Firma Digital</small></p>
                                </div>
                            </div>
                        </div>
                    </a>

                   
                    <a href="app/ot/" style='margin-right:10px'>
                        <div class="flip-card">
                            <div class="flip-card-inner">
                                <div class="flip-card-front d-flex align-items-center justify-content-center" style='background-color: #254E70;'>
                                    <h1 style='font-size:64px;color:white;'>BD</h1>
                                   
                                </div>
                                <div class="flip-card-back">
                                    <p style='padding-top:10px;'><strong>Solicitud de Bloqueo de Documentos</strong></p>
                                    <p><small>Solicitud de Bloqueo de Documentos Digitales</small></p>
                                </div>
                            </div>
                        </div>
                    </a>


                    <a href="app/escrituras_publicas/" style='margin-right:10px'>
                        <div class="flip-card">
                            <div class="flip-card-inner">
                                <div class="flip-card-front d-flex align-items-center justify-content-center" style='background-color: #C33C54;'>
                                    <h1 style='font-size:64px;'>USU</h1>
                                   
                                </div>
                                <div class="flip-card-back">
                                    <p style='padding-top:10px;'><strong>Módulo de Gestión de Usuarios</strong></p>
                                    <p><small>Gestión de Usuarios, Roles y Perfiles del sistema SIGN+ v2</small></p>
                                </div>
                            </div>
                        </div>
                    </a>



                </div> -->

                <?php
                //$gm = new Menu($id_usuario);
                //echo $gm->getIndexShortcut();
                ?>


                <iframe id="inlineFrameDocPriv"
                        title="Documento Privado"
                        width="100%"
                        height="100%"
                        src="main_menu.php"
                        scrolling="no"
                >
                </iframe>
                




                <!-- ============================================================== -->
                <!-- Fin del Contenido Personalizado                             -->
                <!-- ============================================================== -->

                <div id="dialogoverlay"></div>
                <div id="dialogbox" class="slit-in-vertical">
                    <div>
                        <div id="dialogboxhead"></div>
                        <div id="dialogboxbody"></div>
                        <div id="dialogboxfoot"></div>
                    </div>
                </div>
            </div>
            <!-- container-fluid -->
        </div>
        <!-- Fin de Contenido de la página -->

        <!-- Footer de la Página -->
        <?php include_once PHYSICAL_PATH . "/layouts/body-footer.php"; ?>


    </div>
    <!-- fin contenido-->

</div>
<!-- Fin Wrapper Principal -->

<!-- Sidebar Derecha -->
<?php include_once PHYSICAL_PATH . "/layouts/right-bar.php"; ?>
<!-- /barra derecha -->

<!-- Javascript -->
<?php include_once PHYSICAL_PATH . "/layouts/script-bottom-body.php"; ?>

<!-- Custom Javascript && Jquery-->

</body>

</html>