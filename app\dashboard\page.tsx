"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { BasicLayout, BasicPageHeader, BasicPageContent } from "@/components/layout/basic-layout"
import { getAppName, getCompanyName } from "@/lib/config"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }



  return (
    <BasicLayout>
      <BasicPageHeader
        title={`Bienvenido, ${session.user.nombreSocial || session.user.username}`}
        description={`Panel de control - ${getCompanyName()}`}
      />

      <BasicPageContent>
        <div className="bg-white rounded-lg shadow-soft border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Dashboard e-NOTARIA
          </h2>
          <p className="text-gray-600 mb-4">
            Bienvenido al sistema e-NOTARIA. Si puedes ver el sidebar a la izquierda y el navbar arriba, el layout está funcionando correctamente.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h3 className="font-medium text-blue-900 mb-2">Información del Usuario</h3>
              <p className="text-sm text-blue-700">Usuario: {session.user.username}</p>
              <p className="text-sm text-blue-700">Nombre: {session.user.nombreSocial || 'No definido'}</p>
              <p className="text-sm text-blue-700">Email: {session.user.email || 'No definido'}</p>
            </div>

            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h3 className="font-medium text-green-900 mb-2">Información del Sistema</h3>
              <p className="text-sm text-green-700">Sistema: {getAppName()}</p>
              <p className="text-sm text-green-700">Empresa: {getCompanyName()}</p>
              <p className="text-sm text-green-700">Versión: 1.0.0</p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-medium text-yellow-900 mb-2">Estado del Layout</h3>
            <p className="text-sm text-yellow-700">
              ✅ Layout con sidebar y navbar implementado
            </p>
            <p className="text-sm text-yellow-700 mt-1">
              ✅ Colores optimizados para protección visual
            </p>
            <p className="text-sm text-yellow-700 mt-1">
              ✅ Configuración centralizada de empresa
            </p>
            <p className="text-sm text-yellow-700 mt-1">
              ✅ Branding e-NOTARIA aplicado
            </p>
          </div>
        </div>
      </BasicPageContent>
    </BasicLayout>
  )
}
