"use client"

import { useState } from "react"
import { UnifiedLayout } from "@/components/layout/unified-layout"
import { LicenseManager } from "@/components/license/license-manager"
import { LicenseData } from "@/lib/license"

export default function LicenciasPage() {
  const [currentLicense, setCurrentLicense] = useState<LicenseData | null>(null)

  const handleLicenseValidated = (license: LicenseData) => {
    setCurrentLicense(license)
    console.log('Licencia validada:', license)
  }

  const handleLicenseError = (error: string) => {
    console.error('Error de licencia:', error)
  }

  return (
    <UnifiedLayout>
      <div style={{ padding: '24px' }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
          {/* Header de página */}
          <div style={{ 
            backgroundColor: 'white', 
            borderBottom: '1px solid #d1d5db',
            padding: '24px',
            marginBottom: '24px',
            borderRadius: '8px',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
          }}>
            <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827' }}>
              Gestión de Licencias
            </h1>
            <p style={{ marginTop: '4px', fontSize: '14px', color: '#4b5563' }}>
              Sistema de licencias cifradas e-NOTARIA
            </p>
          </div>
              
          {/* Información sobre el sistema de licencias */}
          <div style={{ 
            backgroundColor: '#eff6ff', 
            padding: '16px', 
            borderRadius: '8px',
            border: '1px solid #bfdbfe',
            marginBottom: '24px'
          }}>
            <h3 style={{ fontSize: '16px', fontWeight: '500', color: '#1e3a8a', marginBottom: '8px' }}>
              Sistema de Licencias e-NOTARIA
            </h3>
            <p style={{ fontSize: '14px', color: '#1e40af', marginBottom: '8px' }}>
              El sistema de licencias de e-NOTARIA utiliza cifrado avanzado y validación online para proteger su software.
            </p>
            <ul style={{ fontSize: '12px', color: '#1e40af', paddingLeft: '16px' }}>
              <li>Licencias cifradas con datos específicos del cliente</li>
              <li>Validación online automática cada 24 horas</li>
              <li>Fingerprint de hardware para prevenir uso no autorizado</li>
              <li>Diferentes tipos de licencia según las necesidades</li>
            </ul>
          </div>

          {/* Componente de gestión de licencias */}
          <LicenseManager 
            onLicenseValidated={handleLicenseValidated}
            onLicenseError={handleLicenseError}
          />

          {/* Información técnica */}
          {currentLicense && (
            <div style={{ 
              backgroundColor: '#f0fdf4', 
              padding: '16px', 
              borderRadius: '8px',
              border: '1px solid #bbf7d0',
              marginTop: '24px'
            }}>
              <h3 style={{ fontSize: '16px', fontWeight: '500', color: '#14532d', marginBottom: '8px' }}>
                Información Técnica de la Licencia
              </h3>
              <div style={{ fontSize: '12px', color: '#15803d', fontFamily: 'monospace' }}>
                <div><strong>ID de Instalación:</strong> {currentLicense.systemInfo.installationId}</div>
                <div><strong>Fingerprint:</strong> {currentLicense.systemInfo.hardwareFingerprint}</div>
                <div><strong>Versión:</strong> {currentLicense.systemInfo.version}</div>
                <div><strong>Clave de Licencia:</strong> {currentLicense.licenseKey}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </UnifiedLayout>
  )
}
