// Presentation - Server Actions - License Actions
'use server'

import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { container } from "../../infrastructure/config/dependency-injection"
import { revalidatePath } from "next/cache"

export async function validateLicense(formData: FormData) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      throw new Error('Not authenticated')
    }

    const encryptedLicense = formData.get('encryptedLicense') as string
    if (!encryptedLicense) {
      throw new Error('License data is required')
    }

    // Generate system info
    const systemInfo = {
      installationId: process.env.INSTALLATION_ID || 'default-installation',
      hardwareFingerprint: generateHardwareFingerprint(),
      version: '1.0.0',
      platform: process.platform
    }

    const useCase = container.getValidateLicenseUseCase()
    const result = await useCase.execute({
      encryptedLicense,
      systemInfo
    })

    if (!result.success) {
      return {
        success: false,
        error: result.error || 'License validation failed'
      }
    }

    // Store license in session or database
    // TODO: Implement license storage

    revalidatePath('/licencias')
    revalidatePath('/dashboard')

    return {
      success: true,
      license: {
        clientName: result.license!.getClientName(),
        clientEmail: result.license!.getClientEmail().getValue(),
        type: result.license!.getType(),
        status: result.license!.getStatus(),
        features: result.license!.getFeatures(),
        expiresAt: result.license!.getExpiresAt(),
        isActive: result.license!.isActive()
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function getCurrentLicense() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      throw new Error('Not authenticated')
    }

    // TODO: Implement get current license from database
    // For now, return mock data
    return {
      success: true,
      license: {
        clientName: 'Notaría de Prueba',
        clientEmail: '<EMAIL>',
        type: 'STANDARD',
        status: 'ACTIVE',
        features: {
          maxUsers: 10,
          maxDocuments: 1000,
          hasAdvancedReports: true,
          hasApiAccess: false,
          hasCustomBranding: true
        },
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isActive: true
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

function generateHardwareFingerprint(): string {
  const os = require('os')
  const crypto = require('crypto')
  
  const data = {
    platform: os.platform(),
    arch: os.arch(),
    hostname: os.hostname(),
    cpus: os.cpus().length,
    totalmem: os.totalmem()
  }
  
  return crypto
    .createHash('sha256')
    .update(JSON.stringify(data))
    .digest('hex')
    .substring(0, 32)
    .toUpperCase()
}
