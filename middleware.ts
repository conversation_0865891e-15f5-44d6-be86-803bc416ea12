import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    // Verificar permisos específicos si es necesario
    const token = req.nextauth.token
    const pathname = req.nextUrl.pathname

    // Rutas que requieren autenticación
    if (pathname.startsWith('/dashboard') || pathname.startsWith('/app')) {
      if (!token) {
        return NextResponse.redirect(new URL('/auth/login', req.url))
      }
    }

    // Verificar permisos específicos por módulo
    if (pathname.startsWith('/app/ot') && token) {
      const hasOtPermission = token.permissions?.includes('ot_access')
      if (!hasOtPermission) {
        return NextResponse.redirect(new URL('/unauthorized', req.url))
      }
    }

    if (pathname.startsWith('/app/usuarios') && token) {
      const hasUserPermission = token.permissions?.includes('user_management')
      if (!hasUserPermission) {
        return NextResponse.redirect(new URL('/unauthorized', req.url))
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const pathname = req.nextUrl.pathname

        // Permitir acceso solo a rutas de autenticación
        if (pathname.startsWith('/auth')) {
          return true
        }

        // La ruta raíz (/) siempre requiere verificación del lado del cliente
        // para determinar si redirigir a login o dashboard
        if (pathname === '/') {
          return true
        }

        // Requerir token para todas las demás rutas protegidas
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
}
