// Presentation - Components - Dashboard Stats
interface DashboardStatsProps {
  data: {
    totalOT: number
    pendingOT: number
    completedOT: number
    totalDocuments: number
  }
}

export function DashboardStats({ data }: DashboardStatsProps) {
  const stats = [
    {
      name: 'Total OT',
      value: data.totalOT,
      icon: '📋',
      color: 'bg-blue-500'
    },
    {
      name: 'OT Pendientes',
      value: data.pendingOT,
      icon: '⏳',
      color: 'bg-yellow-500'
    },
    {
      name: 'OT Completadas',
      value: data.completedOT,
      icon: '✅',
      color: 'bg-green-500'
    },
    {
      name: 'Documentos',
      value: data.totalDocuments,
      icon: '📄',
      color: 'bg-purple-500'
    }
  ]

  return (
    <>
      {stats.map((stat) => (
        <div
          key={stat.name}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center">
            <div className={`${stat.color} rounded-lg p-3 mr-4`}>
              <span className="text-white text-xl">{stat.icon}</span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.name}</p>
              <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
            </div>
          </div>
        </div>
      ))}
    </>
  )
}
