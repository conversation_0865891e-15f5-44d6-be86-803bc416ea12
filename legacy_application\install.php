<?php
ini_set('max_execution_time', '300');
ini_set('max_execution_time', '0');
include('./layouts/config.php');
require_once "appa/autoload.php";
require_once PHYSICAL_PATH . 'library\PhpRbac\autoload.php';
Use PhpRbac\Rbac;
$rbac = new Rbac();
$ini_array = parse_ini_file(PHYSICAL_PATH."env.ini", true);
$dbPass = $ini_array['DB_PASS'];
function roleInit($rbac,$dbPass){
    
   
    //genero conexion a la base de datos
    if (!($link = mysqli_connect("localhost", "root", $dbPass))) {
        exit();
    }
    if (!mysqli_select_db($link,"cons_not")) {
        echo "Error seleccionando la base de datos.";
        exit();
    }

    $create="CREATE TABLE IF NOT EXISTS `install` (
        `id` bigint(21) NOT NULL auto_increment,
        `version` varchar(50) NOT NULL,
        `fecha` datetime default NULL,
        PRIMARY KEY  (`id`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
      $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);



      $create="CREATE TABLE IF NOT EXISTS `parametros_licencia` (
         `id` bigint(21) NOT NULL AUTO_INCREMENT,
         `datos` text DEFAULT NULL,
         `fecha` datetime DEFAULT NULL,
         `usuario` varchar(80) DEFAULT NULL,
        PRIMARY KEY (`id`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
      $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);

      $create="CREATE TABLE IF NOT EXISTS `huellas_empresas_verificacion` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `idtrx` varchar(200) NOT NULL,
        `rut` varchar(20) NOT NULL,
        `nombre` varchar(100) NOT NULL,
        `apellidos` varchar(100) NOT NULL,
        `nacionalidad` varchar(100) NOT NULL,
        `serie` varchar(40) NOT NULL,
        `f_vencimiento` date NOT NULL,
        `f_nacimiento` date NOT NULL,
        `sexo` varchar(20) NOT NULL,
        `log` text NOT NULL,
        `resultado` varchar(1) NOT NULL,
        `estado_civil` varchar(40) NOT NULL,
        `mail` varchar(100) NOT NULL,
        `foto_firma` longblob NOT NULL,
        `foto_ci` longblob NOT NULL,
        `foto_ci2` longblob NOT NULL,
        `foto` longblob NOT NULL COMMENT 'imagen cara',
        `foto_rubrica` blob NOT NULL,
        `fecha_consulta` datetime NOT NULL,
        `qr` varchar(40) NOT NULL,
        `domicilio` varchar(200) NOT NULL,
        `tipo_consulta` varchar(40) NOT NULL COMMENT 'MESON O CAJA',
        `ip_consulta` varchar(30) NOT NULL,
        `pdf417` varchar(450) NOT NULL,
        `id_transanccion_notaria` bigint(20) NOT NULL COMMENT 'Operacion en particular para una trx especifica de la notaria',
        `id_sujeto_notaria` varchar(20) NOT NULL COMMENT 'Tipo Documento de Notaria ',
        `id_tipo_documento_notaria` varchar(40) NOT NULL,
        `id_institucion` varchar(100) NOT NULL,
        `id_sucursal` varchar(200) NOT NULL,
        `tipo_documento` varchar(3) DEFAULT NULL,
        `firma_rubrica` longblob NOT NULL,
        `usada` int(11) DEFAULT 0,
        `id_fojas` bigint(20) DEFAULT 0,
        `latitud` varchar(40) NOT NULL,
        `longitud` varchar(40) NOT NULL,
        `fecha_hora` timestamp NOT NULL DEFAULT current_timestamp(),
        PRIMARY KEY (`id`),
        KEY `rut` (`rut`,`nombre`,`apellidos`,`fecha_consulta`),
        KEY `tipo_consulta` (`tipo_consulta`),
        KEY `ip_consulta` (`ip_consulta`),
        KEY `tipo_documento_notaria` (`id_tipo_documento_notaria`),
        KEY `id_transanccion_notaria` (`id_transanccion_notaria`),
        KEY `idtrx` (`idtrx`)
      ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_bin AUTO_INCREMENT=1;";
      
      $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);
  
  
      $create="CREATE TABLE IF NOT EXISTS `certificados_firmas` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `numero_firma` bigint(12) NOT NULL,
        `tipo` varchar(80) NOT NULL,
        `caratula` int(10) NOT NULL,
        `libro` varchar(80) NOT NULL,
        `folio_real` int(20) NOT NULL,
        `foja` varchar(30) NOT NULL,
        `numero` varchar(20) NOT NULL,
        `anho` int(4) NOT NULL,
        `fecha` date NOT NULL,
        `hora` time NOT NULL,
        `data` longblob NOT NULL,
        `usuario` varchar(20) NOT NULL,
        `revisor` varchar(20) NOT NULL,
        `listo` varchar(3) NOT NULL DEFAULT 'nop',
        `texto` mediumtext DEFAULT NULL,
        `mail_destinatario` mediumtext NOT NULL,
        `pdf_tipo` varchar(20) NOT NULL,
        `codigo_descarga` varchar(30) NOT NULL,
        `codigo_subida_tmp` varchar(40) NOT NULL,
        `codigo` varchar(100) NOT NULL,
        `id_pago` bigint(20) NOT NULL,
        PRIMARY KEY (`id`),
        KEY `caratula` (`caratula`),
        KEY `libro` (`libro`),
        KEY `folio_real` (`folio_real`),
        KEY `foja` (`foja`),
        KEY `numero` (`numero`),
        KEY `anho` (`anho`),
        KEY `fecha` (`fecha`),
        KEY `listo` (`listo`),
        KEY `numero_firma` (`numero_firma`),
        KEY `codigo` (`codigo`),
        KEY `codigo_2` (`codigo`),
        KEY `codigo_3` (`codigo`),
        KEY `codigo_4` (`codigo`),
        KEY `codigo_5` (`codigo`),
        KEY `codigo_6` (`codigo`),
        KEY `codigo_subida_tmp` (`codigo_subida_tmp`),
        KEY `id_pago` (`id_pago`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
    $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);
  
    $create="CREATE TABLE IF NOT EXISTS `letras` (
      `id` int(20) NOT NULL AUTO_INCREMENT,
      `id2` int(15) NOT NULL,
      `aceptante` varchar(100) NOT NULL,
      `tipo_aceptante` varchar(25) DEFAULT NULL,
      `direccion` varchar(100) DEFAULT NULL,
      `ciudad` varchar(40) DEFAULT NULL,
      `rut` varchar(15) NOT NULL,
      `girador` varchar(100) DEFAULT NULL,
      `requirente` varchar(100) DEFAULT NULL,
      `numero_protesto` int(20) NOT NULL,
      `moneda` varchar(20) NOT NULL,
      `monto` decimal(20,2) DEFAULT NULL,
      `gasto` int(6) DEFAULT NULL,
      `causa` varchar(10) DEFAULT NULL,
      `fecha_recibido` date DEFAULT NULL,
      `fecha_protesto` date NOT NULL,
      `fecha_vencimiento` date NOT NULL,
      `fecha_citacion` date DEFAULT NULL,
      `tipo` varchar(1) DEFAULT NULL,
      `estado` varchar(1) NOT NULL,
      `firmado` varchar(2) DEFAULT NULL,
      `universidad` varchar(1) DEFAULT NULL,
      `adicional` text DEFAULT NULL,
      `derechos` decimal(15,0) NOT NULL,
      `impuestos` decimal(15,0) NOT NULL,
      `rut_requirente` varchar(20) DEFAULT NULL,
      `rut_girador` varchar(20) DEFAULT NULL,
      `ot` bigint(20) DEFAULT NULL,
      KEY `id` (`id`),
      KEY `aceptante` (`aceptante`)
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
    
  $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);

      /* $qry      = "SELECT count(*) as total FROM install";
      $result   = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
      $row  = mysqli_fetch_array($result);
      if($row['total'] > 0)
      {
        return;
        die();
      } */
    //SE GENERAN TABLAS DE RBAC
    
    $create="CREATE TABLE IF NOT EXISTS `rbac_permissions` (
      `ID` int(11) NOT NULL auto_increment,
      `Lft` int(11) NOT NULL,
      `Rght` int(11) NOT NULL,
      `Title` char(64) NOT NULL,
      `Description` text NOT NULL,
      PRIMARY KEY  (`ID`),
      KEY `Title` (`Title`),
      KEY `Lft` (`Lft`),
      KEY `Rght` (`Rght`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_bin AUTO_INCREMENT=1;";
    
    $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);

    $create="CREATE TABLE IF NOT EXISTS `rbac_rolepermissions` (
      `RoleID` int(11) NOT NULL,
      `PermissionID` int(11) NOT NULL,
      `AssignmentDate` int(11) NOT NULL,
      PRIMARY KEY  (`RoleID`,`PermissionID`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_bin;";
    
    $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);
    
    $create="CREATE TABLE IF NOT EXISTS `rbac_roles` (
      `ID` int(11) NOT NULL auto_increment,
      `Lft` int(11) NOT NULL,
      `Rght` int(11) NOT NULL,
      `Title` varchar(128) NOT NULL,
      `Description` text NOT NULL,
      PRIMARY KEY  (`ID`),
      KEY `Title` (`Title`),
      KEY `Lft` (`Lft`),
      KEY `Rght` (`Rght`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_bin;";
    
    $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);
    
    $create="CREATE TABLE IF NOT EXISTS `rbac_userroles` (
      `UserID` int(11) NOT NULL,
      `RoleID` int(11) NOT NULL,
      `AssignmentDate` int(11) NOT NULL,
      PRIMARY KEY  (`UserID`,`RoleID`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_bin;";
    
    $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);
    $qry = "REPAIR TABLE usuarios_not";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    //Paso 1.0: Reset tabla => usuarios_not_new_sign2 // respaldo usarios
    $qry   = "CREATE TABLE IF NOT EXISTS usuarios_not_new_sign2 LIKE usuarios_not";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $qry   = "DROP TABLE usuarios_not_new_sign2";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    //Paso 1.1: Reset de todas las tablas de rbac
    $qry   = "TRUNCATE TABLE rbac_userroles";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $qry   = "TRUNCATE TABLE rbac_roles";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $qry   = "TRUNCATE TABLE rbac_rolepermissions";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $qry   = "TRUNCATE TABLE rbac_permissions";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
      /*
     * Insert Initial Table Data RBAC
     */
    
     $qry   = "INSERT INTO `rbac_permissions` (`ID`, `Lft`, `Rght`, `Title`, `Description`)
     VALUES (1, 0, 1, 'root', 'root');";
     $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
     
     $qry   = "INSERT INTO `rbac_rolepermissions` (`RoleID`, `PermissionID`, `AssignmentDate`)
     VALUES (1, 1, UNIX_TIMESTAMP());";
     $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
     
     $qry   = "INSERT INTO `rbac_roles` (`ID`, `Lft`, `Rght`, `Title`, `Description`)
     VALUES (1, 0, 1, 'root', 'root');";
     $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
     
     $qry   = "INSERT INTO `rbac_userroles` (`UserID`, `RoleID`, `AssignmentDate`)
     VALUES (1, 1, UNIX_TIMESTAMP());";
     $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);

    try {
        $rbac->Permissions->reset(true); 
        $rbac->Roles->reset(true);
        $rbac->Permissions->resetAssignments(true);
        $rbac->Roles->resetAssignments(true);
    } catch (Exception $e) {
        //echo 'Excepción capturada: ',  $e->getMessage(), "\n";
    }
    $qryr = "REPAIR TABLE usuarios_not";
    $result = mysqli_query($link, $qryr) or die("Error: ". mysqli_error($link)." ".$qryr);
    //Paso 2: Se limpia la tabla de usuario del sign
    $qry   = "CREATE TABLE IF NOT EXISTS usuarios_not_new_sign2 LIKE usuarios_not";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $qry = "REPAIR TABLE usuarios_not_new_sign2";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $qry   = "INSERT INTO usuarios_not_new_sign2 SELECT * FROM usuarios_not;";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $qry   = "TRUNCATE TABLE usuarios_not";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $raf        = "SHOW COLUMNS FROM usuarios_not LIKE 'nombreSocial' ";
    $result     = mysqli_query($link, $raf) or die("Error: ". mysqli_error($link)." ".$raf);
    $row_ot     = mysqli_fetch_array($result);
    if (!isset($row_ot)) {
        $r_up     = "ALTER TABLE `usuarios_not` ADD `nombreSocial` VARCHAR(100) NULL,
        ADD `estado` VARCHAR(10) NULL,
        ADD `creado` DATETIME NULL, 
        ADD `creado_por` VARCHAR(80) NULL, 
        ADD `modificado` DATETIME NULL, 
        ADD `modificado_por` INT(80) NULL,
        ADD `pass_hash` VARCHAR(300) NULL;";
        $result     = mysqli_query($link, $r_up) or die("Error: ". mysqli_error($link)." ".$r_up);
    }

    $raf        = "SHOW COLUMNS FROM usuarios_not LIKE 'pass_hash' ";
    $result     = mysqli_query($link, $raf) or die("Error: ". mysqli_error($link)." ".$raf);
    $row_ot     = mysqli_fetch_array($result);
    if( is_null($row_ot) || count($row_ot) == 0 )
    { 
        $r_up     = "ALTER TABLE `usuarios_not` ADD `nombreSocial` VARCHAR(100) NULL,
        ADD `creado` DATETIME NULL, 
        ADD `creado_por` VARCHAR(80) NULL, 
        ADD `modificado` DATETIME NULL, 
        ADD `modificado_por` INT(80) NULL,
        ADD `pass_hash` VARCHAR(300) NULL;";
        $result     = mysqli_query($link, $r_up) or die("Error: ". mysqli_error($link)." ".$r_up);
    }

    $raf        = "SHOW COLUMNS FROM repertorio_escrituras LIKE 'id_cardex' ";
    $result     = mysqli_query($link, $raf) or die("Error: ". mysqli_error($link)." ".$raf);
    $row_ot     = mysqli_fetch_array($result);
    if( is_null($row_ot) || count($row_ot) == 0 )
    { 
        $r_up     = "ALTER TABLE `repertorio_escrituras` ADD `id_cardex` BIGINT( 20 ) NULL";
        $result     = mysqli_query($link, $r_up) or die("Error: ". mysqli_error($link)." ".$r_up);
    }
    //Paso 3: Se Generan los usuarios basicos
    //ADMINISTRADOR
    $hashear = "administrador *//* sign123";
    $hash = password_hash($hashear, PASSWORD_DEFAULT);
    $qry   = "SELECT * FROM usuarios_not WHERE nombre = 'administrador'";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $row_usuario = mysqli_fetch_array($result);
    if(empty($row_usuario['id']))
    {
        $qry = "INSERT INTO usuarios_not 
        ( `nombre`,`nombreSocial`,`password`,`estado`,`creado`,`creado_por`,`pass_hash`)
        VALUES 
        ( 'administrador','ADMINISTRADOR','sign123','1','".date("Y-m-d H:i:s")."','1','".$hash."')";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
        $id_administrador = mysqli_insert_id($link);
    }else{
        $id_administrador = $row_usuario['id'];
    }
    //FUNCIONARIO
    $hashear = "funcionario *//* sign123";
    $hash = password_hash($hashear, PASSWORD_DEFAULT);
    $qry   = "SELECT * FROM usuarios_not WHERE nombre = 'funcionario'";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $row_usuario = mysqli_fetch_array($result);
    if(empty($row_usuario['id']))
    {
        $qry = "INSERT INTO usuarios_not 
        ( `nombre`,`nombreSocial`,`password`,`estado`,`creado`,`creado_por`,`pass_hash`)
        VALUES 
        ( 'funcionario','FUNCIONARIO','sign123','1','".date("Y-m-d H:i:s")."','1','".$hash."')";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
        $id_funcionario = mysqli_insert_id($link);
    }else{
        $id_funcionario = $row_usuario['id'];
    }
     //NOTARIO
    $hashear = "notario *//* sign123";
    $hash = password_hash($hashear, PASSWORD_DEFAULT);
    $qry   = "SELECT * FROM usuarios_not WHERE nombre = 'notario'";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $row_usuario = mysqli_fetch_array($result);
    if(empty($row_usuario['id']))
    {
        $qry = "INSERT INTO usuarios_not 
        ( `nombre`,`nombreSocial`,`password`,`estado`,`creado`,`creado_por`,`pass_hash`)
        VALUES 
        ( 'notario','NOTARIO','sign123','1','".date("Y-m-d H:i:s")."','1','".$hash."')";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
        $id_notario = mysqli_insert_id($link);
    }else{
        $id_notario = $row_usuario['id'];
    }
    //CAJERO
    $hashear = "cajero *//* sign123";
    $hash = password_hash($hashear, PASSWORD_DEFAULT);
    $qry   = "SELECT * FROM usuarios_not WHERE nombre = 'cajero'";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $row_usuario = mysqli_fetch_array($result);
    if(empty($row_usuario['id']))
    {
        $qry = "INSERT INTO usuarios_not 
        ( `nombre`,`nombreSocial`,`password`,`estado`,`creado`,`creado_por`,`pass_hash`)
        VALUES 
        ( 'cajero','CAJERO','sign123','1','".date("Y-m-d H:i:s")."','1','".$hash."')";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
        $id_cajero = mysqli_insert_id($link);
    }else{
        $id_cajero = $row_usuario['id'];
    }
    //REPERTORISTA
    $hashear = "repertorista *//* sign123";
    $hash = password_hash($hashear, PASSWORD_DEFAULT);
    $qry   = "SELECT * FROM usuarios_not WHERE nombre = 'repertorista'";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $row_usuario = mysqli_fetch_array($result);
    if(empty($row_usuario['id']))
    {
        $qry = "INSERT INTO usuarios_not 
        ( `nombre`,`nombreSocial`,`password`,`estado`,`creado`,`creado_por`,`pass_hash`)
        VALUES 
        ( 'repertorista','REPERTORISTA','sign123','1','".date("Y-m-d H:i:s")."','1','".$hash."')";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
        $id_repertorista = mysqli_insert_id($link);
    }else{
        $id_repertorista = $row_usuario['id'];
    }
    //JEFE REGISTRO
    $hashear = "jefe_registro *//* sign123";
    $hash = password_hash($hashear, PASSWORD_DEFAULT);
    $qry   = "SELECT * FROM usuarios_not WHERE nombre = 'jefe_registro'";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    $row_usuario = mysqli_fetch_array($result);
    if(empty($row_usuario['id']))
    {
        $qry = "INSERT INTO usuarios_not 
        ( `nombre`,`nombreSocial`,`password`,`estado`,`creado`,`creado_por`,`pass_hash`)
        VALUES 
        ( 'jefe_registro','JEFE REGISTRO','sign123','1','".date("Y-m-d H:i:s")."','1','".$hash."')";
    $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
        $id_jefe_registro = mysqli_insert_id($link);
    }else{
        $id_jefe_registro = $row_usuario['id'];
    }
    //Paso 4: Se Generan los roles
    $id_administrador_rbac  = $rbac->Roles->add('ADMINISTRADOR', 'Administrador del sistema', 1);
    $id_funcionario_rbac    = $rbac->Roles->add('FUNCIONARIO', 'Funcionario', $id_administrador_rbac);
    $id_caja_rbac           = $rbac->Roles->add('CAJERO', 'Encargado de la caja', $id_administrador_rbac);
    $id_repertorista_rbac   = $rbac->Roles->add('REPERTORISTA', 'Encargado de asignar repertorio', $id_administrador_rbac);
    $id_jefe_registro_rbac  = $rbac->Roles->add('JEFE_REGISTRO', 'Jefe de Registro', $id_administrador_rbac);
    $id_notario_rbac        = $rbac->Roles->add('NOTARIO', 'Notario', $id_administrador_rbac);
    $id_provisorio_rbac     = $rbac->Roles->add('PROVISORIO', 'Provisorio', 1);
    //Paso 5: Se Generan los permisos
    /* Permisos modulo de OT */
    $rbac->Permissions->add("ot_generar","Permite crear una nueva OT");  // nombre_permission , descripción del role
    $rbac->Permissions->add("ot_imprimir","Permite imprimir una OT");
    $rbac->Permissions->add("ot_editar","Permite editar una OT");
    $rbac->Permissions->add("ot_valorizar","Permite valorizar una OT");
    $rbac->Permissions->add("ot_valorizar_eliminar","Permite eliminar una valorizacion de una OT"); // no estan en la matriz
    $rbac->Permissions->add("ot_avances_generar","Permite agregar un estado de avance en una OT");
    $rbac->Permissions->add("ot_uaf","Permite agregar UAF (Registros de Operaciones en Efectivo) en una OT");
    $rbac->Permissions->add("ot_rndpa","Permite solicitar documento Registro Nacional de Deudores de Pensión Alimenticia en una OT");
    $rbac->Permissions->add("ot_historial","Permite visualizar el registro de todas las actividades hechas sobre la OT");
    $rbac->Permissions->add("ot_busqueda","Permite acceder a la busqueda del modulo OT");
    $rbac->Permissions->add("ot_busqueda_avanzada","Permite acceder a la busqueda avanzada del modulo OT");
    $rbac->Permissions->add("ot_avances_eliminar","Permite eliminar un avance en una OT");// no estan en la matriz
    $rbac->Permissions->add("ot_repertorio_generar","Permite asignar repertorio a una OT");
    $rbac->Permissions->add("ot_repertorio_editar","Permite editar el repertorio de una OT");
    $rbac->Permissions->add("libro_indice_esc_publica_generar","Permite generar el libro de indices de escritura pública");
    $rbac->Permissions->add("libro_repertorio_esc_publica_generar","Permite generar el libro de repertorio de escritura pública");
    $rbac->Permissions->add("libro_indice_vehiculo_generar","Permite generar el libro de indices de vehículos");
    $rbac->Permissions->add("libro_repertorio_vehiculo_generar","Permite generar el libro de repertorio de vehículos");
    $rbac->Permissions->add("uaf_generar_reporte","Permite generar reporte de UAF");
    $rbac->Permissions->add("ot_generar_reporte","Permite generar reporte de OT");
    $rbac->Permissions->add("ot_uaf_eliminar","Permite eliminar un registro UAF");
    $rbac->Permissions->add("nc_eliminar","Permite eliminar una NC");
    //NC
    $rbac->Permissions->add("nc_generar","Permite generar una Nota de Cobro");
    $rbac->Permissions->add("nc_imprimir","Permite imprimir una Nota de Cobro");
    /* Fin Permisos modulo de OT */
    /* Permisos modulo de CAJA */
    $rbac->Permissions->add("bhe_generar","Permite generar una BHE");
    $rbac->Permissions->add("bhe_informe_diario","Permite visualizar las BHE emitidas en tiempo real");
    $rbac->Permissions->add("bhe_buscar","Permite acceder al buscador de BHE");
    $rbac->Permissions->add("bhe_eliminar","Permite eliminar una BHE");
    $rbac->Permissions->add("bhe_visualizar","Permite visualizar una BHE emitida (pdf)");
    $rbac->Permissions->add("bhe_generar_informe","Permite visualizar un informe diario de BHE emitidas");
    /* Fin Permisos modulo de CAJA */
    /* Permisos modulo de Documentos Privados */
    $rbac->Permissions->add("doc_privado_local_generar","Permite generar un documento privado local");
    $rbac->Permissions->add("doc_privado_local_imprimir_para_firma","Permite acceder a la opcion de 'Imprimir para firma' en documento privado local");
    $rbac->Permissions->add("doc_privado_local_imprimir_notariada","Permite acceder a la opcion de 'Firma digital notariada' en documento privado local");
    $rbac->Permissions->add("doc_privado_local_visualizar","Permite visualizar el documento privado local generado");
    $rbac->Permissions->add("doc_privado_online_generar","Permite generar un documento privado online");
    $rbac->Permissions->add("doc_privado_externo_buscar_por_fecha","Permite acceder a la busqueda por fecha de un documento privado externo");
    $rbac->Permissions->add("doc_privado_externo_buscar_por_rut","Permite acceder a la busqueda por rut de un documento privado externo");
    $rbac->Permissions->add("doc_privado_externo_buscar_rut_y_fecha","Permite acceder a la busqueda por fecha y rut de un documento privado externo");
    $rbac->Permissions->add("doc_privado_externo_generar","Permite generar un documento privado externo");
    /* Fin Permisos modulo de Documentos Privados */
    /* Permisos modulo de Escrituras publicas */
    $rbac->Permissions->add("esc_publica_generar","Permite generar una escritura publica");
    $rbac->Permissions->add("esc_publica_extracto_generar","Permite generar un extracto");
    $rbac->Permissions->add("esc_publica_repertorio_generar","Permite generar repertorio de escrituras publicas");
    $rbac->Permissions->add("esc_publica_repertorio_editar","Permite editar el repertorio de escrituras publicas");
    /* Fin Permisos modulo de Escrituras publicas */
    /* Permisos modulo de Registros de firma */
    $rbac->Permissions->add("registro_firma_generar","Permite ingresar un registro de firma");
    $rbac->Permissions->add("registro_firma_editar","Permite editar un registro de firma");
    $rbac->Permissions->add("registro_firma_ver_actividades","Permite visualizar la actividad de un registro de firma");
    $rbac->Permissions->add("registro_firma_visualizar_pdf","Permite visualizar el pdf de un registro de firma");
    $rbac->Permissions->add("registro_firma_ver_documento_historial","Permite visualizar el historial de un registro de firma");
    $rbac->Permissions->add("registro_firma_indice_buscar","Permite buscar el indice de los registros de firma");
    $rbac->Permissions->add("registro_firma_indice_viualizar_pdf","Permite visualizar el informe pdf de el indice de los registros de firma");
    $rbac->Permissions->add("registro_firma_indice_actividades","Permite visualizar el registro de actividades de los registros de firma");
    /* Fin Permisos modulo de Registros de firma */
    /* Permisos modulo de UAF */
    $rbac->Permissions->add("uaf_generar","Permite generar un registro de UAF");
    $rbac->Permissions->add("uaf_buscar","Permite buscar un registro de UAF");
    $rbac->Permissions->add("uaf_exportar_a_excel","Permite exportar el informe excel de los registros de UAF");
    $rbac->Permissions->add("uaf_editar","Permite editar un registro de UAF");
    $rbac->Permissions->add("uaf_eliminar","Permite eliminar un registro de UAF");
    $rbac->Permissions->add("uaf_ver_historial","Permite visualizar el historial de los registros de UAF");
    /* Fin Permisos modulo de UAF */
    /* Permisos modulo de USUARIOS */
    $rbac->Permissions->add("usuarios_generar","Permite generar un usuario del sistema");
    $rbac->Permissions->add("usuarios_asignar_responsable","Permite asignar un jefe de registro");
    $rbac->Permissions->add("usuarios_asignar_ayudante","Permite asignar un ayudante de registro");
    $rbac->Permissions->add("usuarios_editar","Permite editar un usuario del sistema");
    $rbac->Permissions->add("usuarios_eliminar","Permite eliminar un usuario del sistema");
    $rbac->Permissions->add("usuarios_rol_generar","Permite generar un rol de un usuario del sistema");
    $rbac->Permissions->add("usuarios_rol_editar","Permite editar un rol de un usuario del sistema");
    $rbac->Permissions->add("usuarios_rol_eliminar","Permite eliminar un usuario del sistema");
    /* Fin Permisos modulo de USUARIOS */
    /* Permisos modulo de STEV */
    $rbac->Permissions->add("stev_solicitar_transferencia","Permite generar una solicitud de transferencia de STEV");
    $rbac->Permissions->add("stev_visualizar_pdf_solicitud_transferencia","Permite visualizar el comprobante pdf de una solicitud de transferencia de STEV");
    $rbac->Permissions->add("stev_solicitar_limitacion","Permite generar una solicitud de limitacion de STEV");
    $rbac->Permissions->add("stev_visualizar_pdf_solicitud_limitacion","Permite visualizar el comprobante pdf de una limitacion de transferencia de STEV");
    $rbac->Permissions->add("stev_enviar_documento_fundante","Permite enviar un documento fundante a traves del sistema stev al SRC");
    $rbac->Permissions->add("stev_consultar_estado_transferencia","Permite consultar el estado de una solicitud de transferencia de STEV");
    $rbac->Permissions->add("stev_consultar_anotaciones","Permite consultar el CAV");
    $rbac->Permissions->add("stev_consultar_solicitud_transferencia","Permite generar el comprobante de una solicitud de transferencia formato del SRC");
    $rbac->Permissions->add("stev_consultar_solicitud_limitacion","Permite generar el comprobante de una limitacion de transferencia formato del SRC");
    $rbac->Permissions->add("stev_generar_informe_diario_transferencias","Permite generar un informe con las solicitudes de transferencia del dia");
    $rbac->Permissions->add("stev_generar_informe_diario_anuladas","Permite generar un informe con las solicitudes anuladas de transferencia del dia");
    $rbac->Permissions->add("stev_generar_informe_diario_de_consultas","Permite generar un informe con las consultas al SRC");
    $rbac->Permissions->add("stev_adjuntar_comprobante_pago_appa","Permite generar una solicitud de transferencia de STEV");
    $rbac->Permissions->add("stev_reingresar_transferencia","Permite generar una solicitud de reingreso de transferencia de STEV");
    $rbac->Permissions->add("stev_generar_informe_diario_reingresos","Permite generar un informe con los reingresos de transferencia del dia");
    $rbac->Permissions->add("stev_visualizar_pdf_reingreso_transferencia","Permite visualizar el comprobante de reingreso");
    $rbac->Permissions->add("stev_buscador_de_rechazos","Permite acceder a la funcionalidad de buscador de rechazos");
    $rbac->Permissions->add("stev_ver_rechazadas_por_mes","Permite generar una solicitud de transferencia de STEV");
    $rbac->Permissions->add("stev_filtros_por_dias","Permite ver el filtro por dia STEV");
    $rbac->Permissions->add("stev_filtros_por_mes","Permite ver el filtro por mes STEV");
    $rbac->Permissions->add("stev_generar_repertorio","Permite generar repertorio de vehiculo");
    $rbac->Permissions->add("stev_editar_repertorio","Permite editar repertorio de vehiculo");
    /* Fin Permisos modulo de STEV */
    /* Permisos modulo de CLIENTES */
    $rbac->Permissions->add("clientes_generar","Permite generar un cliente");
    $rbac->Permissions->add("clientes_buscar","Permite buscar un cliente");
    $rbac->Permissions->add("clientes_editar","Permite editar un cliente");
    /* Fin Permisos modulo de CLIENTES */
    /* Permisos modulo de FIRMA DIGITAL */
    $rbac->Permissions->add("firma_autorizar","Permite acceder a la opcion 'Documentos por firmar'");
    $rbac->Permissions->add("firma_rechazar","Permite rechazar un documento enviado a firma");
    $rbac->Permissions->add("firma_postergar","Permite postergar firma de un documento enviado a firmar");
    $rbac->Permissions->add("firma_otros_documentos_pdf","Permite acceder a la opcion 'Otros documentos PDF'");
    $rbac->Permissions->add("firma_otros_documentos_texto","Permite acceder a la opcion 'Otros documentos texto'");
    $rbac->Permissions->add("firma_cadena_de_firmas","Permite acceder a la opcion 'Cadena de firma'");
    $rbac->Permissions->add("firma_documento_vincular","Permite acceder a la opcion 'Vincular documentos'");
    $rbac->Permissions->add("firma_documento_revocar","Permite acceder a la opcion 'Revocaciones'");
    $rbac->Permissions->add("firma_documento_bloquear","Permite acceder a la opcion 'Bloquear documentos'");
    $rbac->Permissions->add("firma_buscar_documentos","Permite acceder a la opcion 'Buscar documentos firmados'");
    $rbac->Permissions->add("firma_verificar_documentos","Permite acceder a la opcion 'Verificar documentos'");
    /* Fin Permisos modulo de FIRMA DIGITAL */
    /* Permisos modulo de LETRAS */
    $rbac->Permissions->add("letras_ingreso","Permite generar una Letra");
    $rbac->Permissions->add("letras_historial","Permite visualizar el historial de una Letra");
    $rbac->Permissions->add("letras_buscar","Permite buscar una Letra");
    $rbac->Permissions->add("letras_eliminar","Permite eliminar una Letra");
    $rbac->Permissions->add("letras_editar","Permite editar la data de una Letra");
    $rbac->Permissions->add("letras_informe_citacion","Permite generar el infomre de citacion de Letras");
    $rbac->Permissions->add("letras_informe_protesto","Permite generar el informe de protesto de Letras");
    $rbac->Permissions->add("letras_boletin_imprimir_completo","Permite imprimir el boletin completo de Letras");
    $rbac->Permissions->add("letras_boletin_imprimir_pagina","Permite imprimir una pagina del boletin de Letras");
    /* Fin Permisos modulo de LETRAS */
    /* Permisos modulo de INSTRUCCIONES */
    $rbac->Permissions->add("instruccion_ingreso","Permite generar una Instruccion");
    $rbac->Permissions->add("instruccion_buscar","Permite buscar una Instruccion");
    $rbac->Permissions->add("instruccion_libro","Permite generar el libro de instrucciones");
    /* Fin Permisos modulo de INSTRUCCIONES */
    /* Permisos modulo de OTRAS OPCIONES */
    $rbac->Permissions->add("otras_opciones_valorizar_documentos","Permite valorizar los formatos de documentos privados");
    $rbac->Permissions->add("otras_opciones_informe_autorizacion","Permite generar el informe de autorizacion de viajes de menores");
    $rbac->Permissions->add("otras_opciones_informes_consultas_rndpa","Permite generar el informe de consultas a RNDPA");
    /* Fin Permisos modulo de OTRAS OPCIONES */
    //Paso 6: Se Asignan los permisos a los roles
    //FUNCIONARIO
      //OT
      $rbac->assign('FUNCIONARIO','ot_generar');
      $rbac->assign('FUNCIONARIO','ot_imprimir');
      $rbac->assign('FUNCIONARIO','ot_editar');
      $rbac->assign('FUNCIONARIO','ot_valorizar');
      $rbac->assign('FUNCIONARIO','ot_avances_generar');
      $rbac->assign('FUNCIONARIO','ot_uaf');
      $rbac->assign('FUNCIONARIO','ot_rndpa');
      $rbac->assign('FUNCIONARIO','ot_historial');
      $rbac->assign('FUNCIONARIO','ot_busqueda');
      $rbac->assign('FUNCIONARIO','ot_busqueda_avanzada');
      $rbac->assign('FUNCIONARIO','uaf_generar_reporte');
      $rbac->assign('FUNCIONARIO','ot_generar_reporte');
      $rbac->assign('FUNCIONARIO','ot_avances_eliminar');
      $rbac->assign('FUNCIONARIO','ot_uaf_eliminar');
      //NC
      $rbac->assign('FUNCIONARIO','nc_imprimir');
      $rbac->assign('FUNCIONARIO','nc_generar');
      //Documento Privado
      $rbac->assign('FUNCIONARIO','doc_privado_local_generar');
      $rbac->assign('FUNCIONARIO','doc_privado_local_imprimir_para_firma');
      $rbac->assign('FUNCIONARIO','doc_privado_local_imprimir_notariada');
      $rbac->assign('FUNCIONARIO','doc_privado_local_visualizar');
      $rbac->assign('FUNCIONARIO','doc_privado_online_generar');
      $rbac->assign('FUNCIONARIO','doc_privado_externo_buscar_por_fecha');
      $rbac->assign('FUNCIONARIO','doc_privado_externo_buscar_por_rut');
      $rbac->assign('FUNCIONARIO','doc_privado_externo_buscar_rut_y_fecha');
      $rbac->assign('FUNCIONARIO','doc_privado_externo_generar');
      //Escritura publica
      $rbac->assign('FUNCIONARIO','esc_publica_generar');
      $rbac->assign('FUNCIONARIO','esc_publica_extracto_generar');
      //Registro de Firma
      $rbac->assign('FUNCIONARIO','registro_firma_generar');
      $rbac->assign('FUNCIONARIO','registro_firma_ver_actividades');
      $rbac->assign('FUNCIONARIO','registro_firma_visualizar_pdf');
      $rbac->assign('FUNCIONARIO','registro_firma_ver_documento_historial');
      $rbac->assign('FUNCIONARIO','registro_firma_indice_buscar');
      $rbac->assign('FUNCIONARIO','registro_firma_indice_viualizar_pdf');
      $rbac->assign('FUNCIONARIO','registro_firma_indice_actividades');
      //UAF
      $rbac->assign('FUNCIONARIO','uaf_generar');
      $rbac->assign('FUNCIONARIO','uaf_buscar');
      $rbac->assign('FUNCIONARIO','uaf_exportar_a_excel');
      $rbac->assign('FUNCIONARIO','uaf_editar');
      $rbac->assign('FUNCIONARIO','uaf_eliminar');
      $rbac->assign('FUNCIONARIO','uaf_ver_historial');
      //STEV
      $rbac->assign('FUNCIONARIO','stev_solicitar_transferencia');
      $rbac->assign('FUNCIONARIO','stev_visualizar_pdf_solicitud_transferencia');
      $rbac->assign('FUNCIONARIO','stev_solicitar_limitacion');
      $rbac->assign('FUNCIONARIO','stev_visualizar_pdf_solicitud_limitacion');
      $rbac->assign('FUNCIONARIO','stev_enviar_documento_fundante');
      $rbac->assign('FUNCIONARIO','stev_consultar_estado_transferencia');
      $rbac->assign('FUNCIONARIO','stev_consultar_anotaciones');
      $rbac->assign('FUNCIONARIO','stev_consultar_solicitud_transferencia');
      $rbac->assign('FUNCIONARIO','stev_consultar_solicitud_limitacion');
      $rbac->assign('FUNCIONARIO','stev_generar_informe_diario_transferencias');
      $rbac->assign('FUNCIONARIO','stev_generar_informe_diario_anuladas');
      $rbac->assign('FUNCIONARIO','stev_generar_informe_diario_de_consultas');
      $rbac->assign('FUNCIONARIO','stev_adjuntar_comprobante_pago_appa');
      $rbac->assign('FUNCIONARIO','stev_reingresar_transferencia');
      $rbac->assign('FUNCIONARIO','stev_generar_informe_diario_reingresos');
      $rbac->assign('FUNCIONARIO','stev_visualizar_pdf_reingreso_transferencia');
      $rbac->assign('FUNCIONARIO','stev_buscador_de_rechazos');
      $rbac->assign('FUNCIONARIO','stev_ver_rechazadas_por_mes');
      $rbac->assign('FUNCIONARIO','stev_filtros_por_dias');
      $rbac->assign('FUNCIONARIO','stev_filtros_por_mes');
      //Firma digital
      $rbac->assign('FUNCIONARIO','firma_otros_documentos_pdf');
      $rbac->assign('FUNCIONARIO','firma_otros_documentos_texto');
      $rbac->assign('FUNCIONARIO','firma_cadena_de_firmas');
      $rbac->assign('FUNCIONARIO','firma_buscar_documentos');
      $rbac->assign('FUNCIONARIO','firma_verificar_documentos');
      //LETRAS
      $rbac->assign('FUNCIONARIO','letras_ingreso');
      $rbac->assign('FUNCIONARIO','letras_historial');
      $rbac->assign('FUNCIONARIO','letras_buscar');
      $rbac->assign('FUNCIONARIO','letras_boletin_imprimir_completo');
      $rbac->assign('FUNCIONARIO','letras_boletin_imprimir_pagina');
      //INSTRUCCIONES
      $rbac->assign('FUNCIONARIO','instruccion_ingreso');
      $rbac->assign('FUNCIONARIO','instruccion_buscar');
      $rbac->assign('FUNCIONARIO','instruccion_libro');
      //OTRAS OPCIONES
      $rbac->assign('FUNCIONARIO','otras_opciones_informe_autorizacion');
      $rbac->assign('FUNCIONARIO','otras_opciones_informes_consultas_rndpa');

    //CAJERO
      //OT
      $rbac->assign('CAJERO','ot_busqueda');
      $rbac->assign('CAJERO','ot_busqueda_avanzada');
      //CAJA
      $rbac->assign('CAJERO','bhe_generar');
      $rbac->assign('CAJERO','bhe_buscar');
      $rbac->assign('CAJERO','bhe_eliminar');
      $rbac->assign('CAJERO','bhe_visualizar');
      $rbac->assign('CAJERO','bhe_generar_informe');
    //REPERTORISTA
      //OT
      $rbac->assign('REPERTORISTA','ot_imprimir');
      $rbac->assign('REPERTORISTA','ot_editar');
      $rbac->assign('REPERTORISTA','ot_avances_generar');
      $rbac->assign('REPERTORISTA','ot_busqueda');
      $rbac->assign('REPERTORISTA','ot_busqueda_avanzada');
      $rbac->assign('REPERTORISTA','ot_repertorio_generar');
      $rbac->assign('REPERTORISTA','ot_repertorio_editar');
      $rbac->assign('REPERTORISTA','libro_indice_esc_publica_generar');
      $rbac->assign('REPERTORISTA','libro_repertorio_esc_publica_generar');
      $rbac->assign('REPERTORISTA','libro_indice_vehiculo_generar');
      $rbac->assign('REPERTORISTA','libro_repertorio_vehiculo_generar');
      //Escritura publica
      $rbac->assign('REPERTORISTA','esc_publica_repertorio_generar');
      $rbac->assign('REPERTORISTA','esc_publica_repertorio_editar');
      //STEV
      $rbac->assign('REPERTORISTA','stev_generar_repertorio');
      $rbac->assign('REPERTORISTA','stev_editar_repertorio');
    //JEFE_REGISTRO
      //OT
      $rbac->assign('JEFE_REGISTRO','ot_generar');
      $rbac->assign('JEFE_REGISTRO','ot_imprimir');
      $rbac->assign('JEFE_REGISTRO','ot_editar');
      $rbac->assign('JEFE_REGISTRO','ot_valorizar');
      $rbac->assign('JEFE_REGISTRO','ot_avances_generar');
      $rbac->assign('JEFE_REGISTRO','ot_uaf');
      $rbac->assign('JEFE_REGISTRO','ot_rndpa');
      $rbac->assign('JEFE_REGISTRO','ot_historial');
      $rbac->assign('JEFE_REGISTRO','ot_busqueda');
      $rbac->assign('JEFE_REGISTRO','ot_busqueda_avanzada');
      $rbac->assign('JEFE_REGISTRO','uaf_generar_reporte');
      $rbac->assign('JEFE_REGISTRO','ot_generar_reporte');
      $rbac->assign('JEFE_REGISTRO','ot_avances_eliminar');
      $rbac->assign('JEFE_REGISTRO','ot_uaf_eliminar');
      //NC
      $rbac->assign('JEFE_REGISTRO','nc_imprimir');
      $rbac->assign('JEFE_REGISTRO','nc_generar');
      $rbac->assign('JEFE_REGISTRO','nc_eliminar');
      //Documento Privado
      $rbac->assign('JEFE_REGISTRO','doc_privado_local_generar');
      $rbac->assign('JEFE_REGISTRO','doc_privado_local_imprimir_para_firma');
      $rbac->assign('JEFE_REGISTRO','doc_privado_local_imprimir_notariada');
      $rbac->assign('JEFE_REGISTRO','doc_privado_local_visualizar');
      $rbac->assign('JEFE_REGISTRO','doc_privado_online_generar');
      $rbac->assign('JEFE_REGISTRO','doc_privado_externo_buscar_por_fecha');
      $rbac->assign('JEFE_REGISTRO','doc_privado_externo_buscar_por_rut');
      $rbac->assign('JEFE_REGISTRO','doc_privado_externo_buscar_rut_y_fecha');
      $rbac->assign('JEFE_REGISTRO','doc_privado_externo_generar');
      //Escritura publica
      $rbac->assign('JEFE_REGISTRO','esc_publica_generar');
      $rbac->assign('JEFE_REGISTRO','esc_publica_extracto_generar');
      //Registro de Firma
      $rbac->assign('JEFE_REGISTRO','registro_firma_generar');
      $rbac->assign('JEFE_REGISTRO','registro_firma_ver_actividades');
      $rbac->assign('JEFE_REGISTRO','registro_firma_visualizar_pdf');
      $rbac->assign('JEFE_REGISTRO','registro_firma_ver_documento_historial');
      $rbac->assign('JEFE_REGISTRO','registro_firma_indice_buscar');
      $rbac->assign('JEFE_REGISTRO','registro_firma_indice_viualizar_pdf');
      $rbac->assign('JEFE_REGISTRO','registro_firma_indice_actividades');
      //UAF
      $rbac->assign('JEFE_REGISTRO','uaf_generar');
      $rbac->assign('JEFE_REGISTRO','uaf_buscar');
      $rbac->assign('JEFE_REGISTRO','uaf_exportar_a_excel');
      $rbac->assign('JEFE_REGISTRO','uaf_editar');
      $rbac->assign('JEFE_REGISTRO','uaf_eliminar');
      $rbac->assign('JEFE_REGISTRO','uaf_ver_historial');
      //STEV
      $rbac->assign('JEFE_REGISTRO','stev_solicitar_transferencia');
      $rbac->assign('JEFE_REGISTRO','stev_visualizar_pdf_solicitud_transferencia');
      $rbac->assign('JEFE_REGISTRO','stev_solicitar_limitacion');
      $rbac->assign('JEFE_REGISTRO','stev_visualizar_pdf_solicitud_limitacion');
      $rbac->assign('JEFE_REGISTRO','stev_enviar_documento_fundante');
      $rbac->assign('JEFE_REGISTRO','stev_consultar_estado_transferencia');
      $rbac->assign('JEFE_REGISTRO','stev_consultar_anotaciones');
      $rbac->assign('JEFE_REGISTRO','stev_consultar_solicitud_transferencia');
      $rbac->assign('JEFE_REGISTRO','stev_consultar_solicitud_limitacion');
      $rbac->assign('JEFE_REGISTRO','stev_generar_informe_diario_transferencias');
      $rbac->assign('JEFE_REGISTRO','stev_generar_informe_diario_anuladas');
      $rbac->assign('JEFE_REGISTRO','stev_generar_informe_diario_de_consultas');
      $rbac->assign('JEFE_REGISTRO','stev_adjuntar_comprobante_pago_appa');
      $rbac->assign('JEFE_REGISTRO','stev_reingresar_transferencia');
      $rbac->assign('JEFE_REGISTRO','stev_generar_informe_diario_reingresos');
      $rbac->assign('JEFE_REGISTRO','stev_visualizar_pdf_reingreso_transferencia');
      $rbac->assign('JEFE_REGISTRO','stev_buscador_de_rechazos');
      $rbac->assign('JEFE_REGISTRO','stev_ver_rechazadas_por_mes');
      $rbac->assign('JEFE_REGISTRO','stev_filtros_por_dias');
      $rbac->assign('JEFE_REGISTRO','stev_filtros_por_mes');
      //Firma digital
      $rbac->assign('JEFE_REGISTRO','firma_autorizar');
      $rbac->assign('JEFE_REGISTRO','firma_rechazar');
      $rbac->assign('JEFE_REGISTRO','firma_otros_documentos_pdf');
      $rbac->assign('JEFE_REGISTRO','firma_otros_documentos_texto');
      $rbac->assign('JEFE_REGISTRO','firma_cadena_de_firmas');
      $rbac->assign('JEFE_REGISTRO','firma_documento_vincular');
      $rbac->assign('JEFE_REGISTRO','firma_documento_revocar');
      $rbac->assign('JEFE_REGISTRO','firma_documento_bloquear');
      $rbac->assign('JEFE_REGISTRO','firma_buscar_documentos');
      $rbac->assign('JEFE_REGISTRO','firma_verificar_documentos');
      //LETRAS
      $rbac->assign('JEFE_REGISTRO','letras_ingreso');
      $rbac->assign('JEFE_REGISTRO','letras_historial');
      $rbac->assign('JEFE_REGISTRO','letras_buscar');
      $rbac->assign('JEFE_REGISTRO','letras_eliminar');
      $rbac->assign('JEFE_REGISTRO','letras_editar');
      $rbac->assign('JEFE_REGISTRO','letras_informe_citacion');
      $rbac->assign('JEFE_REGISTRO','letras_informe_protesto');
      $rbac->assign('JEFE_REGISTRO','letras_boletin_imprimir_completo');
      $rbac->assign('JEFE_REGISTRO','letras_boletin_imprimir_pagina');
      //INSTRUCCIONES
      $rbac->assign('JEFE_REGISTRO','instruccion_ingreso');
      $rbac->assign('JEFE_REGISTRO','instruccion_buscar');
      $rbac->assign('JEFE_REGISTRO','instruccion_libro');
      //OTRAS OPCIONES
      $rbac->assign('JEFE_REGISTRO','otras_opciones_informe_autorizacion');
      $rbac->assign('JEFE_REGISTRO','otras_opciones_informes_consultas_rndpa');
    //NOTARIO
      //OT
      $rbac->assign('NOTARIO','ot_busqueda');
      $rbac->assign('NOTARIO','ot_busqueda_avanzada');
      $rbac->assign('NOTARIO','ot_valorizar_eliminar');
      $rbac->assign('NOTARIO','ot_avances_eliminar');
      $rbac->assign('NOTARIO','ot_uaf_eliminar');
      $rbac->assign('NOTARIO','nc_eliminar');
      //CAJA
      $rbac->assign('NOTARIO','bhe_informe_diario');
      //Firma digital
      $rbac->assign('NOTARIO','firma_autorizar');
      $rbac->assign('NOTARIO','firma_rechazar');
      $rbac->assign('NOTARIO','firma_postergar');
    //ADMINISTRADOR

      //CAJA
      $rbac->assign('ADMINISTRADOR','bhe_generar');
      $rbac->assign('ADMINISTRADOR','bhe_informe_diario');
      $rbac->assign('ADMINISTRADOR','bhe_buscar');
      $rbac->assign('ADMINISTRADOR','bhe_eliminar');
      $rbac->assign('ADMINISTRADOR','bhe_visualizar');
      $rbac->assign('ADMINISTRADOR','bhe_generar_informe');
      //OT
      $rbac->assign('ADMINISTRADOR','ot_generar');
      $rbac->assign('ADMINISTRADOR','ot_imprimir');
      $rbac->assign('ADMINISTRADOR','ot_editar');
      $rbac->assign('ADMINISTRADOR','ot_valorizar');
      $rbac->assign('ADMINISTRADOR','ot_avances_generar');
      $rbac->assign('ADMINISTRADOR','ot_uaf');
      $rbac->assign('ADMINISTRADOR','ot_rndpa');
      $rbac->assign('ADMINISTRADOR','ot_historial');
      $rbac->assign('ADMINISTRADOR','ot_busqueda');
      $rbac->assign('ADMINISTRADOR','ot_busqueda_avanzada');
      $rbac->assign('ADMINISTRADOR','ot_avances_eliminar');
      $rbac->assign('ADMINISTRADOR','ot_repertorio_generar');
      $rbac->assign('ADMINISTRADOR','ot_repertorio_editar');
      $rbac->assign('ADMINISTRADOR','ot_valorizar_eliminar');
      $rbac->assign('ADMINISTRADOR','ot_uaf_eliminar');
      $rbac->assign('ADMINISTRADOR','libro_indice_esc_publica_generar');
      $rbac->assign('ADMINISTRADOR','libro_repertorio_esc_publica_generar');
      $rbac->assign('ADMINISTRADOR','libro_indice_vehiculo_generar');
      $rbac->assign('ADMINISTRADOR','libro_repertorio_vehiculo_generar');
      $rbac->assign('ADMINISTRADOR','uaf_generar_reporte');
      $rbac->assign('ADMINISTRADOR','ot_generar_reporte');
      $rbac->assign('ADMINISTRADOR','nc_eliminar');

      //NC
      $rbac->assign('ADMINISTRADOR','nc_generar');
      $rbac->assign('ADMINISTRADOR','nc_imprimir');

      // DOC PRIVADO
      $rbac->assign('ADMINISTRADOR','doc_privado_local_generar');
      $rbac->assign('ADMINISTRADOR','doc_privado_local_imprimir_para_firma');
      $rbac->assign('ADMINISTRADOR','doc_privado_local_imprimir_notariada');
      $rbac->assign('ADMINISTRADOR','doc_privado_local_visualizar');
      $rbac->assign('ADMINISTRADOR','doc_privado_online_generar');
      $rbac->assign('ADMINISTRADOR','doc_privado_externo_buscar_por_fecha');
      $rbac->assign('ADMINISTRADOR','doc_privado_externo_buscar_por_rut');
      $rbac->assign('ADMINISTRADOR','doc_privado_externo_buscar_rut_y_fecha');
      $rbac->assign('ADMINISTRADOR','doc_privado_externo_generar');

      // ESCRITURAS PUBLICAS

      $rbac->assign('ADMINISTRADOR','esc_publica_generar');
      $rbac->assign('ADMINISTRADOR','esc_publica_extracto_generar');
      $rbac->assign('ADMINISTRADOR','esc_publica_repertorio_generar');
      $rbac->assign('ADMINISTRADOR','esc_publica_repertorio_editar');

      // REGISTRO DE FIRMA

      $rbac->assign('ADMINISTRADOR','registro_firma_generar');
      $rbac->assign('ADMINISTRADOR','registro_firma_editar');
      $rbac->assign('ADMINISTRADOR','registro_firma_ver_actividades');
      $rbac->assign('ADMINISTRADOR','registro_firma_visualizar_pdf');
      $rbac->assign('ADMINISTRADOR','registro_firma_ver_documento_historial');
      $rbac->assign('ADMINISTRADOR','registro_firma_indice_buscar');
      $rbac->assign('ADMINISTRADOR','registro_firma_indice_viualizar_pdf');
      $rbac->assign('ADMINISTRADOR','registro_firma_indice_actividades');

      //STEV
      $rbac->assign('ADMINISTRADOR','stev_solicitar_transferencia');
      $rbac->assign('ADMINISTRADOR','stev_visualizar_pdf_solicitud_transferencia');
      $rbac->assign('ADMINISTRADOR','stev_solicitar_limitacion');
      $rbac->assign('ADMINISTRADOR','stev_visualizar_pdf_solicitud_limitacion');
      $rbac->assign('ADMINISTRADOR','stev_enviar_documento_fundante');
      $rbac->assign('ADMINISTRADOR','stev_consultar_estado_transferencia');
      $rbac->assign('ADMINISTRADOR','stev_consultar_anotaciones');
      $rbac->assign('ADMINISTRADOR','stev_consultar_solicitud_transferencia');
      $rbac->assign('ADMINISTRADOR','stev_consultar_solicitud_limitacion');
      $rbac->assign('ADMINISTRADOR','stev_generar_informe_diario_transferencias');
      $rbac->assign('ADMINISTRADOR','stev_generar_informe_diario_anuladas');
      $rbac->assign('ADMINISTRADOR','stev_generar_informe_diario_de_consultas');
      $rbac->assign('ADMINISTRADOR','stev_adjuntar_comprobante_pago_appa');
      $rbac->assign('ADMINISTRADOR','stev_reingresar_transferencia');
      $rbac->assign('ADMINISTRADOR','stev_generar_informe_diario_reingresos');
      $rbac->assign('ADMINISTRADOR','stev_visualizar_pdf_reingreso_transferencia');
      $rbac->assign('ADMINISTRADOR','stev_buscador_de_rechazos');
      $rbac->assign('ADMINISTRADOR','stev_ver_rechazadas_por_mes');
      $rbac->assign('ADMINISTRADOR','stev_filtros_por_dias');
      $rbac->assign('ADMINISTRADOR','stev_filtros_por_mes');
      $rbac->assign('ADMINISTRADOR','stev_generar_repertorio');
      $rbac->assign('ADMINISTRADOR','stev_editar_repertorio');
      //Otras opciones
      $rbac->assign('ADMINISTRADOR','otras_opciones_valorizar_documentos');
      $rbac->assign('ADMINISTRADOR','otras_opciones_informe_autorizacion');
      $rbac->assign('ADMINISTRADOR','otras_opciones_informes_consultas_rndpa');
      //LETRAS
      $rbac->assign('ADMINISTRADOR','letras_ingreso');
      $rbac->assign('ADMINISTRADOR','letras_historial');
      $rbac->assign('ADMINISTRADOR','letras_buscar');
      $rbac->assign('ADMINISTRADOR','letras_eliminar');
      $rbac->assign('ADMINISTRADOR','letras_editar');
      $rbac->assign('ADMINISTRADOR','letras_informe_citacion');
      $rbac->assign('ADMINISTRADOR','letras_informe_protesto');
      $rbac->assign('ADMINISTRADOR','letras_boletin_imprimir_completo');
      $rbac->assign('ADMINISTRADOR','letras_boletin_imprimir_pagina');

      //INSTRUCCIONES
      $rbac->assign('ADMINISTRADOR','instruccion_ingreso');
      $rbac->assign('ADMINISTRADOR','instruccion_buscar');
      $rbac->assign('ADMINISTRADOR','instruccion_libro');
      
      //USUARIOS
      $rbac->assign('ADMINISTRADOR','usuarios_generar');
      $rbac->assign('ADMINISTRADOR','usuarios_asignar_responsable');
      $rbac->assign('ADMINISTRADOR','usuarios_asignar_ayudante');
      $rbac->assign('ADMINISTRADOR','usuarios_editar');
      $rbac->assign('ADMINISTRADOR','usuarios_eliminar');
      $rbac->assign('ADMINISTRADOR','usuarios_rol_generar');
      $rbac->assign('ADMINISTRADOR','usuarios_rol_editar');
      $rbac->assign('ADMINISTRADOR','usuarios_rol_eliminar');

      //UAF
      $rbac->assign('ADMINISTRADOR','uaf_generar');
      $rbac->assign('ADMINISTRADOR','uaf_buscar');
      $rbac->assign('ADMINISTRADOR','uaf_exportar_a_excel');
      $rbac->assign('ADMINISTRADOR','uaf_editar');
      $rbac->assign('ADMINISTRADOR','uaf_eliminar');
      $rbac->assign('ADMINISTRADOR','uaf_ver_historial');

      //CLIENTES
      $rbac->assign('ADMINISTRADOR','clientes_generar');
      $rbac->assign('ADMINISTRADOR','clientes_buscar');
      $rbac->assign('ADMINISTRADOR','clientes_editar');
      
      // FIRMA DIGITAL
      $rbac->assign('ADMINISTRADOR','firma_autorizar');
      $rbac->assign('ADMINISTRADOR','firma_rechazar');
      $rbac->assign('ADMINISTRADOR','firma_postergar');
      $rbac->assign('ADMINISTRADOR','firma_otros_documentos_pdf');
      $rbac->assign('ADMINISTRADOR','firma_otros_documentos_texto');
      $rbac->assign('ADMINISTRADOR','firma_cadena_de_firmas');
      $rbac->assign('ADMINISTRADOR','firma_documento_vincular');
      $rbac->assign('ADMINISTRADOR','firma_documento_revocar');
      $rbac->assign('ADMINISTRADOR','firma_documento_bloquear');
      $rbac->assign('ADMINISTRADOR','firma_buscar_documentos');
      $rbac->assign('ADMINISTRADOR','firma_verificar_documentos');
      //PROVISORIO
      $rbac->assign('PROVISORIO','ot_imprimir');
      $rbac->assign('PROVISORIO','ot_busqueda');
      $rbac->assign('PROVISORIO','ot_busqueda_avanzada');
      $rbac->assign('PROVISORIO','libro_indice_esc_publica_generar');
      $rbac->assign('PROVISORIO','libro_repertorio_esc_publica_generar');
      $rbac->assign('PROVISORIO','libro_indice_vehiculo_generar');
      $rbac->assign('PROVISORIO','libro_repertorio_vehiculo_generar');
      $rbac->assign('PROVISORIO','uaf_generar_reporte');
      $rbac->assign('PROVISORIO','ot_generar_reporte');
      $rbac->assign('PROVISORIO','nc_imprimir');
      $rbac->assign('PROVISORIO','bhe_buscar');
      $rbac->assign('PROVISORIO','bhe_generar_informe');
      $rbac->assign('PROVISORIO','doc_privado_local_generar');
      $rbac->assign('PROVISORIO','doc_privado_local_visualizar');
      $rbac->assign('PROVISORIO','esc_publica_generar');
      $rbac->assign('PROVISORIO','esc_publica_extracto_generar');
      $rbac->assign('PROVISORIO','esc_publica_repertorio_generar');
      $rbac->assign('PROVISORIO','registro_firma_ver_actividades');
      $rbac->assign('PROVISORIO','registro_firma_visualizar_pdf');
      $rbac->assign('PROVISORIO','registro_firma_ver_documento_historial');
      $rbac->assign('PROVISORIO','registro_firma_indice_buscar');
      $rbac->assign('PROVISORIO','registro_firma_indice_viualizar_pdf');
      $rbac->assign('PROVISORIO','registro_firma_indice_actividades');
      $rbac->assign('PROVISORIO','uaf_generar');
      $rbac->assign('PROVISORIO','uaf_buscar');
      $rbac->assign('PROVISORIO','uaf_exportar_a_excel');
      $rbac->assign('PROVISORIO','uaf_ver_historial');
      $rbac->assign('PROVISORIO','stev_visualizar_pdf_solicitud_transferencia');
      $rbac->assign('PROVISORIO','stev_visualizar_pdf_solicitud_limitacion');
      $rbac->assign('PROVISORIO','stev_consultar_estado_transferencia');
      $rbac->assign('PROVISORIO','stev_generar_informe_diario_transferencias');
      $rbac->assign('PROVISORIO','stev_generar_informe_diario_anuladas');
      $rbac->assign('PROVISORIO','stev_generar_informe_diario_de_consultas');
      $rbac->assign('PROVISORIO','stev_generar_informe_diario_reingresos');
      $rbac->assign('PROVISORIO','stev_visualizar_pdf_reingreso_transferencia');
      $rbac->assign('PROVISORIO','stev_buscador_de_rechazos');
      $rbac->assign('PROVISORIO','stev_filtros_por_dias');
      $rbac->assign('PROVISORIO','stev_filtros_por_mes');
      $rbac->assign('PROVISORIO','stev_generar_repertorio');
      $rbac->assign('PROVISORIO','clientes_buscar');
      $rbac->assign('PROVISORIO','firma_autorizar');
      $rbac->assign('PROVISORIO','firma_otros_documentos_pdf');
      $rbac->assign('PROVISORIO','firma_otros_documentos_texto');
      $rbac->assign('PROVISORIO','firma_cadena_de_firmas');
      $rbac->assign('PROVISORIO','firma_documento_vincular');
      $rbac->assign('PROVISORIO','firma_documento_revocar');
      $rbac->assign('PROVISORIO','firma_documento_bloquear');
      $rbac->assign('PROVISORIO','firma_buscar_documentos');
      $rbac->assign('PROVISORIO','firma_verificar_documentos');
      $rbac->assign('PROVISORIO','letras_historial');
      $rbac->assign('PROVISORIO','letras_informe_citacion');
      $rbac->assign('PROVISORIO','letras_informe_protesto');
      $rbac->assign('PROVISORIO','letras_boletin_imprimir_completo');
      $rbac->assign('PROVISORIO','letras_boletin_imprimir_pagina');
      $rbac->assign('PROVISORIO','instruccion_buscar');
      $rbac->assign('PROVISORIO','instruccion_libro');
      $rbac->assign('PROVISORIO','otras_opciones_informe_autorizacion');
      $rbac->assign('PROVISORIO','otras_opciones_informes_consultas_rndpa');
   
    //Paso 7: Se Asignan los usuarios a los roles
    $rbac->Users->assign('ADMINISTRADOR',$id_administrador); // titulo de rol,id user
    $rbac->Users->assign('FUNCIONARIO',$id_funcionario);
    $rbac->Users->assign('NOTARIO',$id_notario);
    $rbac->Users->assign('CAJERO',$id_cajero);
    $rbac->Users->assign('REPERTORISTA',$id_repertorista);
    $rbac->Users->assign('JEFE_REGISTRO',$id_jefe_registro);


    $qry              = "SELECT * FROM usuarios_not_new_sign2 ORDER BY id ASC";
    $result           = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    while($usus       = mysqli_fetch_array($result))
    {
      $qryu              = "SELECT id FROM usuarios_not WHERE UPPER(nombre) = '".strtoupper($usus['nombre'])."' ORDER BY id DESC LIMIT 1";
      $resultu           = mysqli_query($link, $qryu) or die("Error: ". mysqli_error($link)." ".$qry);
      $ususx             = mysqli_fetch_array($resultu);
      if($ususx['id'] == "")
      {
        $hashear = $usus['nombre']." *//* ".$usus['password'];
        $hash = password_hash($hashear, PASSWORD_DEFAULT);
        $qryinsert = "INSERT INTO usuarios_not 
        ( `nombre`,`nombreSocial`,`password`,`estado`,`creado`,`creado_por`,`pass_hash`)
        VALUES 
        ( '".$usus['nombre']."','".strtoupper($usus['nombre'])."','".$usus['password']."','1','".date("Y-m-d H:i:s")."','1','".$hash."')";
        $resultin = mysqli_query($link, $qryinsert) or die("Error: ". mysqli_error($link)." ".$qry);
        $id_usuario = mysqli_insert_id($link);
        if(intval($id_usuario) > 0)
        {
          $rbac->Users->assign('FUNCIONARIO',$id_usuario);
        }
      }
      
    }


    $raf        = "SHOW COLUMNS FROM usuarios_not LIKE 'token' ";
    $result     = mysqli_query($link, $raf) or die("Error: ". mysqli_error($link)." ".$raf);
    $row_ot     = mysqli_fetch_array($result);
    if( is_null($row_ot) || count($row_ot) == 0 )
        { 
            $r_up   =" ALTER TABLE `usuarios_not` ADD `token` TEXT DEFAULT '' ";
            $result = mysqli_query($link, $r_up) or die("Error: ". mysqli_error($link)." ".$r_up);
        }
    
    
    $raf        = "SHOW COLUMNS FROM cardex LIKE 'tipo_documento' ";
    $result     = mysqli_query($link, $raf) or die("Error: ". mysqli_error($link)." ".$raf);
    $row_ot     = mysqli_fetch_array($result);
    if( is_null($row_ot) || count($row_ot) == 0 )
        { 
            $r_up=" ALTER TABLE `cardex` ADD `tipo_documento` VARCHAR( 100 ) NOT NULL";
            $result     = mysqli_query($link, $r_up) or die("Error: ". mysqli_error($link)." ".$r_up);
            $r_up=" ALTER TABLE `cardex` ADD INDEX ( `tipo_documento` )";
            $result     = mysqli_query($link, $r_up) or die("Error: ". mysqli_error($link)." ".$r_up);

       
        }

    $qry              = "SELECT id,numero,tipo_documento FROM cardex WHERE numero IS NOT NULL AND tipo_documento = '' ORDER BY id DESC";
    $result           = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);
    while($otex       = mysqli_fetch_array($result))
    {
        //saco tipo de documento
        if(empty($otex['tipo_documento']))
        {
            $tipo_documento     = tipodeOT($link,$otex['numero']);
            if($tipo_documento <> null)
            {
                $update_ot =" UPDATE cardex SET 
                                        tipo_documento                = '".$tipo_documento['tipo']."'
                                  WHERE id = '".$otex['id']."'";
                $result2 = mysqli_query($link, $update_ot) or die("Error: ". mysqli_error($link)." ".$update_ot);
            }
        } 
    }

      $create="CREATE TABLE IF NOT EXISTS `relacion_jefe_ayudante` (
        `id` bigint(21) NOT NULL auto_increment,
        `id_jefe_registro` bigint(21) NULL,
        `ayudante` bigint(21) NULL,
        `creado` datetime default NULL,
        `creado_por` varchar(30) default NULL,
        `actualizado` datetime default NULL,
        `actualizado_por` varchar(30) default NULL,
        `eliminado` datetime default NULL,
        `eliminado_por` varchar(30) default NULL,
        PRIMARY KEY  (`id`),
        KEY `id_jefe_registro` (`id_jefe_registro`),
        KEY `ayudante` (`ayudante`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
      $result       = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);

      $result002		= "SELECT count(*) as total FROM relacion_jefe_ayudante";
      $result       = mysqli_query($link, $result002) or die("Error: ". mysqli_error($link)." ".$result002);
      $row121       = mysqli_fetch_array($result);
      if($row121['total'] == 0)
      {
        
        $insert_detalle = "INSERT INTO relacion_jefe_ayudante 
        ( `id_jefe_registro`,`ayudante`,`creado`,`creado_por`)
        VALUES 
        ( '".$id_administrador."','".$id_caja."','".date("Y-m-d H:i:s")."','".$id_administrador."')";
        $result       = mysqli_query($link, $insert_detalle) or die("Error: ". mysqli_error($link)." ".$insert_detalle);
        $insert_detalle = "INSERT INTO relacion_jefe_ayudante 
        ( `id_jefe_registro`,`ayudante`,`creado`,`creado_por`)
        VALUES 
        ( '".$id_repertorista."','".$id_funcionario."','".date("Y-m-d H:i:s")."','".$id_administrador."')";
        $result       = mysqli_query($link, $insert_detalle) or die("Error: ". mysqli_error($link)." ".$insert_detalle);
      }
      
      $qry = "ALTER TABLE `documentos_not_formatos` CONVERT TO CHARACTER SET utf8 COLLATE utf8_general_ci;";
      $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);

      $create="CREATE TABLE IF NOT EXISTS `certificados_inscripcion` (
        `id` bigint(21) NOT NULL auto_increment,
        `numero_firma` bigint(20) NOT NULL,
        `cliente` varchar(60) NOT NULL,
        `numero_operacion` varchar(50) NOT NULL,
        `repertorio` bigint(20) NOT NULL,
        `anho` int(4) NOT NULL,
        `cbr` varchar(30) NOT NULL,
        `caratula_cbr` varchar(30) NOT NULL,
        `fecha_i` date NOT NULL,
        PRIMARY KEY (`id`),
        KEY `numero_firma` (`numero_firma`,`cliente`,`numero_operacion`,`repertorio`,`anho`,`cbr`,`caratula_cbr`),
        KEY `fecha_i` (`fecha_i`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
      $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);

      $create="CREATE TABLE IF NOT EXISTS `tutoriales` (
        `id` bigint(21) NOT NULL auto_increment,
        `seccion` varchar(250) default NULL,
        `url` varchar(250) default NULL,
        `detalle` varchar(250) default NULL,
        `titulo` varchar(250) default NULL,
        `estado` varchar(50) default NULL,
        `url_seccion` varchar(250) default NULL,
        `fecha` datetime,
        PRIMARY KEY (`id`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
      $result  = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);


      $create="CREATE TABLE IF NOT EXISTS `failed_jobs` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `queue` text NOT NULL,
        `ref` varchar(255) NOT NULL,
        `obs` varchar(255) NOT NULL,
        `payload` longtext NOT NULL,
        `attempts` tinyint(3) NOT NULL,
        `last_failed_at` datetime NOT NULL,
        `created_at` datetime NOT NULL,
        PRIMARY KEY (`id`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
      $result = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);

      $create="CREATE TABLE IF NOT EXISTS `certificados_id_divididos` (
        `id` bigint(21) NOT NULL auto_increment,
        `numero_firma` varchar(250) default NULL,
        `creado` datetime,
        `creado_por` varchar(250) default NULL,
        PRIMARY KEY (`id`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
      $result       = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);

      $create="CREATE TABLE IF NOT EXISTS `certificados_divididos` (
        `id` bigint(21) NOT NULL auto_increment,
        `numero_firma` varchar(250) default NULL,
        `id_grupo` varchar(250) default NULL,
        `creado` datetime,
        `creado_por` varchar(250) default NULL,
        PRIMARY KEY (`id`),
        KEY `numero_firma` (`numero_firma`),
        KEY `id_grupo` (`id_grupo`)
      ) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
      
      $result       = mysqli_query($link, $create) or die("Error: ". mysqli_error($link)." ".$create);

      $qry   = "TRUNCATE TABLE tutoriales";
      $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);

      $qry   = "INSERT INTO `tutoriales` (`seccion`,`url`,`detalle`,`titulo`,`estado`,`url_seccion`,`fecha`) VALUES 
      ('Módulo de OT', 'https://youtu.be/gTXptofrDQ0', null, 'Reporte Ampliado de OT', '1', 'app/ot', '".date("Y-m-d H:i:s")."'),
      ('Módulo de OT', 'https://youtu.be/VwMbu3StOaY', null, 'Reporte', '1', 'app/ot', '".date("Y-m-d H:i:s")."'),
      ('Módulo de OT', 'https://youtu.be/7zmVsgaHe-4', null, 'Reporte UAF', '1', 'app/ot', '".date("Y-m-d H:i:s")."'),
      ('Módulo de OT', 'https://youtu.be/f2xd_T2RTeU', null, 'Registro de Avance', '1', 'app/ot', '".date("Y-m-d H:i:s")."'),
      ('Módulo de OT', 'https://youtu.be/IBd4Ms14DRw', null, 'Valorización OT', '1', 'app/ot', '".date("Y-m-d H:i:s")."'),
      ('Módulo de OT', 'https://youtu.be/OdHLqNqD8qk', null, 'Ingreso de Repertorio de Escritura Pública', '1', 'app/ot', '".date("Y-m-d H:i:s")."'),
      ('Módulo de OT', 'https://youtu.be/gmuwGxawBQM', null, 'Ingreso de UAF', '1', 'app/ot', '".date("Y-m-d H:i:s")."'),
      ('Módulo de OT', 'https://youtu.be/O_zAUuBrVmo', null, 'OT Ingreso', '1', 'app/ot', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Escritura Pública', 'https://youtu.be/ufSgt2QxxK8', null, 'Ingreso Manual de Repertorio de Escritura Pública', '1', 'app/escrituras_publicas', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Escritura Pública', 'https://youtu.be/AoAP_1cDazQ', null, 'Editar Repertorio de Escritura Pública', '1', 'app/escrituras_publicas', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Escritura Pública', 'https://youtu.be/Saa9-dUt5SE', null, 'Índice de Escrituras', '1', 'app/escrituras_publicas', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Escritura Pública', 'https://youtu.be/_5f3QCq6vNM', null, 'Libro de Repertorio de Escritura Pública', '1', 'app/escrituras_publicas', '".date("Y-m-d H:i:s")."'),
      ('Módulo de UAF', 'https://youtu.be/EgQdZ5SBfJU', null, 'Ingreso Manual UAF', '1', 'app/uaf', '".date("Y-m-d H:i:s")."'),
      ('Módulo de UAF', 'https://youtu.be/GIHde1Q84bk', null, 'Editar y Eliminar Registro de UAF', '1', 'app/uaf', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Registro de Firma', 'https://youtu.be/R9SN2Z3DTMQ', null, 'Ingreso Manual de Registro de Firma', '1', 'app/registro_firma', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Registro de Firma', 'https://youtu.be/_Wdhy1SutxY', null, 'Editar Registro de Firma', '1', 'app/registro_firma', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Registro de Firma', 'https://youtu.be/vaRe7-so0UE', null, 'Buscar Registro de Firma', '1', 'app/registro_firma', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Registro de Firma', 'https://youtu.be/chKtZPWeWIg', null, 'Índice de Registro de Firma', '1', 'app/registro_firma', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Documentos Privados', 'https://youtu.be/ZpMqG6p9L64', null, 'Documento Electrónico Notarial', '1', 'app/documento_privado', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Vehículos', 'https://youtu.be/HeQdlVhpdzM', null, 'Ingreso de Repertorio de Vehículos', '1', 'app/vehiculos', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Vehículos', 'https://youtu.be/VBAhcdNoCAQ', null, 'Editar Repertorio de Vehículos', '1', 'app/vehiculos', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Vehículos', 'https://youtu.be/adeRo8XqITo', null, 'Índice de Vehículos', '1', 'app/vehiculos', '".date("Y-m-d H:i:s")."'),
      ('Módulo de Vehículos', 'https://youtu.be/ALb7XrNtYvg', null, 'Libro de Repertorio de Vehículos', '1', 'app/vehiculos', '".date("Y-m-d H:i:s")."'),
      ('Administración', 'https://youtu.be/YvIZ9GlqTl0', null, 'Creación y Modificación de Usuario', '1', 'app/usuarios', '".date("Y-m-d H:i:s")."'),
      ('Administración', 'https://youtu.be/Stzi9OKxXGw', null, 'Asignar Jefe(a) de Registro y Ayudante', '1', 'app/usuarios', '".date("Y-m-d H:i:s")."'),
      ('Administración', 'https://youtu.be/6pFgcL2Oxzs', null, 'Creación y Modificación de Roles Usuarios', '1', 'app/usuarios', '".date("Y-m-d H:i:s")."'),
      ('Informes', 'https://youtu.be/hIkYeQgb-Ss', null, 'Generar Informe de Autorizaciones de Viaje de Menores', '1', 'app/informes', '".date("Y-m-d H:i:s")."'),
      ('Informes', 'https://youtu.be/F7ZNENsUxKk', null, 'Generar Informe de RNDPA', '1', 'app/informes', '".date("Y-m-d H:i:s")."');";
      $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);

      $qry = "INSERT INTO install 
      ( `version`,`fecha`)
      VALUES 
      ( '1','".date("Y-m-d H:i:s")."')";
      $result = mysqli_query($link, $qry) or die("Error: ". mysqli_error($link)." ".$qry);

      

      mysqli_close($link);
    return "Paso 1) Instalación correcta"; 
}

function tipodeOT($link,$ot)
  {
    $resp             = array('estado' => true,'mensaje' =>'','data'=>'');
    $tipo_documento   = array();
    if($ot == '' || $ot <= 0 || !is_numeric($ot))
    {
      $tipo_documento   = null;
    }else{
        $sql_ot        = "SELECT id FROM cardex WHERE numero = '".$ot."' LIMIT 1";
        $result        = mysqli_query($link, $sql_ot) or die("Error: ". mysqli_error($link)." ".$sql_ot);
        $row_ot        = mysqli_fetch_array($result);
        if(empty($row_ot['id']))
        {
          $tipo_documento   = null;
        }else{
          //verifico si OT existe en repertorio escritura
          $sqlv4         = "SELECT count(id) as total FROM repertorio_escrituras WHERE id_cardex = '".$row_ot['id']."' LIMIT 1";
          $result        = mysqli_query($link, $sqlv4) or die("Error: ". mysqli_error($link)." ".$sqlv4);          
          $rowv4       = mysqli_fetch_array($result);
          if($rowv4['total'] > 0)
          {
            $tipo_documento['tipo']   = 'escritura_publica';
            $tipo_documento['nombre'] = 'Escritura Publica';
          }else{
            $sqlv4         = "SELECT count(id) as total FROM cardex_solicitudes_local WHERE OT = '".$ot."' LIMIT 1";
            $result        = mysqli_query($link, $sqlv4) or die("Error: ". mysqli_error($link)." ".$sqlv4);
            $rowv4        = mysqli_fetch_array($result);
            if($rowv4['total'] > 0)
            {
              $tipo_documento['tipo']   = 'documento_privado';
              $tipo_documento['nombre'] = 'Documento Privado';

            }else{
                $sqlv4         = "SELECT count(id) as total FROM repertorio_vehiculos WHERE caratula = '".$ot."' LIMIT 1";
                $result        = mysqli_query($link, $sqlv4) or die("Error: ". mysqli_error($link)." ".$sqlv4);
                $rowv4        = mysqli_fetch_array($result);
                if($rowv4['total'] > 0)
                {
                  $tipo_documento['tipo']   = 'repertorio_vehiculo';
                  $tipo_documento['nombre'] = 'Vehiculo';
                }else{
                  $tipo_documento   = null;
                }
            }

          }
        }
    }
    return $tipo_documento;
  }

  function testPermisos(){
    $user = new entidades\usuario();
    $resp_validacion = $user->validarLogin('administrador', 'sign123', 'n');
    if(!$resp_validacion['estado']){
      return 'Error en login de prueba';
    }else{
      $bearerToken = $_SESSION["token"];
      $url = BASE_PATH . 'app/ot/api/';
      $curl = curl_init();
        
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
            "accion":"traePermisos"
        }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $bearerToken,
            ),
        )
        );
        $text = preg_replace( '/[\x{200B}-\x{200D}\x{FEFF}]/u', '', curl_exec($curl) );
		    $response_p = json_decode($text,true);

        $mensaje = is_null($response_p) ?  'Error en los permisos del sistema' : 'Permisos instalados correctamente';
        $resp_validacion = $user->logoutUser($_SESSION['id']);
        unset($_SESSION['loggedin']);
        unset($_SESSION['id']);
        unset($_SESSION['username']);
        unset($_SESSION['token']);
        $_SESSION = array();

        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        session_destroy();
        return $mensaje;
    }
  }

echo roleInit($rbac,$dbPass);
echo '<br><br>';
sleep(3);
echo "Paso 2) ".testPermisos();

?>