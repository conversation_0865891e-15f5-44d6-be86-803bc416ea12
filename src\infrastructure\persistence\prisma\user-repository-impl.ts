// Infrastructure - Persistence - User Repository Implementation
import { PrismaClient } from '@prisma/client'
import { UserRepository } from '../../../domain/user/repositories/user-repository'
import { User, UserRole, UserStatus } from '../../../domain/user/entities/user'
import { UserId } from '../../../domain/shared/value-objects/id'
import { Email } from '../../../domain/shared/value-objects/email'

export class PrismaUserRepository implements UserRepository {
  constructor(private prisma: PrismaClient) {}

  async findById(id: UserId): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { id: id.getValue() }
    })

    return userData ? this.toDomain(userData) : null
  }

  async findByUsername(username: string): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { username }
    })

    return userData ? this.toDomain(userData) : null
  }

  async findByEmail(email: Email): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { email: email.getValue() }
    })

    return userData ? this.toDomain(userData) : null
  }

  async findAll(): Promise<User[]> {
    const usersData = await this.prisma.user.findMany()
    return usersData.map(userData => this.toDomain(userData))
  }

  async findActiveUsers(): Promise<User[]> {
    const usersData = await this.prisma.user.findMany({
      where: { status: 'ACTIVE' }
    })
    return usersData.map(userData => this.toDomain(userData))
  }

  async save(user: User): Promise<void> {
    const userData = this.toPersistence(user)
    await this.prisma.user.create({
      data: userData
    })
  }

  async update(user: User): Promise<void> {
    const userData = this.toPersistence(user)
    await this.prisma.user.update({
      where: { id: user.getId().getValue() },
      data: userData
    })
  }

  async delete(id: UserId): Promise<void> {
    await this.prisma.user.delete({
      where: { id: id.getValue() }
    })
  }

  async existsByUsername(username: string): Promise<boolean> {
    const count = await this.prisma.user.count({
      where: { username }
    })
    return count > 0
  }

  async existsByEmail(email: Email): Promise<boolean> {
    const count = await this.prisma.user.count({
      where: { email: email.getValue() }
    })
    return count > 0
  }

  private toDomain(userData: any): User {
    return new User(
      new UserId(userData.id),
      userData.username,
      new Email(userData.email),
      userData.passwordHash,
      userData.nombreSocial,
      userData.role as UserRole,
      userData.status as UserStatus,
      userData.permissions ? JSON.parse(userData.permissions) : [],
      userData.createdAt,
      userData.updatedAt,
      userData.lastLoginAt
    )
  }

  private toPersistence(user: User): any {
    return {
      id: user.getId().getValue(),
      username: user.getUsername(),
      email: user.getEmail().getValue(),
      passwordHash: user.getPasswordHash(),
      nombreSocial: user.getNombreSocial(),
      role: user.getRole(),
      status: user.getStatus(),
      permissions: JSON.stringify(user.getPermissions()),
      createdAt: user.getCreatedAt(),
      updatedAt: user.getUpdatedAt(),
      lastLoginAt: user.getLastLoginAt()
    }
  }
}
