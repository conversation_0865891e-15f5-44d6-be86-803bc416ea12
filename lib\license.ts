import crypto from 'crypto'

// Configuración de licencias
const LICENSE_CONFIG = {
  // Clave secreta para cifrado (en producción debe estar en variables de entorno)
  SECRET_KEY: process.env.LICENSE_SECRET_KEY || 'e-notaria-license-key-2024',
  // URL del servidor de validación de licencias
  VALIDATION_URL: process.env.LICENSE_VALIDATION_URL || 'https://api.e-notaria.com/validate-license',
  // Algoritmo de cifrado
  ALGORITHM: 'aes-256-gcm',
  // Tiempo de cache de validación (en milisegundos)
  CACHE_DURATION: 24 * 60 * 60 * 1000, // 24 horas
}

// Interfaz para datos de licencia
export interface LicenseData {
  // Información del cliente
  clientId: string
  clientName: string
  clientEmail: string
  clientRut?: string
  
  // Información de la licencia
  licenseKey: string
  licenseType: 'trial' | 'standard' | 'premium' | 'enterprise'
  
  // Fechas
  issuedAt: string
  expiresAt: string
  
  // Características habilitadas
  features: {
    maxUsers: number
    maxDocuments: number
    hasReports: boolean
    hasBackup: boolean
    hasApiAccess: boolean
    hasCustomBranding: boolean
  }
  
  // Información del sistema
  systemInfo: {
    version: string
    installationId: string
    hardwareFingerprint?: string
  }
}

// Interfaz para respuesta de validación
export interface LicenseValidationResponse {
  valid: boolean
  license?: LicenseData
  error?: string
  remainingDays?: number
  warnings?: string[]
}

/**
 * Genera un fingerprint único del hardware/sistema
 */
export function generateHardwareFingerprint(): string {
  try {
    // En un entorno real, esto incluiría información del hardware
    // Por ahora usamos información disponible en Node.js
    const os = require('os')
    
    const systemInfo = {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      cpus: os.cpus().length,
      totalmem: os.totalmem(),
    }
    
    const fingerprint = crypto
      .createHash('sha256')
      .update(JSON.stringify(systemInfo))
      .digest('hex')
      .substring(0, 16)
    
    return fingerprint
  } catch (error) {
    // Fallback si no se puede generar el fingerprint
    return crypto.randomBytes(8).toString('hex')
  }
}

/**
 * Cifra los datos de la licencia
 */
export function encryptLicenseData(licenseData: LicenseData): string {
  try {
    const key = crypto.scryptSync(LICENSE_CONFIG.SECRET_KEY, 'salt', 32)
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipheriv(LICENSE_CONFIG.ALGORITHM, key, iv)

    let encrypted = cipher.update(JSON.stringify(licenseData), 'utf8', 'hex')
    encrypted += cipher.final('hex')

    const authTag = cipher.getAuthTag()

    // Combinar IV, authTag y datos cifrados
    const result = iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted

    // Codificar en base64 para facilitar el almacenamiento
    return Buffer.from(result).toString('base64')
  } catch (error) {
    throw new Error('Error al cifrar la licencia: ' + error.message)
  }
}

/**
 * Descifra los datos de la licencia
 */
export function decryptLicenseData(encryptedLicense: string): LicenseData {
  try {
    // Decodificar de base64
    const decoded = Buffer.from(encryptedLicense, 'base64').toString('utf8')
    const parts = decoded.split(':')

    if (parts.length !== 3) {
      throw new Error('Formato de licencia inválido')
    }

    const iv = Buffer.from(parts[0], 'hex')
    const authTag = Buffer.from(parts[1], 'hex')
    const encrypted = parts[2]

    const key = crypto.scryptSync(LICENSE_CONFIG.SECRET_KEY, 'salt', 32)
    const decipher = crypto.createDecipheriv(LICENSE_CONFIG.ALGORITHM, key, iv)
    decipher.setAuthTag(authTag)

    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return JSON.parse(decrypted)
  } catch (error) {
    throw new Error('Error al descifrar la licencia: ' + error.message)
  }
}

/**
 * Valida una licencia localmente (verificaciones básicas)
 */
export function validateLicenseLocally(licenseData: LicenseData): LicenseValidationResponse {
  try {
    const now = new Date()
    const expiresAt = new Date(licenseData.expiresAt)
    const issuedAt = new Date(licenseData.issuedAt)
    
    // Verificar fechas
    if (now < issuedAt) {
      return {
        valid: false,
        error: 'La licencia aún no es válida'
      }
    }
    
    if (now > expiresAt) {
      return {
        valid: false,
        error: 'La licencia ha expirado'
      }
    }
    
    // Calcular días restantes
    const remainingDays = Math.ceil((expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    
    // Generar advertencias
    const warnings: string[] = []
    if (remainingDays <= 30) {
      warnings.push(`La licencia expira en ${remainingDays} días`)
    }
    
    // Verificar fingerprint del hardware (si está disponible)
    if (licenseData.systemInfo.hardwareFingerprint) {
      const currentFingerprint = generateHardwareFingerprint()
      if (currentFingerprint !== licenseData.systemInfo.hardwareFingerprint) {
        warnings.push('El sistema ha cambiado desde la instalación original')
      }
    }
    
    return {
      valid: true,
      license: licenseData,
      remainingDays,
      warnings: warnings.length > 0 ? warnings : undefined
    }
  } catch (error) {
    return {
      valid: false,
      error: 'Error al validar la licencia: ' + error.message
    }
  }
}

/**
 * Valida una licencia online (verificación completa)
 */
export async function validateLicenseOnline(licenseKey: string): Promise<LicenseValidationResponse> {
  // Primero intentar descifrar la licencia localmente
  try {
    const licenseData = decryptLicenseData(licenseKey)
    const localValidation = validateLicenseLocally(licenseData)

    if (localValidation.valid) {
      // Si la licencia es válida localmente, intentar validación online como verificación adicional
      try {
        const response = await fetch(LICENSE_CONFIG.VALIDATION_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'e-NOTARIA/1.0.0'
          },
          body: JSON.stringify({
            licenseKey: licenseData.licenseKey,
            hardwareFingerprint: generateHardwareFingerprint(),
            timestamp: new Date().toISOString()
          }),
          signal: AbortSignal.timeout(5000) // Timeout de 5 segundos
        })

        if (response.ok) {
          const onlineResult = await response.json()
          return onlineResult
        }
      } catch (onlineError) {
        console.warn('Validación online falló, usando validación local:', onlineError.message)
      }

      // Retornar validación local si online falla
      return {
        ...localValidation,
        warnings: [
          ...(localValidation.warnings || []),
          'Validación online no disponible, usando validación local'
        ]
      }
    }

    return localValidation
  } catch (decryptError) {
    return {
      valid: false,
      error: 'Clave de licencia inválida o corrupta'
    }
  }
}

/**
 * Carga la licencia desde el almacenamiento local (solo servidor)
 */
export async function loadLocalLicense(): Promise<LicenseData | null> {
  // Solo funciona en el servidor
  if (typeof window !== 'undefined') {
    return null
  }

  try {
    const fs = require('fs').promises
    const path = require('path')

    const licensePath = path.join(process.cwd(), '.license')
    const encryptedLicense = await fs.readFile(licensePath, 'utf8')

    return decryptLicenseData(encryptedLicense)
  } catch (error) {
    return null
  }
}

/**
 * Guarda la licencia en el almacenamiento local (solo servidor)
 */
export async function saveLocalLicense(licenseData: LicenseData): Promise<void> {
  // Solo funciona en el servidor
  if (typeof window !== 'undefined') {
    throw new Error('saveLocalLicense solo puede ejecutarse en el servidor')
  }

  try {
    const fs = require('fs').promises
    const path = require('path')

    const encryptedLicense = encryptLicenseData(licenseData)
    const licensePath = path.join(process.cwd(), '.license')

    await fs.writeFile(licensePath, encryptedLicense, 'utf8')
  } catch (error) {
    throw new Error('Error al guardar la licencia: ' + error.message)
  }
}

/**
 * Genera una licencia de ejemplo para testing
 */
export function generateSampleLicense(clientName: string, clientEmail: string): LicenseData {
  const now = new Date()
  const expiresAt = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000) // 1 año
  
  return {
    clientId: crypto.randomUUID(),
    clientName,
    clientEmail,
    licenseKey: crypto.randomBytes(16).toString('hex').toUpperCase(),
    licenseType: 'standard',
    issuedAt: now.toISOString(),
    expiresAt: expiresAt.toISOString(),
    features: {
      maxUsers: 10,
      maxDocuments: 1000,
      hasReports: true,
      hasBackup: true,
      hasApiAccess: false,
      hasCustomBranding: true
    },
    systemInfo: {
      version: '1.0.0',
      installationId: crypto.randomUUID(),
      hardwareFingerprint: generateHardwareFingerprint()
    }
  }
}
