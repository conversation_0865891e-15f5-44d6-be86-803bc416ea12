"use client"

import { useState } from "react"

interface HistorialTabProps {
  otId?: string
  numeroOT?: string
}

interface HistorialEntry {
  id: string
  accion: string
  descripcion: string
  estadoAnterior?: string
  estadoNuevo?: string
  usuario: string
  fecha: string
  datos?: any
}

export function HistorialTab({ otId, numeroOT }: HistorialTabProps) {
  // Mock historial data
  const [historial] = useState<HistorialEntry[]>([
    {
      id: '1',
      accion: 'CREADA',
      descripcion: 'OT creada en el sistema',
      estadoNuevo: 'CREADA',
      usuario: 'ADMIN',
      fecha: new Date().toISOString(),
      datos: {
        tipoDocumento: 'escritura_publica',
        prioridad: 'MEDIA'
      }
    }
  ])

  if (!otId) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">📚</div>
        <p className="text-gray-600">Debe crear la OT primero para ver el historial</p>
      </div>
    )
  }

  const getAccionIcon = (accion: string) => {
    switch (accion) {
      case 'CREADA': return '🆕'
      case 'EDITADA': return '✏️'
      case 'ESTADO_CAMBIADO': return '🔄'
      case 'COMPARECIENTE_AGREGADO': return '👤'
      case 'COMPARECIENTE_ELIMINADO': return '👤❌'
      case 'VALORIZACION_AGREGADA': return '💰'
      case 'AVANCE_REGISTRADO': return '📋'
      case 'REPERTORIO_ACTUALIZADO': return '📚'
      case 'RNDPA_GENERADO': return '📋'
      default: return '📝'
    }
  }

  const getAccionColor = (accion: string) => {
    switch (accion) {
      case 'CREADA': return 'bg-green-100 text-green-800'
      case 'EDITADA': return 'bg-blue-100 text-blue-800'
      case 'ESTADO_CAMBIADO': return 'bg-purple-100 text-purple-800'
      case 'COMPARECIENTE_AGREGADO': return 'bg-teal-100 text-teal-800'
      case 'COMPARECIENTE_ELIMINADO': return 'bg-red-100 text-red-800'
      case 'VALORIZACION_AGREGADA': return 'bg-yellow-100 text-yellow-800'
      case 'AVANCE_REGISTRADO': return 'bg-indigo-100 text-indigo-800'
      case 'REPERTORIO_ACTUALIZADO': return 'bg-orange-100 text-orange-800'
      case 'RNDPA_GENERADO': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      <div className="bg-gray-600 text-white p-4 rounded-t-lg">
        <h3 className="text-lg font-medium">
          Historial - OT: {numeroOT}
        </h3>
        <p className="text-sm opacity-90">
          Registro completo de cambios y actividades
        </p>
      </div>

      <div className="bg-gray-50 p-6 rounded-b-lg border border-gray-200">
        {historial.length > 0 ? (
          <div className="space-y-4">
            {historial.map((entry, index) => (
              <div key={entry.id} className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-start space-x-4">
                  {/* Timeline indicator */}
                  <div className="flex flex-col items-center">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full text-sm">
                      {getAccionIcon(entry.accion)}
                    </div>
                    {index < historial.length - 1 && (
                      <div className="w-px h-8 bg-gray-300 mt-2"></div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getAccionColor(entry.accion)}`}>
                        {entry.accion}
                      </span>
                      
                      {entry.estadoAnterior && entry.estadoNuevo && (
                        <div className="flex items-center space-x-1 text-xs text-gray-500">
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            {entry.estadoAnterior}
                          </span>
                          <span>→</span>
                          <span className="px-2 py-1 bg-blue-100 rounded">
                            {entry.estadoNuevo}
                          </span>
                        </div>
                      )}
                    </div>

                    <h4 className="font-medium text-gray-900 mb-1">
                      {entry.descripcion}
                    </h4>

                    {/* Additional data */}
                    {entry.datos && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                        <details>
                          <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                            Ver detalles
                          </summary>
                          <pre className="mt-2 text-gray-700 whitespace-pre-wrap">
                            {JSON.stringify(entry.datos, null, 2)}
                          </pre>
                        </details>
                      </div>
                    )}

                    <div className="flex items-center space-x-4 text-xs text-gray-500 mt-2">
                      <span className="flex items-center">
                        👤 {entry.usuario}
                      </span>
                      <span className="flex items-center">
                        📅 {new Date(entry.fecha).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">📚</div>
            <p>No hay historial disponible</p>
            <p className="text-sm">El historial se generará automáticamente con las actividades</p>
          </div>
        )}

        {/* Export Options */}
        <div className="mt-6 flex justify-end space-x-3">
          <button className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
            📄 Exportar PDF
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
            📊 Exportar Excel
          </button>
        </div>

        {/* Statistics */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <p className="text-2xl font-bold text-blue-600">{historial.length}</p>
            <p className="text-sm text-gray-600">Total Eventos</p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <p className="text-2xl font-bold text-green-600">
              {historial.filter(h => h.accion === 'CREADA').length}
            </p>
            <p className="text-sm text-gray-600">Creaciones</p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <p className="text-2xl font-bold text-yellow-600">
              {historial.filter(h => h.accion === 'EDITADA').length}
            </p>
            <p className="text-sm text-gray-600">Ediciones</p>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200 text-center">
            <p className="text-2xl font-bold text-purple-600">
              {historial.filter(h => h.accion === 'ESTADO_CAMBIADO').length}
            </p>
            <p className="text-sm text-gray-600">Cambios Estado</p>
          </div>
        </div>
      </div>
    </div>
  )
}
