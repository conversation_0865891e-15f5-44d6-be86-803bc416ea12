// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modelo de Usuario
model User {
  id          String   @id @default(cuid())
  username    String   @unique
  email       String?  @unique
  password    String
  nombreSocial String?
  estado      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLogin   DateTime?

  // Relaciones
  sessions       Session[]
  userRoles      UserRole[]
  activeSessions ActiveSession[]

  @@map("users")
}

// Modelo de Sesión para NextAuth
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Modelo de Cuenta para NextAuth (OAuth providers)
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// Modelo de Token de Verificación
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Sistema RBAC - Roles
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  userRoles   UserRole[]
  rolePermissions RolePermission[]

  @@map("rbac_roles")
}

// Sistema RBAC - Permisos
model Permission {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  module      String?  // Módulo al que pertenece (ot, documentos, etc.)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  rolePermissions RolePermission[]

  @@map("rbac_permissions")
}

// Sistema RBAC - Relación Usuario-Rol
model UserRole {
  id     String @id @default(cuid())
  userId String
  roleId String

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("rbac_user_roles")
}

// Sistema RBAC - Relación Rol-Permiso
model RolePermission {
  id           String @id @default(cuid())
  roleId       String
  permissionId String

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("rbac_role_permissions")
}

// Modelo de Auditoría para Seguridad
model AuditLog {
  id        String   @id @default(cuid())
  action    String   // LOGIN_SUCCESS, LOGIN_FAILED, PERMISSION_DENIED, etc.
  details   String?
  ip        String?
  userAgent String?
  userId    String?
  metadata  Json?    // Información adicional en formato JSON
  createdAt DateTime @default(now())

  @@map("audit_logs")
  @@index([action])
  @@index([userId])
  @@index([createdAt])
}

// Modelo para Control de Intentos de Login
model LoginAttempt {
  id          String   @id @default(cuid())
  ip          String
  username    String?
  successful  Boolean
  userAgent   String?
  createdAt   DateTime @default(now())

  @@map("login_attempts")
  @@index([ip, createdAt])
  @@index([username, createdAt])
}

// Modelo para Configuración del Sistema
model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String
  description String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}

// === SISTEMA OT (ÓRDENES DE TRABAJO) ===

// Tabla principal de OT
model OrdenTrabajo {
  id                    String   @id @default(cuid())
  numeroOT              String   @unique
  tipoDocumento         String   // escritura_publica, documento_privado, etc.
  fechaOT               DateTime @default(now())

  // Repertorio
  numeroRepertorio      String?
  fechaRepertorio       DateTime?
  fojaRepertorio        String?
  folioProtocolo        String?
  fojasProtocolo        Int?
  notarioId             String?

  // Materia
  materiaId             String?
  materiaDetalle        String

  // Cliente principal
  clienteId             String?
  clienteCargoId        String?

  // Gestión
  gestora               String?
  numeroWorkflow        String?
  jefeRegistroId        String?
  ayudanteId            String?
  observaciones         String?

  // Estados y control
  estado                String   @default("CREADA")
  prioridad             String   @default("MEDIA")
  fechaVencimiento      DateTime?
  fechaCompletada       DateTime?

  // UAF/ROE
  esRoe                 Boolean  @default(false)
  montoUaf              Decimal?

  // Auditoría
  creadoPor             String
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  eliminadoAt           DateTime?
  eliminadoPor          String?

  // Relaciones
  comparecientes        OTCompareciente[]
  avances               OTAvance[]
  valorizaciones        OTValorizacion[]
  documentos            OTDocumento[]
  rndpa                 OTRndpa[]
  historial             OTHistorial[]

  @@map("ot")
}

// Comparecientes de la OT
model OTCompareciente {
  id                String      @id @default(cuid())
  otId              String
  rut               String
  nombres           String
  apellidoPaterno   String
  apellidoMaterno   String?
  calidad           String      // COMPRADOR, VENDEDOR, etc.
  esChileno         Boolean     @default(true)
  createdAt         DateTime    @default(now())

  // Relaciones
  ot                OrdenTrabajo @relation(fields: [otId], references: [id], onDelete: Cascade)

  @@map("ot_comparecientes")
  @@index([otId])
}

// Avances/Estados de la OT
model OTAvance {
  id                String      @id @default(cuid())
  otId              String
  workflowId        String?
  workflow          String?
  eventoId          String?
  evento            String?
  notaId            String?
  nota              String?
  observacion       String?
  alerta            Boolean     @default(false)
  defineCiclo       Boolean     @default(false)
  tareaTerminada    Boolean     @default(true)
  creadoPor         String
  createdAt         DateTime    @default(now())

  // Relaciones
  ot                OrdenTrabajo @relation(fields: [otId], references: [id], onDelete: Cascade)

  @@map("ot_avances")
  @@index([otId])
}

// Valorizaciones de la OT
model OTValorizacion {
  id                      String      @id @default(cuid())
  otId                    String
  tipo                    String      // derechos, impuestos, diligencias
  detalleId               Int?
  detalle                 String?
  baseImpuesto            Decimal?
  baseImpuestoOrigen      Decimal?
  tipoMoneda              String?
  fechaVencimientoImpuesto DateTime?
  monto                   Decimal?
  valorIpc                Decimal?
  valorInteres            Decimal?
  intereses               Decimal?
  creadoPor               String
  createdAt               DateTime    @default(now())
  updatedAt               DateTime    @updatedAt

  // Relaciones
  ot                      OrdenTrabajo @relation(fields: [otId], references: [id], onDelete: Cascade)

  @@map("ot_valorizacion")
  @@index([otId])
}

// Documentos asociados a la OT
model OTDocumento {
  id                String      @id @default(cuid())
  otId              String
  tipo              String      // copia_escritura, bhe, etc.
  nombre            String?
  archivo           Bytes?
  estado            String?
  firma             String?
  creadoPor         String
  createdAt         DateTime    @default(now())

  // Relaciones
  ot                OrdenTrabajo @relation(fields: [otId], references: [id], onDelete: Cascade)

  @@map("ot_documentos")
  @@index([otId])
}

// RNDPA de la OT
model OTRndpa {
  id                    String      @id @default(cuid())
  otId                  String
  rut                   String
  fecha                 DateTime?
  folio                 Int?
  codigoVerificacion    String?
  usuarioOficio         String?
  estado                String?
  creadoPor             String
  createdAt             DateTime    @default(now())

  // Relaciones
  ot                    OrdenTrabajo @relation(fields: [otId], references: [id], onDelete: Cascade)

  @@map("ot_rndpa")
  @@index([otId])
}

// Historial de cambios de la OT
model OTHistorial {
  id                String      @id @default(cuid())
  otId              String
  accion            String      // CREADA, EDITADA, ESTADO_CAMBIADO, etc.
  descripcion       String?
  estadoAnterior    String?
  estadoNuevo       String?
  datos             Json?       // Datos adicionales del cambio
  creadoPor         String
  createdAt         DateTime    @default(now())

  // Relaciones
  ot                OrdenTrabajo @relation(fields: [otId], references: [id], onDelete: Cascade)

  @@map("ot_historial")
  @@index([otId])
  @@index([createdAt])
}

// === CATÁLOGOS Y CONFIGURACIÓN ===

// Tipos de materia para repertorio
model TipoMateria {
  id                String      @id @default(cuid())
  tipo              String      @unique
  descripcion       String?
  activo            Boolean     @default(true)
  createdAt         DateTime    @default(now())

  // Relaciones
  calidades         TipoMateriaCalidad[]

  @@map("tipo_materia_repertorio")
}

// Calidades/comparecientes por tipo de materia
model TipoMateriaCalidad {
  id                String      @id @default(cuid())
  tipoMateriaId     String
  nombre            String
  activo            Boolean     @default(true)
  createdAt         DateTime    @default(now())

  // Relaciones
  tipoMateria       TipoMateria @relation(fields: [tipoMateriaId], references: [id], onDelete: Cascade)

  @@map("tipo_materia_repertorio_comparecientes")
  @@unique([tipoMateriaId, nombre])
  @@index([tipoMateriaId])
}

// Workflow y estados
model Workflow {
  id                String      @id @default(cuid())
  tipo              String      // escritura_publica, documento_privado, etc.
  workflow          String
  orden             Int?
  activo            Boolean     @default(true)
  createdAt         DateTime    @default(now())

  // Relaciones
  estados           WorkflowEstado[]

  @@map("ot_workflow")
  @@unique([tipo, workflow])
}

// Estados del workflow
model WorkflowEstado {
  id                String      @id @default(cuid())
  workflowId        String
  estado            String
  orden             Int?
  icon              String?
  activo            Boolean     @default(true)
  createdAt         DateTime    @default(now())

  // Relaciones
  workflow          Workflow    @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  notas             WorkflowNota[]

  @@map("ot_workflow_estados")
  @@unique([workflowId, estado])
  @@index([workflowId])
}

// Notas del workflow
model WorkflowNota {
  id                String      @id @default(cuid())
  estadoId          String
  nota              String
  defineCiclo       Boolean     @default(false)
  activo            Boolean     @default(true)
  createdAt         DateTime    @default(now())

  // Relaciones
  estado            WorkflowEstado @relation(fields: [estadoId], references: [id], onDelete: Cascade)

  @@map("ot_workflow_notas")
  @@unique([estadoId, nota])
  @@index([estadoId])
}

// Modelo para Sesiones Activas (control granular)
model ActiveSession {
  id           String   @id @default(cuid())
  userId       String
  sessionToken String   @unique
  ip           String?
  userAgent    String?
  lastActivity DateTime @default(now())
  expiresAt    DateTime
  createdAt    DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("active_sessions")
  @@index([userId])
  @@index([expiresAt])
}
