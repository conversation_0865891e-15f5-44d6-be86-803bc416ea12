// Reset OT Data Script
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function resetOTData() {
  console.log('🧹 Resetting OT data...')

  try {
    // Delete all OT related data in correct order
    console.log('🗑️ Deleting existing OT data...')
    
    await prisma.oTHistorial.deleteMany()
    await prisma.oTRndpa.deleteMany()
    await prisma.oTDocumento.deleteMany()
    await prisma.oTValorizacion.deleteMany()
    await prisma.oTAvance.deleteMany()
    await prisma.oTCompareciente.deleteMany()
    await prisma.ordenTrabajo.deleteMany()
    
    console.log('✅ Deleted all existing OT data')

    // Create fresh sample data
    console.log('🌱 Creating fresh sample data...')
    
    const adminUser = await prisma.user.findUnique({ where: { username: 'ADMI<PERSON>' } })
    if (!adminUser) {
      throw new Error('ADMIN user not found')
    }

    // Create sample OTs
    const sampleOTs = [
      {
        numeroOT: 'OT-2025-001',
        tipoDocumento: 'escritura_publica',
        materiaDetalle: 'Compraventa de casa habitación ubicada en Santiago, Región Metropolitana',
        estado: 'CREADA',
        prioridad: 'MEDIA',
        gestora: 'INMOBILIARIA EJEMPLO S.A.',
        numeroWorkflow: 'WF-2025-001',
        observaciones: 'OT de ejemplo para pruebas del sistema',
        comparecientes: [
          {
            rut: '12345678-9',
            nombres: 'JUAN CARLOS',
            apellidoPaterno: 'PEREZ',
            apellidoMaterno: 'GONZALEZ',
            calidad: 'COMPRADOR',
            esChileno: true
          },
          {
            rut: '98765432-1',
            nombres: 'MARIA ELENA',
            apellidoPaterno: 'RODRIGUEZ',
            apellidoMaterno: 'SILVA',
            calidad: 'VENDEDOR',
            esChileno: true
          }
        ]
      },
      {
        numeroOT: 'OT-2025-002',
        tipoDocumento: 'documento_privado',
        materiaDetalle: 'Contrato de arriendo de local comercial en Las Condes',
        estado: 'EN_PROCESO',
        prioridad: 'ALTA',
        gestora: 'INMOBILIARIA PREMIUM LTDA.',
        numeroWorkflow: 'WF-2025-002',
        observaciones: 'Cliente requiere urgencia en la tramitación',
        comparecientes: [
          {
            rut: '11111111-1',
            nombres: 'CARLOS EDUARDO',
            apellidoPaterno: 'MARTINEZ',
            apellidoMaterno: 'LOPEZ',
            calidad: 'ARRENDADOR',
            esChileno: true
          },
          {
            rut: '22222222-2',
            nombres: 'SOFIA ALEJANDRA',
            apellidoPaterno: 'TORRES',
            apellidoMaterno: 'RAMIREZ',
            calidad: 'ARRENDATARIO',
            esChileno: true
          }
        ]
      },
      {
        numeroOT: 'OT-2025-003',
        tipoDocumento: 'repertorio_vehiculo',
        materiaDetalle: 'Transferencia de vehículo marca Toyota, modelo Corolla, año 2020',
        estado: 'CREADA',
        prioridad: 'MEDIA',
        gestora: 'AUTOMOTORA CENTRAL S.A.',
        numeroWorkflow: 'WF-2025-003',
        observaciones: 'Verificar documentos del vehículo antes de proceder',
        comparecientes: [
          {
            rut: '33333333-3',
            nombres: 'MIGUEL ANGEL',
            apellidoPaterno: 'FERNANDEZ',
            apellidoMaterno: 'CASTRO',
            calidad: 'VENDEDOR',
            esChileno: true
          },
          {
            rut: '44444444-4',
            nombres: 'PATRICIA ELENA',
            apellidoPaterno: 'MORALES',
            apellidoMaterno: 'SILVA',
            calidad: 'COMPRADOR',
            esChileno: true
          }
        ]
      }
    ]

    for (const otData of sampleOTs) {
      const ot = await prisma.ordenTrabajo.create({
        data: {
          numeroOT: otData.numeroOT,
          tipoDocumento: otData.tipoDocumento,
          materiaDetalle: otData.materiaDetalle,
          estado: otData.estado,
          prioridad: otData.prioridad,
          gestora: otData.gestora,
          numeroWorkflow: otData.numeroWorkflow,
          observaciones: otData.observaciones,
          creadoPor: adminUser.id,
          comparecientes: {
            create: otData.comparecientes
          },
          historial: {
            create: {
              accion: 'CREADA',
              descripcion: `OT ${otData.numeroOT} creada en el sistema`,
              estadoNuevo: otData.estado,
              creadoPor: adminUser.id
            }
          }
        }
      })

      console.log(`✅ Created OT: ${ot.numeroOT}`)
    }

    // Create system configuration
    console.log('⚙️ Creating system configuration...')
    
    const configs = [
      { key: 'ot_numero_format', value: 'OT-{YEAR}-{SEQUENCE}', description: 'Formato de numeración de OT' },
      { key: 'ot_sequence_current', value: '3', description: 'Secuencia actual de OT' },
      { key: 'uaf_value_current', value: '37000', description: 'Valor actual de la UAF en pesos' },
      { key: 'rndpa_enabled', value: 'true', description: 'RNDPA habilitado' },
      { key: 'roe_threshold_uaf', value: '10000', description: 'Umbral ROE en UAF' },
      { key: 'system_name', value: 'e-Notaría', description: 'Nombre del sistema' },
      { key: 'system_version', value: '2.0.0', description: 'Versión del sistema' }
    ]

    for (const config of configs) {
      await prisma.systemConfig.upsert({
        where: { key: config.key },
        update: { value: config.value, description: config.description },
        create: {
          key: config.key,
          value: config.value,
          description: config.description
        }
      })
    }

    // Verify final state
    const stats = {
      ots: await prisma.ordenTrabajo.count(),
      comparecientes: await prisma.oTCompareciente.count(),
      historial: await prisma.oTHistorial.count(),
      configs: await prisma.systemConfig.count()
    }

    console.log('📊 Final Statistics:')
    console.log(`   📄 OTs: ${stats.ots}`)
    console.log(`   👥 Comparecientes: ${stats.comparecientes}`)
    console.log(`   📚 Historial entries: ${stats.historial}`)
    console.log(`   ⚙️ System configs: ${stats.configs}`)

    console.log('✅ OT data reset completed successfully!')

  } catch (error) {
    console.error('❌ Error resetting OT data:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
if (require.main === module) {
  resetOTData()
    .then(() => {
      console.log('🎉 Reset completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Reset failed:', error)
      process.exit(1)
    })
}

export { resetOTData }
