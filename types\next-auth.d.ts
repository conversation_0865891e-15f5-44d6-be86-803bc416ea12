import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      username: string
      email?: string | null
      nombreSocial?: string | null
      permissions: string[]
    }
  }

  interface User {
    id: string
    username: string
    email?: string | null
    nombreSocial?: string | null
    permissions: string[]
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    username: string
    nombreSocial?: string | null
    permissions: string[]
  }
}
