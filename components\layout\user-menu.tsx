"use client"

import { useState, useRef, useEffect } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { 
  UserIcon, 
  Cog6ToothIcon, 
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

export function UserMenu() {
  const { data: session } = useSession()
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  // Cerrar menú al hacer clic fuera
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleLogout = async () => {
    try {
      await signOut({ 
        callbackUrl: '/auth/login',
        redirect: true 
      })
    } catch (error) {
      console.error('Error al cerrar sesión:', error)
    }
  }

  if (!session?.user) {
    return null
  }

  return (
    <div className="relative" ref={menuRef}>
      {/* Botón del usuario */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md px-2 py-1"
      >
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
            <UserIcon className="w-4 h-4 text-primary-600" />
          </div>
          <span className="hidden sm:block font-medium">
            {session.user.nombreSocial || session.user.username}
          </span>
        </div>
        <ChevronDownIcon 
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {/* Menú desplegable */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* Información del usuario */}
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                <UserIcon className="w-5 h-5 text-primary-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {session.user.nombreSocial || session.user.username}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {session.user.email || 'Sin email'}
                </p>
                <p className="text-xs text-gray-400">
                  Usuario: {session.user.username}
                </p>
              </div>
            </div>
          </div>

          {/* Opciones del menú */}
          <div className="py-1">
            <a
              href="/perfil"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900"
            >
              <UserIcon className="w-4 h-4 mr-3 text-gray-400" />
              Mi Perfil
            </a>
            
            <a
              href="/configuracion"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900"
            >
              <Cog6ToothIcon className="w-4 h-4 mr-3 text-gray-400" />
              Configuración
            </a>
            
            <a
              href="/licencias"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900"
            >
              <InformationCircleIcon className="w-4 h-4 mr-3 text-gray-400" />
              Licencias
            </a>
          </div>

          {/* Información del sistema */}
          <div className="border-t border-gray-100 py-2">
            <div className="px-4 py-2">
              <p className="text-xs text-gray-500">e-NOTARIA v1.0.0</p>
              <p className="text-xs text-gray-400">
                Sesión iniciada: {new Date().toLocaleDateString('es-CL')}
              </p>
            </div>
          </div>

          {/* Cerrar sesión */}
          <div className="border-t border-gray-100 py-1">
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-900"
            >
              <ArrowRightOnRectangleIcon className="w-4 h-4 mr-3 text-red-500" />
              Cerrar Sesión
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

// Versión con estilos inline para compatibilidad
export function UserMenuInline() {
  const { data: session } = useSession()
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleLogout = async () => {
    try {
      await signOut({ 
        callbackUrl: '/auth/login',
        redirect: true 
      })
    } catch (error) {
      console.error('Error al cerrar sesión:', error)
    }
  }

  if (!session?.user) {
    return null
  }

  return (
    <div style={{ position: 'relative' }} ref={menuRef}>
      {/* Botón del usuario */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '14px',
          color: '#374151',
          background: 'none',
          border: 'none',
          cursor: 'pointer',
          padding: '4px 8px',
          borderRadius: '6px',
          transition: 'color 0.2s'
        }}
        onMouseEnter={(e) => e.target.style.color = '#111827'}
        onMouseLeave={(e) => e.target.style.color = '#374151'}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div style={{
            width: '32px',
            height: '32px',
            backgroundColor: '#dbeafe',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <span style={{ fontSize: '14px', color: '#2563eb' }}>👤</span>
          </div>
          <span style={{ fontWeight: '500' }}>
            {session.user.nombreSocial || session.user.username}
          </span>
        </div>
        <span style={{ 
          fontSize: '12px',
          transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.2s'
        }}>
          ▼
        </span>
      </button>

      {/* Menú desplegable */}
      {isOpen && (
        <div style={{
          position: 'absolute',
          right: 0,
          top: '100%',
          marginTop: '8px',
          width: '256px',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          border: '1px solid #e5e7eb',
          padding: '8px 0',
          zIndex: 50
        }}>
          {/* Información del usuario */}
          <div style={{
            padding: '12px 16px',
            borderBottom: '1px solid #f3f4f6'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{
                width: '40px',
                height: '40px',
                backgroundColor: '#dbeafe',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ fontSize: '16px', color: '#2563eb' }}>👤</span>
              </div>
              <div style={{ flex: 1, minWidth: 0 }}>
                <p style={{ 
                  fontSize: '14px', 
                  fontWeight: '500', 
                  color: '#111827',
                  margin: 0,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  {session.user.nombreSocial || session.user.username}
                </p>
                <p style={{ 
                  fontSize: '12px', 
                  color: '#6b7280',
                  margin: 0,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  {session.user.email || 'Sin email'}
                </p>
                <p style={{ 
                  fontSize: '11px', 
                  color: '#9ca3af',
                  margin: 0
                }}>
                  Usuario: {session.user.username}
                </p>
              </div>
            </div>
          </div>

          {/* Opciones del menú */}
          <div style={{ padding: '4px 0' }}>
            <a
              href="/perfil"
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 16px',
                fontSize: '14px',
                color: '#374151',
                textDecoration: 'none',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
            >
              <span style={{ marginRight: '12px' }}>👤</span>
              Mi Perfil
            </a>
            
            <a
              href="/configuracion"
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 16px',
                fontSize: '14px',
                color: '#374151',
                textDecoration: 'none',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
            >
              <span style={{ marginRight: '12px' }}>⚙️</span>
              Configuración
            </a>
            
            <a
              href="/licencias"
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 16px',
                fontSize: '14px',
                color: '#374151',
                textDecoration: 'none',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
            >
              <span style={{ marginRight: '12px' }}>🔐</span>
              Licencias
            </a>
          </div>

          {/* Información del sistema */}
          <div style={{ borderTop: '1px solid #f3f4f6', padding: '8px 0' }}>
            <div style={{ padding: '8px 16px' }}>
              <p style={{ fontSize: '11px', color: '#6b7280', margin: 0 }}>
                e-NOTARIA v1.0.0
              </p>
              <p style={{ fontSize: '11px', color: '#9ca3af', margin: 0 }}>
                Sesión: {new Date().toLocaleDateString('es-CL')}
              </p>
            </div>
          </div>

          {/* Cerrar sesión */}
          <div style={{ borderTop: '1px solid #f3f4f6', padding: '4px 0' }}>
            <button
              onClick={handleLogout}
              style={{
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                padding: '8px 16px',
                fontSize: '14px',
                color: '#dc2626',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#fef2f2'
                e.target.style.color = '#991b1b'
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent'
                e.target.style.color = '#dc2626'
              }}
            >
              <span style={{ marginRight: '12px' }}>🚪</span>
              Cerrar Sesión
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
