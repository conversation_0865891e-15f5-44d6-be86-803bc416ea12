"use client"

interface RepertorioTabProps {
  otId?: string
  numeroOT?: string
  tipoDocumento: string
  formState: any
  updateFormState: (updates: any) => void
}

export function RepertorioTab({ 
  otId, 
  numeroOT, 
  tipoDocumento, 
  formState, 
  updateFormState 
}: RepertorioTabProps) {
  
  if (!otId) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">📚</div>
        <p className="text-gray-600">Debe crear la OT primero para acceder al repertorio</p>
      </div>
    )
  }

  if (tipoDocumento === 'documento_privado' || tipoDocumento === 'instruccion') {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">🚫</div>
        <p className="text-gray-600">
          El repertorio no está disponible para {tipoDocumento.replace('_', ' ')}
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="bg-indigo-600 text-white p-4 rounded-t-lg">
        <h3 className="text-lg font-medium">
          Repertorio - OT: {numeroOT}
        </h3>
      </div>

      <div className="bg-indigo-50 p-6 rounded-b-lg border border-indigo-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Número de Repertorio
            </label>
            <input
              type="text"
              value={formState.numeroRepertorio || ''}
              onChange={(e) => updateFormState({ numeroRepertorio: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Ej: 2025-001"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Fecha de Repertorio
            </label>
            <input
              type="date"
              value={formState.fechaRepertorio || ''}
              onChange={(e) => updateFormState({ fechaRepertorio: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Foja de Repertorio
            </label>
            <input
              type="text"
              value={formState.fojaRepertorio || ''}
              onChange={(e) => updateFormState({ fojaRepertorio: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Ej: 123"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Folio de Protocolo
            </label>
            <input
              type="text"
              value={formState.folioProtocolo || ''}
              onChange={(e) => updateFormState({ folioProtocolo: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Ej: 456"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Fojas de Protocolo
            </label>
            <input
              type="number"
              value={formState.fojasProtocolo || ''}
              onChange={(e) => updateFormState({ fojasProtocolo: parseInt(e.target.value) || 0 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              min="1"
              placeholder="Número de fojas"
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <button
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Guardar Repertorio
          </button>
        </div>
      </div>
    </div>
  )
}
