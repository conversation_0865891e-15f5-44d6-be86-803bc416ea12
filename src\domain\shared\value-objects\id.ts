// Domain - Shared - Value Objects
export abstract class Id {
  protected readonly value: string

  constructor(value: string) {
    if (!value || value.trim().length === 0) {
      throw new Error('ID cannot be empty')
    }
    this.value = value
  }

  public getValue(): string {
    return this.value
  }

  public equals(other: Id): boolean {
    return this.value === other.value
  }

  public toString(): string {
    return this.value
  }
}

export class UserId extends Id {
  constructor(value: string) {
    super(value)
  }

  static generate(): UserId {
    return new UserId(crypto.randomUUID())
  }
}

export class LicenseId extends Id {
  constructor(value: string) {
    super(value)
  }

  static generate(): LicenseId {
    return new LicenseId(crypto.randomUUID())
  }
}

export class OTId extends Id {
  constructor(value: string) {
    super(value)
  }

  static generate(): OTId {
    return new OTId(crypto.randomUUID())
  }
}

export class DocumentId extends Id {
  constructor(value: string) {
    super(value)
  }

  static generate(): DocumentId {
    return new DocumentId(crypto.randomUUID())
  }
}
