# Script para configurar solo la base de datos con Docker
# Ejecutar como: .\setup-database.ps1

Write-Host "🐳 Configurando Base de Datos para E-Notaría..." -ForegroundColor Green

# Verificar que Docker esté instalado
Write-Host "📋 Verificando Docker..." -ForegroundColor Yellow

if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker no está instalado. Por favor instala Docker Desktop." -ForegroundColor Red
    exit 1
}

if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker Compose no está disponible." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Docker está disponible" -ForegroundColor Green

# Verificar que Docker esté ejecutándose
try {
    docker info | Out-Null
    Write-Host "✅ Docker está ejecutándose" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker no está ejecutándose. Por favor inicia Docker Desktop." -ForegroundColor Red
    exit 1
}

# Crear directorios necesarios
Write-Host "📁 Creando directorios..." -ForegroundColor Yellow

$directories = @(
    "database/init",
    "database/backups"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Directorio $dir creado" -ForegroundColor Green
    }
}

# Detener contenedores existentes si los hay
Write-Host "🛑 Deteniendo contenedores existentes..." -ForegroundColor Yellow
docker-compose down 2>$null

# Iniciar solo los servicios de base de datos
Write-Host "🚀 Iniciando servicios de base de datos..." -ForegroundColor Yellow

try {
    docker-compose up -d postgres redis pgadmin mailhog
    Write-Host "✅ Servicios iniciados correctamente" -ForegroundColor Green
} catch {
    Write-Host "❌ Error al iniciar servicios: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Esperar a que PostgreSQL esté listo
Write-Host "⏳ Esperando a que PostgreSQL esté listo..." -ForegroundColor Yellow

$maxAttempts = 30
$attempt = 0

do {
    $attempt++
    try {
        $result = docker exec e-notaria-postgres pg_isready -U notaria_user -d e_notaria_db 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL está listo" -ForegroundColor Green
            break
        }
    } catch {
        # Continuar intentando
    }
    
    if ($attempt -eq $maxAttempts) {
        Write-Host "❌ PostgreSQL no respondió después de $maxAttempts intentos" -ForegroundColor Red
        Write-Host "💡 Verifica los logs con: docker-compose logs postgres" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Host "⏳ Intento $attempt/$maxAttempts - Esperando PostgreSQL..." -ForegroundColor Yellow
    Start-Sleep -Seconds 2
} while ($true)

# Verificar Redis
Write-Host "🔴 Verificando Redis..." -ForegroundColor Yellow
try {
    docker exec e-notaria-redis redis-cli ping | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Redis está funcionando" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Redis puede no estar completamente listo" -ForegroundColor Yellow
}

# Mostrar estado de los servicios
Write-Host "`n📊 Estado de los servicios:" -ForegroundColor Cyan
docker-compose ps

Write-Host "`n🎉 Base de datos configurada correctamente!" -ForegroundColor Green

Write-Host "`n📋 Servicios disponibles:" -ForegroundColor Cyan
Write-Host "  🗄️  PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "     - Base de datos: e_notaria_db" -ForegroundColor Gray
Write-Host "     - Usuario: notaria_user" -ForegroundColor Gray
Write-Host "     - Contraseña: notaria_secure_2024" -ForegroundColor Gray
Write-Host ""
Write-Host "  🔴 Redis: localhost:6379" -ForegroundColor White
Write-Host "     - Contraseña: redis_notaria_2024" -ForegroundColor Gray
Write-Host ""
Write-Host "  🔧 pgAdmin: http://localhost:5050" -ForegroundColor White
Write-Host "     - Email: <EMAIL>" -ForegroundColor Gray
Write-Host "     - Contraseña: admin_notaria_2024" -ForegroundColor Gray
Write-Host ""
Write-Host "  📧 MailHog: http://localhost:8025" -ForegroundColor White

Write-Host "`n📝 Próximos pasos:" -ForegroundColor Cyan
Write-Host "  1. Navega al directorio del proyecto Next.js" -ForegroundColor White
Write-Host "  2. Ejecuta: npm install" -ForegroundColor White
Write-Host "  3. Ejecuta: npm run dev" -ForegroundColor White
Write-Host "  4. Abre: http://localhost:3000" -ForegroundColor White

Write-Host "`n🔗 Comandos útiles:" -ForegroundColor Cyan
Write-Host "  Ver logs: docker-compose logs -f" -ForegroundColor White
Write-Host "  Detener: docker-compose down" -ForegroundColor White
Write-Host "  Reiniciar: docker-compose restart" -ForegroundColor White

Write-Host "`n✨ ¡Listo para desarrollar!" -ForegroundColor Green
