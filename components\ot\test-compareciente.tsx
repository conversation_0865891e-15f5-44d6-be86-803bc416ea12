"use client"

import { useState } from "react"

interface TestComparecienteProps {
  onAdd: (comp: any) => void
  comparecientes: any[]
  onRemove: (id: string) => void
}

export function TestCompareciente({ onAdd, comparecientes, onRemove }: TestComparecienteProps) {
  const [showForm, setShowForm] = useState(false)

  const handleAddTest = () => {
    const testCompareciente = {
      id: Date.now().toString(),
      rut: '12345678-9',
      nombres: 'JUAN',
      apellidoPaterno: 'PEREZ',
      apellidoMaterno: 'GONZALEZ',
      calidad: 'COMPRADOR',
      esChileno: true
    }
    
    console.log('Adding test compareciente:', testCompareciente)
    onAdd(testCompareciente)
    setShowForm(false)
  }

  return (
    <div className="space-y-4">
      <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
        <h4 className="font-medium text-yellow-800 mb-2">
          🧪 Test Component - Comparecientes: {comparecientes.length}
        </h4>
        
        {!showForm && (
          <button
            onClick={() => setShowForm(true)}
            className="bg-blue-600 text-white px-3 py-1 rounded text-sm"
          >
            Agregar Test Compareciente
          </button>
        )}

        {showForm && (
          <div className="mt-3 space-y-2">
            <p className="text-sm text-gray-600">
              Se agregará: JUAN PEREZ GONZALEZ (12345678-9) - COMPRADOR
            </p>
            <div className="space-x-2">
              <button
                onClick={handleAddTest}
                className="bg-green-600 text-white px-3 py-1 rounded text-sm"
              >
                Confirmar
              </button>
              <button
                onClick={() => setShowForm(false)}
                className="bg-gray-600 text-white px-3 py-1 rounded text-sm"
              >
                Cancelar
              </button>
            </div>
          </div>
        )}

        {comparecientes.length > 0 && (
          <div className="mt-4">
            <h5 className="font-medium text-gray-700 mb-2">Lista actual:</h5>
            {comparecientes.map((comp, index) => (
              <div key={comp.id || index} className="flex justify-between items-center bg-white p-2 rounded border mb-1">
                <span className="text-sm">
                  {comp.nombres} {comp.apellidoPaterno} {comp.apellidoMaterno} ({comp.rut})
                </span>
                <button
                  onClick={() => onRemove(comp.id)}
                  className="text-red-600 text-sm hover:text-red-800"
                >
                  Eliminar
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
