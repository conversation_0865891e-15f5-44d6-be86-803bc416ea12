"use client"

import { useSession } from "next-auth/react"
import { useMemo } from "react"

interface Permission {
  name: string
  module?: string
  description?: string
}

interface Role {
  id: string
  name: string
  description?: string
}

export function usePermissions() {
  const { data: session, status } = useSession()

  const permissions = useMemo(() => {
    if (!session?.user?.permissions) return []
    return session.user.permissions as Permission[]
  }, [session?.user?.permissions])

  const roles = useMemo(() => {
    if (!session?.user?.roles) return []
    return session.user.roles as Role[]
  }, [session?.user?.roles])

  // Verificar si el usuario tiene un permiso específico
  const hasPermission = (permissionName: string): boolean => {
    return permissions.some(p => p.name === permissionName)
  }

  // Verificar si el usuario tiene permisos en un módulo específico
  const hasModuleAccess = (moduleName: string): boolean => {
    return permissions.some(p => p.module === moduleName)
  }

  // Verificar si el usuario tiene un rol específico
  const hasRole = (roleName: string): boolean => {
    return roles.some(r => r.name === roleName)
  }

  // Verificar múltiples permisos (AND)
  const hasAllPermissions = (permissionNames: string[]): boolean => {
    return permissionNames.every(name => hasPermission(name))
  }

  // Verificar al menos uno de varios permisos (OR)
  const hasAnyPermission = (permissionNames: string[]): boolean => {
    return permissionNames.some(name => hasPermission(name))
  }

  // Obtener permisos por módulo
  const getModulePermissions = (moduleName: string): Permission[] => {
    return permissions.filter(p => p.module === moduleName)
  }

  // Verificar si es administrador
  const isAdmin = (): boolean => {
    return hasRole('admin') || hasPermission('admin_access')
  }

  // Verificar permisos CRUD específicos
  const canCreate = (module: string): boolean => {
    return hasPermission(`${module}_create`) || isAdmin()
  }

  const canRead = (module: string): boolean => {
    return hasPermission(`${module}_read`) || hasPermission(`${module}_access`) || isAdmin()
  }

  const canUpdate = (module: string): boolean => {
    return hasPermission(`${module}_update`) || hasPermission(`${module}_edit`) || isAdmin()
  }

  const canDelete = (module: string): boolean => {
    return hasPermission(`${module}_delete`) || isAdmin()
  }

  return {
    // Estado
    isLoading: status === "loading",
    isAuthenticated: !!session,
    user: session?.user,
    permissions,
    roles,

    // Métodos de verificación
    hasPermission,
    hasModuleAccess,
    hasRole,
    hasAllPermissions,
    hasAnyPermission,
    getModulePermissions,
    isAdmin,

    // Métodos CRUD
    canCreate,
    canRead,
    canUpdate,
    canDelete,

    // Métodos de conveniencia para módulos específicos
    canAccessOT: () => canRead('ot'),
    canCreateOT: () => canCreate('ot'),
    canEditOT: () => canUpdate('ot'),
    canDeleteOT: () => canDelete('ot'),

    canAccessDocuments: () => canRead('documentos'),
    canCreateDocuments: () => canCreate('documentos'),
    canEditDocuments: () => canUpdate('documentos'),

    canAccessUsers: () => canRead('usuarios'),
    canManageUsers: () => hasPermission('user_management') || isAdmin(),

    canAccessReports: () => hasPermission('reportes_access') || isAdmin(),
    canAccessCaja: () => hasPermission('caja_access') || isAdmin(),
    canAccessRepertorio: () => hasPermission('repertorio_access') || isAdmin(),
  }
}

// Hook para componentes que requieren permisos específicos
export function useRequirePermission(permission: string | string[]) {
  const { hasPermission, hasAnyPermission, isLoading } = usePermissions()

  const hasRequiredPermission = useMemo(() => {
    if (isLoading) return false
    
    if (Array.isArray(permission)) {
      return hasAnyPermission(permission)
    }
    
    return hasPermission(permission)
  }, [permission, hasPermission, hasAnyPermission, isLoading])

  return {
    hasPermission: hasRequiredPermission,
    isLoading
  }
}

// Hook para componentes que requieren roles específicos
export function useRequireRole(role: string | string[]) {
  const { hasRole, roles, isLoading } = usePermissions()

  const hasRequiredRole = useMemo(() => {
    if (isLoading) return false
    
    if (Array.isArray(role)) {
      return role.some(r => hasRole(r))
    }
    
    return hasRole(role)
  }, [role, hasRole, isLoading])

  return {
    hasRole: hasRequiredRole,
    roles,
    isLoading
  }
}
