import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { prisma } from "./prisma"
import bcrypt from "bcryptjs"
import { rateLimit } from "./rate-limit"
import { auditLog } from "./audit-log"

// Rate limiting para intentos de login
const loginLimiter = rateLimit({
  interval: 15 * 60 * 1000, // 15 minutos
  uniqueTokenPerInterval: 500, // Límite por IP
})

export const authOptionsEnhanced: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        username: { label: "<PERSON><PERSON><PERSON>", type: "text" },
        password: { label: "Contraseña", type: "password" }
      },
      async authorize(credentials, req) {
        if (!credentials?.username || !credentials?.password) {
          await auditLog({
            action: 'LOGIN_ATTEMPT_FAILED',
            details: 'Missing credentials',
            ip: req?.headers?.['x-forwarded-for'] as string || 'unknown'
          })
          return null
        }

        const clientIP = req?.headers?.['x-forwarded-for'] as string || 'unknown'

        // Rate limiting por IP
        try {
          await loginLimiter.check(clientIP, 5) // 5 intentos por IP cada 15 min
        } catch {
          await auditLog({
            action: 'LOGIN_RATE_LIMITED',
            details: `IP: ${clientIP}`,
            ip: clientIP
          })
          throw new Error('Demasiados intentos de login. Intente más tarde.')
        }

        // Buscar usuario con información completa
        const user = await prisma.user.findUnique({
          where: {
            username: credentials.username.toUpperCase()
          },
          include: {
            userRoles: {
              include: {
                role: {
                  include: {
                    rolePermissions: {
                      include: {
                        permission: true
                      }
                    }
                  }
                }
              }
            },
            sessions: {
              where: {
                expires: {
                  gt: new Date()
                }
              }
            }
          }
        })

        if (!user || !user.estado) {
          await auditLog({
            action: 'LOGIN_ATTEMPT_FAILED',
            details: `User not found or inactive: ${credentials.username}`,
            ip: clientIP
          })
          return null
        }

        // Verificar contraseña con bcrypt (seguro)
        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          await auditLog({
            action: 'LOGIN_ATTEMPT_FAILED',
            details: `Invalid password for user: ${user.username}`,
            ip: clientIP,
            userId: user.id
          })
          return null
        }

        // Verificar sesiones concurrentes (mejorado del legacy)
        const activeSessions = user.sessions.length
        const maxConcurrentSessions = 3 // Configurable

        if (activeSessions >= maxConcurrentSessions) {
          // Opcional: Cerrar sesión más antigua automáticamente
          const oldestSession = await prisma.session.findFirst({
            where: { userId: user.id },
            orderBy: { expires: 'asc' }
          })
          
          if (oldestSession) {
            await prisma.session.delete({
              where: { id: oldestSession.id }
            })
          }
        }

        // Actualizar último login y limpiar intentos fallidos
        await prisma.user.update({
          where: { id: user.id },
          data: { 
            lastLogin: new Date(),
            // Resetear contador de intentos fallidos si existe
          }
        })

        // Preparar permisos granulares
        const permissions = user.userRoles.flatMap(userRole =>
          userRole.role.rolePermissions.map(rp => ({
            name: rp.permission.name,
            module: rp.permission.module,
            description: rp.permission.description
          }))
        )

        const roles = user.userRoles.map(ur => ({
          id: ur.role.id,
          name: ur.role.name,
          description: ur.role.description
        }))

        await auditLog({
          action: 'LOGIN_SUCCESS',
          details: `User logged in successfully`,
          ip: clientIP,
          userId: user.id
        })

        return {
          id: user.id,
          username: user.username,
          email: user.email,
          nombreSocial: user.nombreSocial,
          permissions,
          roles,
          lastLogin: user.lastLogin
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 horas
    updateAge: 24 * 60 * 60, // Actualizar cada 24 horas
  },
  callbacks: {
    async jwt({ token, user, trigger }) {
      if (user) {
        token.username = user.username
        token.nombreSocial = user.nombreSocial
        token.permissions = user.permissions
        token.roles = user.roles
        token.lastLogin = user.lastLogin
      }

      // Revalidar permisos periódicamente
      if (trigger === "update" && token.sub) {
        const freshUser = await prisma.user.findUnique({
          where: { id: token.sub },
          include: {
            userRoles: {
              include: {
                role: {
                  include: {
                    rolePermissions: {
                      include: {
                        permission: true
                      }
                    }
                  }
                }
              }
            }
          }
        })

        if (freshUser && freshUser.estado) {
          const freshPermissions = freshUser.userRoles.flatMap(userRole =>
            userRole.role.rolePermissions.map(rp => ({
              name: rp.permission.name,
              module: rp.permission.module,
              description: rp.permission.description
            }))
          )
          token.permissions = freshPermissions
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.username = token.username as string
        session.user.nombreSocial = token.nombreSocial as string
        session.user.permissions = token.permissions as any[]
        session.user.roles = token.roles as any[]
        session.user.lastLogin = token.lastLogin as Date
      }
      return session
    },
    async signIn({ user, account, profile }) {
      // Verificaciones adicionales en el sign-in
      if (account?.provider === "credentials") {
        // Verificar si el usuario está activo
        const dbUser = await prisma.user.findUnique({
          where: { id: user.id }
        })
        
        return dbUser?.estado === true
      }
      return true
    }
  },
  events: {
    async signOut({ token }) {
      if (token?.sub) {
        await auditLog({
          action: 'LOGOUT',
          details: 'User logged out',
          userId: token.sub
        })
      }
    }
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error"
  },
  secret: process.env.NEXTAUTH_SECRET,
}
