// Configuración centralizada del sistema e-NOTARIA

export const APP_CONFIG = {
  // Información de la aplicación
  name: "e-NOTARIA",
  fullName: "e-NOTARIA - Sistema Informático de Gestión Notarial",
  version: "1.0.0",
  description: "Sistema de Gestión Notarial",
  
  // Información de la empresa (configurable)
  company: {
    name: process.env.NEXT_PUBLIC_COMPANY_NAME || "Notaría Ejemplo",
    fullName: process.env.NEXT_PUBLIC_COMPANY_FULL_NAME || "Notaría Ejemplo S.A.",
    address: process.env.NEXT_PUBLIC_COMPANY_ADDRESS || "",
    phone: process.env.NEXT_PUBLIC_COMPANY_PHONE || "",
    email: process.env.NEXT_PUBLIC_COMPANY_EMAIL || "",
    website: process.env.NEXT_PUBLIC_COMPANY_WEBSITE || "",
  },

  // URLs y rutas
  urls: {
    login: "/auth/login",
    dashboard: "/dashboard",
    home: "/",
  },

  // Configuración de sesión
  session: {
    maxAge: 8 * 60 * 60, // 8 horas
    maxConcurrentSessions: 3,
    inactivityTimeout: 30 * 60, // 30 minutos
  },

  // Configuración de seguridad
  security: {
    passwordMinLength: 8,
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60, // 15 minutos
  },
} as const

// Tema de colores optimizado para protección visual
export const THEME_CONFIG = {
  colors: {
    // Colores primarios - Azul suave para reducir fatiga visual
    primary: {
      50: '#f0f9ff',   // Muy claro
      100: '#e0f2fe',  // Claro
      200: '#bae6fd',  // Medio claro
      300: '#7dd3fc',  // Medio
      400: '#38bdf8',  // Medio oscuro
      500: '#0ea5e9',  // Principal (más suave que el legacy #42a4d7)
      600: '#0284c7',  // Oscuro
      700: '#0369a1',  // Muy oscuro
      800: '#075985',  // Extra oscuro
      900: '#0c4a6e',  // Máximo oscuro
    },

    // Colores secundarios - Verde suave para elementos de éxito
    secondary: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',  // Verde principal
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
    },

    // Grises cálidos para reducir contraste duro
    gray: {
      50: '#fafaf9',   // Fondo muy claro con tinte cálido
      100: '#f5f5f4',  // Fondo claro
      200: '#e7e5e4',  // Bordes suaves
      300: '#d6d3d1',  // Bordes
      400: '#a8a29e',  // Texto secundario
      500: '#78716c',  // Texto normal
      600: '#57534e',  // Texto principal
      700: '#44403c',  // Texto oscuro
      800: '#292524',  // Texto muy oscuro
      900: '#1c1917',  // Texto máximo
    },

    // Colores de estado con menor saturación
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',

    // Fondos especiales para reducir fatiga
    background: {
      primary: '#fafaf9',    // Fondo principal cálido
      secondary: '#f5f5f4',  // Fondo secundario
      card: '#ffffff',       // Fondo de tarjetas
      sidebar: '#f8fafc',    // Fondo sidebar suave
      header: '#ffffff',     // Fondo header
    },

    // Bordes suaves
    border: {
      light: '#e7e5e4',
      normal: '#d6d3d1',
      dark: '#a8a29e',
    },

    // Texto optimizado para lectura prolongada
    text: {
      primary: '#374151',    // Texto principal (menos contraste que negro puro)
      secondary: '#6b7280',  // Texto secundario
      muted: '#9ca3af',      // Texto deshabilitado
      inverse: '#ffffff',    // Texto sobre fondos oscuros
    },
  },

  // Sombras suaves
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },

  // Espaciado consistente
  spacing: {
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '1rem',      // 16px
    lg: '1.5rem',    // 24px
    xl: '2rem',      // 32px
    '2xl': '3rem',   // 48px
  },

  // Tipografía optimizada para lectura
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
    },
  },

  // Configuración de layout
  layout: {
    sidebar: {
      width: '16rem',        // 256px
      collapsedWidth: '4rem', // 64px
    },
    header: {
      height: '4rem',        // 64px
    },
    content: {
      maxWidth: '1280px',
      padding: '1.5rem',
    },
  },
} as const

// Utilidades para acceso fácil a la configuración
export const getCompanyName = () => APP_CONFIG.company.name
export const getAppName = () => APP_CONFIG.name
export const getAppFullName = () => APP_CONFIG.fullName
export const getAppVersion = () => APP_CONFIG.version

// Función para obtener colores del tema
export const getThemeColor = (path: string) => {
  const keys = path.split('.')
  let value: any = THEME_CONFIG.colors
  
  for (const key of keys) {
    value = value?.[key]
  }
  
  return value || '#000000'
}

// Clases CSS predefinidas para uso común
export const CSS_CLASSES = {
  // Botones
  button: {
    primary: 'bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors duration-200',
    danger: 'bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200',
  },
  
  // Cards
  card: 'bg-white rounded-lg shadow-md border border-gray-200',
  
  // Inputs
  input: 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500',
  
  // Layout
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  
  // Texto
  heading: 'text-gray-700 font-semibold',
  subheading: 'text-gray-600 font-medium',
  body: 'text-gray-600',
  muted: 'text-gray-500',
} as const
