// Presentation - Server Actions - User Actions
'use server'

import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { container } from "../../infrastructure/config/dependency-injection"
import { revalidatePath } from "next/cache"

export async function getUserProfile() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      throw new Error('Not authenticated')
    }

    const useCase = container.getGetUserProfileUseCase()
    const result = await useCase.execute({ userId: session.user.id })

    if (!result.success) {
      throw new Error(result.error || 'Failed to get user profile')
    }

    return {
      success: true,
      user: {
        id: result.user!.getId().getValue(),
        username: result.user!.getUsername(),
        email: result.user!.getEmail().getValue(),
        nombreSocial: result.user!.getNombreSocial(),
        role: result.user!.getRole(),
        status: result.user!.getStatus(),
        permissions: result.user!.getPermissions(),
        lastLoginAt: result.user!.getLastLoginAt()
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function updateUserProfile(formData: FormData) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      throw new Error('Not authenticated')
    }

    const nombreSocial = formData.get('nombreSocial') as string
    const email = formData.get('email') as string

    if (!nombreSocial || !email) {
      throw new Error('Missing required fields')
    }

    // TODO: Implement update user profile use case
    // const useCase = container.getUpdateUserProfileUseCase()
    // const result = await useCase.execute({
    //   userId: session.user.id,
    //   nombreSocial,
    //   email
    // })

    revalidatePath('/dashboard')
    revalidatePath('/usuarios')

    return {
      success: true,
      message: 'Profile updated successfully'
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
