// Application - License - Use Cases
import { LicenseRepository } from '../../../domain/license/repositories/license-repository'
import { LicenseEncryptionService } from '../../../domain/license/services/license-encryption-service'
import { License } from '../../../domain/license/entities/license'

export interface ValidateLicenseRequest {
  encryptedLicense: string
  systemInfo: {
    installationId: string
    hardwareFingerprint: string
    version: string
    platform: string
  }
}

export interface ValidateLicenseResponse {
  success: boolean
  license?: License
  error?: string
}

export class ValidateLicenseUseCase {
  constructor(
    private licenseRepository: LicenseRepository,
    private encryptionService: LicenseEncryptionService
  ) {}

  async execute(request: ValidateLicenseRequest): Promise<ValidateLicenseResponse> {
    try {
      // Decrypt license data
      const licenseData = this.encryptionService.decrypt(request.encryptedLicense) as any

      // Find license by key
      const license = await this.licenseRepository.findByLicenseKey(licenseData.licenseKey)
      if (!license) {
        return {
          success: false,
          error: 'Invalid license'
        }
      }

      // Validate system info
      if (!license.isValidForSystem(request.systemInfo)) {
        return {
          success: false,
          error: 'License not valid for this system'
        }
      }

      // Check if license is active
      if (!license.isActive()) {
        return {
          success: false,
          error: 'License is not active or has expired'
        }
      }

      // Update validation timestamp
      license.validate()
      await this.licenseRepository.update(license)

      return {
        success: true,
        license
      }
    } catch (error) {
      return {
        success: false,
        error: 'Failed to validate license'
      }
    }
  }
}
