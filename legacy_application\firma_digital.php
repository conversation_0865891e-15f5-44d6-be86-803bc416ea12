<?php
ini_set("memory_limit","2024M");
error_reporting(E_ALL);
session_start();
define('TINYAJAX_PATH', 'calls');
require_once(TINYAJAX_PATH . "/TinyAjax.php"); 
require_once("calls/lib.php");
require_once("calls/lib_firma.php");

require_once('calls/soap/nusoap.php');

$usur1= new usuario;



function limpiar($source, $dest)
	{
    if (is_file($source))
	     {
			unlink($source);
	       }
    $dir = dir($source);
    while (false !== $entry = $dir->read())
    	 {
         if ($entry == '.' || $entry == '..')
           {continue;}
         if ($dest !== "$source/$entry")
           {unlink("$source/$entry");}
	    }


}  

function cuerpo()
	{
$conect= new ingreso;
$link=$conect->Conectarse();
$usur1= new usuario;
$dis = new diseno;
$caratula = $dis->protege($_REQUEST['caratula']);		
if($caratula<>"")
	{
	$card= new cardex;
	$caratula = intval($caratula);
	$caratula=$card->comprobar_numero($caratula);
	$conect= new ingreso;
	$link=$conect->Conectarse();
	 $ff =  new fechas;
	//if(($usur1->verificar_permiso($_SESSION['usuario'],23)) || ($usur1->verificar_permiso($_SESSION['usuario'],33) ) )
        if(1==1)
		{
		$i2_c = 0;
		$r2 = "SELECT fecha, id, tipo, caratula, numero_firma, libro, folio_real, foja, numero, anho, hora, usuario, mail_destinatario FROM certificados_firmas WHERE listo='nop' AND caratula='$caratula'  ";
		$fin=mysqli_query($link, $r2);
		while($row = mysqli_fetch_array($fin))
		{
		if($i2_c==0)
			{
			$dv4.="
		
			<tr>
				<td class=''  ></td>
				<td class=''  >Documento</td>
				<td class=''  >N� Doc</td>
				<td class=''  >OT</td>
				<td class=''  >Fecha</td>
			</tr>";
			}
 
		$r2_up2 = "UPDATE certificados_firmas SET listo='yep' WHERE listo='nop' AND caratula='$caratula' AND id='".$row["id"]."' ";
		$fin22=mysqli_query($link, $r2_up2);


		$i2_c+=1;

		$mms="";
		$dv4.="<tr>
			<td class='td1' align='center'>".$i2_c.")</td>
			<td class='td1'><a onclick=\"return hs.htmlExpand(this, { contentId: 'highslide-html', objectType: 'iframe',	objectWidth: 640, objectHeight: 580} )\" class=\"highslide\" href='calls/ver_pdf.php?iadsfd=".$row["numero_firma"]."&pdf=PDF&quieto=yep' >".$row["tipo"]."</td>
			<td class='td1' align='center'>".$row["numero_firma"]."</td>
			<td class='td1' align='center'>".$row["caratula"]."</td>
			<td class='td1' align='center'>".$ff->mysql_a_fecha($row["fecha"])."</td>
			<td class='td1' align='center'><div id='div_back_".$row["id"]."'><a href='#' onClick='devolver_doc_auto(\"".$row["id"]."\")' title='Devolver a Documento por Autorizar'><img src ='imagenes/expediente_devolver.png' width='20'></a></div></td>
			<tr>";			

			$r_e = "SELECT * FROM certificados_metadata WHERE id_certificado='".$row["numero_firma"]."'  ";
			$fin_e=mysqli_query($link, $r_e);			
			$row_e = mysqli_fetch_array($fin_e);
			if($row_e["id_certificado"]<>"")
				{	
			if($row_e["repertorio_notaria"]	<>"")
				{$mms= " Repertorio:".$row_e["repertorio_notaria"]."-".$row_e["campo4_nombre"]."    - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
			elseif($row_e["campo7_valor"]<>"")
				{$mms="Patente:".$row_e["campo7_valor"]." - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
			elseif($row_e["campo6_nombre"]	<>"")
				{$mms="Banco:".$row_e["campo6_nombre"]." N&ordm; Cuenta".$row_e["campo6_valor"]."  - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
			elseif($row_e["compareciente_1"]<>"" || $row_e["compareciente_2"]<>"" )
				{$mms=$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
				}
		if($mms<>"")
			{
		$dv4.="<tr>
			<td class='td1' colspan='4'>".$mms."</td>
			<tr>";			
			
			}
		}
		if($dv4 <> "")
			{
			$MSG_POST = "<br><div id='texto_carat'> 
			<table class='table' width='60%' align='center'>
			<tr><td colspan='5' class='text_positive'>Se Autorizaron $i2_c Documentos de Car�tula/OT : $caratula</td>".$dv4."</table><br></div>";
			}
		if($dv4 == "")
			{
			$MSG_POST = "<br><div class='text_negative' align='center'> NO EXISTE DOCUMENTOS CON OT ".$caratula."	</div>";
			}
		}
	}



/*if(($usur1->verificar_permiso($_SESSION['usuario'],4)) || ($usur1->verificar_permiso($_SESSION['usuario'],33) ) )	
		{*/
		$head0.="
		<table class='table' align='center' border='1'>
			<td class='' align='center'>Car&aacute;tula / OT: 
			<input name='caratula' id='caratula' size='15' type='text' onKeyup='limpiar_caratula();' onChange='limpiar_caratula();'>
			<input type='Submit' class='btn btn-info' value='Cargar' ></td>
			</tr>
			</tbody></table>
".$MSG_POST."				
			</form>
			<div align='center'><input type='button' class='btn btn-info' onclick='confirmar3();' value='AUTORIZAR FIRMA SELECCIONADOS'></div>";
//		}
$head="	
		<tr>
			<th scope='col' align='center'  >#</td>
			<th scope='col' align='center' ><a href='firma_digital.php?order=carat'>OT</td>
			<th scope='col' align='center' >Documento</td>
			<th scope='col' align='center' >N&ordm; Doc</td>
			<th scope='col' align='center' >Hora</td>
			<th scope='col' align='center' >Usuario</td>
			<th scope='col' align='center' colspan='3' >Acci&oacute;n</td>
			<th scope='col' align='center' >N&ordm;</td>
		</tr>
		<tr>";
$fecha=date("Y-m-d");
$order=$_REQUEST['order'];
if($order ==  "carat" )
	{$r2 = "SELECT fecha, id, tipo, caratula, numero_firma, libro, folio_real, foja, numero, anho, hora, usuario, mail_destinatario, id_pago, codigo FROM certificados_firmas WHERE listo='nop' ORDER BY fecha DESC, caratula, numero_firma ASC";}
else
	{$r2 = "SELECT fecha, id, tipo, caratula, numero_firma, libro, folio_real, foja, numero, anho, hora, usuario, mail_destinatario, id_pago, codigo FROM certificados_firmas WHERE listo='nop' ORDER BY fecha DESC, numero_firma, caratula ASC";}

$fin=mysqli_query($link, $r2);
$ff = new fechas;
$fecha_ant="";
while($row = mysqli_fetch_array($fin))
	{
	$i1+=1;
	$mms="";
	if($row["fecha"]<>$fecha_ant)
		{
		if($fecha_ant<>""){$dv.="</table>";}
		$dv.="<br>
		<table class='table'  width='90%' align='center'>
		<tr>
			<td class='' style='background-color:#ACE3F9 !important;' colspan='10' style='' ><h4 align='center'>Certificados : ".$ff->mysql_a_fecha($row["fecha"])."</h2></tr>".$head;		
		$fecha_ant=$row["fecha"];
		}
	$tipo= mb_convert_case ( str_replace("_"," ", $row["tipo"]) , MB_CASE_TITLE, "UTF-8");
		$dv.="<tr>";
		
		$id_pago = $row["id_pago"];
		
		//$link_videollamada = $row["link_videollamada"];
		
		//echo $id_pago;
		$valida_pago = "NOP";
		$texto_pagado = "";
		
		if ($id_pago > 0)
		{
			$valida_pago = validar_pago_online($id_pago);
			//marcar como yep!!! si es que esta pagado
			
			
			
		}
		else
		{
			$valida_pago = "YEP";
		}
		
		
		if ($valida_pago == "YEP")
		{
			if ($id_pago > 0)
			{
				$texto_pagado = "<br><div class='col-md-3 text-center'><img src='imagenes/video.png' style='width:30px; height:30px;'></div><br><br><blink>Pago Verificado</blink>";
			}
				
			$dv.="<td><input type='checkbox' name='".$row['id']."' value='".$row['id']."' id='".$row['id']."' onchange='asignar_opcion(this.id,this.checked,document.getElementById(\"checkeados\").value);'>".$texto_pagado."</td>";
		}
		else
		{
			//$dv.="<td><div class='col-md-3 text-center'><img src='imagenes/video.png' style='width:30px; height:30px;'></div><br>Pendiente de pago</td>";
		
		}
		
		//echo " codigo : ".$row["codigo"] ;
		
		if ($row["codigo"] <> "")
		{
			
			
			
				$id_solicitud = str_replace("nop","",$row["codigo"]);
				$id_solicitud = str_replace("yep","",$id_solicitud);
			
		
			$OT = "";	
			$r22 = "SELECT * FROM cardex_solicitudes WHERE id = '".$id_solicitud."'";			
			$fin22=mysqli_query($link, $r22) or die (mysql_error());
			$row22 = mysqli_fetch_array($fin22);	
			$OT	= $row22["OT"];
			
			if ($OT == "")
			{
				$r22 = "SELECT * FROM cardex_solicitudes_local WHERE id = '".$id_solicitud."'";	
				
						
				$fin22=mysqli_query($link, $r22) or die (mysql_error());
				$row22 = mysqli_fetch_array($fin22);	
				$OT	= $row22["OT"];
		
			}
			
			
		}
		
				
		
		
		$dv.="<td class=''>".$OT."</a></td>
		<td class=''><a onclick=\"return hs.htmlExpand(this, { contentId: 'highslide-html', objectType: 'iframe',	objectWidth: 640, objectHeight: 580} )\" class=\"highslide\" href='calls/ver_pdf.php?iadsfd=".$row["numero_firma"]."&pdf=PDF&quieto=yep' >".$tipo."</td>
		<td class='' align='center'>".$row["numero_firma"]."</td>
			<td class='' align='center'>".$row["hora"]."</td>
			<td class='' align='center'>".$row["usuario"]."</td>
			<td class='' align='center'><a href='#' onclick='confirmar(".$row["id"].");'><img src='imagenes/vig_s.jpg'></a></td>
			<td width='3'></td>
			<td class='' align='center'><a href='#' onclick='eliminar(".$row["id"].");'><img src='imagenes/vig_n.jpg'></a></td>
			<td class='' align='center'>".$i1."</a></td>
		<tr>";
	$mms="";
	$r_e = "SELECT * FROM certificados_metadata WHERE id_certificado='".$row["numero_firma"]."'  ";
	$fin_e=mysqli_query($link, $r_e);			
	$row_e = mysqli_fetch_array($fin_e);
	if($row_e["id_certificado"]<>"")
		{	
		if($tipo=="Extracto" )
			{$mms=$row_e["nombre_soc"]." - Capital:".$row_e["capital"]." - Repertorio:".$row_e["repertorio_notaria"];}
		else
			{
			if($row_e["repertorio_notaria"]	<>"")
				{$mms=$row_e["tipo"]." ".$row["libro"]."- Repertorio:".$row_e["repertorio_notaria"]."-".$row_e["campo4_nombre"]."    - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
			elseif($row_e["campo7_valor"]<>"")
				{$mms="Patente:".$row_e["campo7_valor"]." - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
			elseif($row_e["campo6_nombre"]	<>"")
				{$mms="Banco:".$row_e["campo6_nombre"]." N� Cuenta".$row_e["campo6_valor"]."  - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
			elseif($row_e["compareciente_1"]<>"" || $row_e["compareciente_2"]<>"" )
				{$mms=$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
			}
		}
		
		
			$r2_sel = "SELECT * FROM `cert_firma_matricera` WHERE `numero_firma` =  '".$row["numero_firma"]."' ";
			$fin_sel = mysqli_query($link, $r2_sel);
			$row_sel = mysqli_fetch_array($fin_sel);		
			if($row_sel["id"]<>""){$mms.= " Area: ".$row_sel["matricera"];}

		
	if($mms<>"")
	   {$dv.="<tr><td class='td1' colspan='10'  >".$mms."</td></tr>";}
	if($row["mail_destinatario"]<>"" && $row["mail_destinatario"]<>"-")
		{$dv.="<tr><td class='td1' colspan='10' >".$row["mail_destinatario"]."<td></tr>";}




	else {$mail="";}
	$dv.="
	<tr height='8'><td colspan='10' >
	<div id='id_".$row["id"]."'></div>
	</td></tr>	<tr height='8'><td colspan='10' >";
	}


if($dv=="")
	{
	$dv.="<table>";
	}
$dv.="</table>
	<br><div align='center'><input type='button' class='btn btn-info' value='AUTORIZAR RESTANTES' Onclick='resto();'></div>";
return $head0.$dv;
 }
 

 

function cuerpo_firma()
	{
$conect= new ingreso;
$link=$conect->Conectarse();
$dv.="
		
		<br>
		<table class='table' align='center' border='0'>
		<td class=''>Car&aacute;tula/OT:<input name='caratula_firma' id='caratula_firma' size='10' type='text'>
			<input type='button' class='btn btn-info' value='Firmar solo una Car&aacute;tula/OT' 
			OnClick='postergar_por_caratula();'>
			<input type='button' class='btn btn-info' value='Agregar a Firmar por Car&aacute;tula/OT' 
			OnClick='postergar_por_caratula_agregar();'>
		</td>
		</tr>
		</tbody></table>		
		</form><br>
		
		
		

		<table class='table'  align='center'>
		<td  class='' >Fecha Emisi&oacute;n:
		d&iacute;a:<input type='text' id='dia_x' name='dia_x'  SIZE='2'  MAXLENGTH = '2'    value=''>
		mes:<input type='text' id='mes_x'  name='mes_x'  SIZE='2' MAXLENGTH = '2'   value='".date("m")."'>
		a&ntilde;o:<input type='text' id='anho_x' name='anho_x'  SIZE='4' MAXLENGTH = '4'   value='".date("Y")."'>
		<input type='button' class='btn btn-info' value='Firmar solo por Fecha de Emisi&oacute;n' onClick='postergar_todo_fecha();'></td></table>
		<br>
		
		
		<table class='table'  align='center'>
		<td width='10'>
		<td> <button class='btn btn-info href='#' onCLick='postergar_por_escrituras_publicas()'>Firmar Solo Escrituras Publicas</button>
		<td width='10'>
		<td> <button class='btn btn-primary  href='#' onCLick='postergar_por_doc_privado()'>Firmar Solo Doc Privados</button>
		<td width='10'>
		<td> <button class='btn btn-info href='#' onCLick='postergar_por_extractos()'> Firmar Solo Extractos</button>
		</td>
		</table>
		
		<br>
		<table align='center' border='0'>
		<td width='10'>		
		<td><button class='btn btn-primary href='#' Onclick='postergar_todo();'>Postergar Firma de todos los Documentos</button><td>
		<td width='45'> </td>
		<td> <button class='btn btn-info href='#' Onclick='reanudar_todo();'>Reanudar Firma de todos los Documentos</button>
		</table>
		<br>

		
		
		
		<table class='table' width='90%' align='center'>
		<tr>
			<td class=''  >N&ordm; OT / Carat</td>
			<td class=''  >Documento</td>
			<td class=''  >N&ordm; Doc</td>
			<td class=''  >Fecha</td>
			<td class=''  >Usuario</td>
			<td class=''  >Revisor</td>
			<td class='' colspan='3' >Acci&oacute;</td>
			<td class='' colspan='' ></td>
		</tr>
		<tr>";
$fecha=date("Y-m-d");
$ff = new fechas;
$r2 = "SELECT  id, tipo, caratula, numero_firma, libro, fecha, folio_real, foja, numero, anho, hora, usuario, revisor, listo  FROM certificados_firmas WHERE listo='yep' OR listo='tmp' ORDER BY numero_firma, fecha,  caratula DESC";
$fin=mysqli_query($link, $r2);
while($row = mysqli_fetch_array($fin))
	{
	
	$tipo= mb_convert_case ( str_replace("_"," ", $row["tipo"]) , MB_CASE_TITLE, "UTF-8");;
	$dv.="<tr><td class='headtablan'>".$row["caratula"]."</a></td><td class='headtablan'><a onclick=\"return hs.htmlExpand(this, { contentId: 'highslide-html', objectType: 'iframe',	objectWidth: 640, objectHeight: 580} )\" class=\"highslide\" href='calls/ver_pdf.php?iadsfd=".$row["numero_firma"]."&pdf=PDF&quieto=yep' >".$tipo."</td>
		<td class='headtablan' align='center'>".$row["numero_firma"]."</td>
		<td class='td1' align='center'>".$ff->mysql_a_fecha($row["fecha"])." ".$row["hora"]."</td>
		<td class='td1' align='center'>".$row["usuario"]."</td>
		<td class='td1' align='center'>".$row["revisor"]."</td>";
		if($row["listo"]=="yep")
			{
			$iir ++;
			$asd = $iir;
			$dv.="<td class='td1' align='center'><a href='#' title='Postergar Firma'  onclick='postergar_firma(".$row["id"].");'><img src='imagenes/caratula_modificar.png' width='22'></a></td>";}
		else
			{
			$asd = "";			
			$dv.="<td class='td1' align='center'><a href='#' title='Enviar a Firma'  onclick='postergar_cancelar_firma(".$row["id"].");'><img src='imagenes/repertorio_modificar.png' width='22'></a></td>";
			}
		$dv.="<td width='10'></td>
			<td class='td1' align='center'><a href='#' onclick='eliminar_firma(".$row["id"].");'><img src='imagenes/vig_n.jpg'></a></td>
			<td class='td1' align='center'>".$asd."";

		$dv.="<tr>";
		$mms="";
		
			$r_e = "SELECT * FROM `certificados_tmp_AAA_NAP` WHERE id_fojas = '".$row["numero_firma"]."'  ";
			$fin_e=mysqli_query($link, $r_e);			
			$row_e = mysqli_fetch_array($fin_e);
			if($row_e["id"]<>"")
				{
				$r_upt = "UPDATE certificados_metadata SET repertorio_notaria = '".$row_e["repertorio_asignado"]."' WHERE id_certificado='".$row["numero_firma"]."'  ";
				$fin_upt=mysqli_query($link, $r_upt);			
				}

			$r_e = "SELECT * FROM certificados_metadata WHERE id_certificado='".$row["numero_firma"]."'  ";
			$fin_e=mysqli_query($link, $r_e);			
			$row_e = mysqli_fetch_array($fin_e);
			if($row_e["id_certificado"]<>"")
			{	
			if($tipo=="Extracto" )
			{$mms=$row_e["nombre_soc"]." - Capital:".$row_e["capital"]." -  kj askj Repertorio:".$row_e["repertorio_notaria"];}
			else
				{
				if($row["libro"]=="Escrituras Publicas" )
					{$mms=$row_e["tipo"]." - Repertorio:".$row_e["repertorio_notaria"]." - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
				if($row_e["repertorio_notaria"]	<>"")
					{$mms=$row_e["tipo"]." - Repertorio:".$row_e["repertorio_notaria"]."-".$row_e["campo4_nombre"]."    - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
				if($row_e["campo7_valor"]<>"")
					{$mms.=" Patente:".$row_e["campo7_valor"];}
				elseif($row_e["campo6_nombre"]	<>"")
					{$mms=" Banco:".$row_e["campo6_nombre"]." N� Cuenta".$row_e["campo6_valor"]."  - ".$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
				elseif($row_e["compareciente_1"]<>"" || $row_e["compareciente_2"]<>"" )
					{$mms=$row_e["compareciente_1"]." / ".$row_e["compareciente_2"];}
				}
			$r2_sel = "SELECT * FROM `cert_firma_matricera` WHERE `numero_firma` =  '".$row["numero_firma"]."' ";
			$fin_sel = mysqli_query($link, $r2_sel);
			$row_sel = mysqli_fetch_array($fin_sel);		
			if($row_sel["id"]<>""){$mms.= " Area: ".$row_sel["matricera"];}

		}
		if($mms<>"")
		{$dv.="<tr><td class='td1' colspan='10'  >".$mms."</td></tr>";}
		$dv.="
	<tr height='8'><td colspan='10' >
	<div id='id_".$row["id"]."'></div>
	</td></tr>";
	}

	$dv.="</table>";


	$ddd= new datos;
	$dd= new net;
	if($dd->status( $ddd->fojas_dir_ip() )=="live")
		{
		$dv.="";
		}
	else
		{
		$dv.="</table>
		<div class='text_negative' align='center'><br>Conecci&oacute;n no disponible con Fojas.cl.
		<br>Verifique el estado del enlace de Internet del servidor y Fojas.cl.</div>";
		}



return $dv;
 }

function centro()
	{
	$dv=cuerpo();
	$tab = new TinyAjaxBehavior();	
	$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
	$dv=" <div id='titulo'><p align='center' class=''>Autorizacion de Certificados</strong></p></div>";
	$tab->add( TabInnerHtml::getBehavior("titulo", $dv));
	$conect= new ingreso;
	$link=$conect->Conectarse();
	$r2 = "SELECT COUNT(ID) AS nn  FROM certificados_firmas WHERE listo='nop' ";
	$fin=mysqli_query($link, $r2);
	$row = mysqli_fetch_array($fin);
	$nn=$row["nn"];
	$r2 = "SELECT COUNT(ID) AS nn  FROM certificados_firmas WHERE listo='yep' ";
	$fin=mysqli_query($link, $r2);
	$row = mysqli_fetch_array($fin);
	$nn2=$row["nn"];
	$dv="<table><td class='td4'>Documentos Para Firma:</td>
		<td class=''  align='center'><a href='#' align='center' Onclick='centro();'> Documentos por Autorizar ($nn) </a>
		<td class=''  align='center'><a href='#' align='center' Onclick='centro_firma();'> Documentos por Firmar ($nn2) </a> </td>
		</table>";
	$tab->add( TabInnerHtml::getBehavior("cabeza_cert", $dv));
	return $tab->getString();
	}

function centro_firma()
	{
	$dv=cuerpo_firma();
	$tab = new TinyAjaxBehavior();
	$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
	$dv=" <div id='titulo'><p align='center' class=''>Documentos en Firma</strong></p></div>";
	$tab->add( TabInnerHtml::getBehavior("titulo", $dv));
	$conect= new ingreso;
	$link=$conect->Conectarse();
	$r2 = "SELECT COUNT(ID) AS nn  FROM certificados_firmas WHERE listo='nop' ";
	$fin=mysqli_query($link, $r2);
	$row = mysqli_fetch_array($fin);
	$nn=$row["nn"];
	$r2 = "SELECT COUNT(ID) AS nn  FROM certificados_firmas WHERE listo='yep' OR listo='tmp' ";
	$fin=mysqli_query($link, $r2);
	$row = mysqli_fetch_array($fin);
	$nn2=$row["nn"];
	$dv="<table><td class='td4'>Documentos Para Firma:</td>
		<td class='td4'  align='center'><a href='#' Onclick='centro();'> Documentos por Autorizar ($nn) </a> </td>
		<td class='td4'  align='center'><a href='#' Onclick='centro_firma();'> Documentos por Firmar ($nn2) </a> </td>
		</table>";
	$tab->add( TabInnerHtml::getBehavior("cabeza_cert", $dv));
	return $tab->getString();
	}

function resto()
	{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE listo='nop' ";
		$fin=mysqli_query($link, $r2);
		$dv="<br><br><div class='text_positive'>Se Autorizaron las Solicitudes</div>
		<br><div class='text_positive'>
			<br>Ingrese al Sistema de Firmas para aplicar la firma electronica.<br>
		<br><br><br>";
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
		}

function resto_firma()
	{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$link_firma=$conect->Conectarse_firma();

		$rr_firma="DELETE FROM certificados_por_firmar ";
		$fin_firma=mysqli_query($link_firma, $rr_firma);
		$rr_firma="DELETE FROM certificados_firmados ";
		$fin_firma=mysqli_query($link_firma, $rr_firma);

		$r2 = "SELECT *  FROM certificados_firmas WHERE listo='yep' ORDER BY caratula ASC";
		$fin=mysqli_query($link, $r2);
		$source=getcwd();
		$source.="/Digi/firma/";


//		$source=getcwd();
//		$source=str_replace("oficina/super","Digi/firma/",$source);

		$dir=str_replace("","",$dir);
		$text=str_replace("'","",$text);
		//Falta cargar la funcion que limpia el servidor
		limpiar($source, $source);
		while($row = mysqli_fetch_array($fin))
			{
			$data=$row["data"];
			$nombre=$source."cert_".$row["id"].".pdf";
			$fp = fopen($nombre,"w");
			fwrite($fp, $data);
			fclose($fp);			
			$filesize=filesize($nombre);
				
				
			$data_firma = addslashes(fread(fopen($nombre, "r"), filesize($nombre)));
			$rr_firma="INSERT INTO certificados_por_firmar (datos, numero_certificado, listo, fecha ) VALUES
					('$data_firma', '".$row["numero_firma"]."', 'si', '".date("Y-m-d")."' )";	 
			$fin_firma=mysqli_query($link_firma, $rr_firma);
			}

		$ddd= new datos;
		$dd= new net;
		if($dd->status( $ddd->fojas_dir_ip() )=="live")
			{
			$msg="<br><br>
			<div class='headtabla'>Se Generaron los archivos.<br>
			<br>Ingrese al Sistema de Firmas para aplicar la firma electronica.<br>";
			}
		else
			{
			$msg.="</table>
			<div class='text_negative' align='center'><br>Conecci&oacute;n no disponible con Fojas.cl.
			<br>Verifique el estado del enlace de Internet del servidor y Fojas.cl.</div>";
			}

	
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $msg));
		return $tab->getString();
		}

function subir()
	{
	$source=getcwd();
	$source.="/Digi/firma/";
//	$source=str_replace("oficina/super","Digi/firma",$source);
	$dir=str_replace("","",$dir);
	$dv=subir_auto($source);
//	limpiar($source, $source);
	$tab = new TinyAjaxBehavior();
	$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
	return $tab->getString();
	}



  function confirmar($id)
		{
		$tab = new TinyAjaxBehavior();
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
			{
			$r2 = "SELECT  id, tipo, caratula, numero_firma, libro, folio_real, foja, numero, anho, hora, usuario, revisor  FROM certificados_firmas WHERE id= '".$id."' ";
			$fin=mysqli_query($link, $r2);
			$row = mysqli_fetch_array($fin);
			$tipo= mb_convert_case ( str_replace("_"," ", $row["tipo"]) , MB_CASE_TITLE, "UTF-8");;
			$numero_firma=$row["numero_firma"];
			$msg="<br>
				<table width='100%' align='center' border='0'>
					<tr>
						<td colspan='3' class='td1_red'>
							ESTA SEGURO DE ENVIAR A FIRMA EL DOCUMENTO N&ordm; $numero_firma - $tipo
						</td>
					<tr>
					<tr>
					<td align='center'  class='td1_red'><input type='button' class='btn btn-success btn-lg' value='AUTORIZAR FIRMA' onclick='confirmar2($id);'></td>
					<td width='20' class='td1_red'></td>
					<td align='center'  class='td1_red'><input type='button' class='btn btn-danger btn-lg'  value='CANCELAR' onclick='borrar_confirmar($id);'></td>
				</table><br>
				";
			$tab->add( TabInnerHtml::getBehavior("id_".$id, $msg));
			$tab->add( TabSetFocus::getBehavior("id_".$id, $msg));
			}
		return $tab->getString();
	}

  function confirmar2($id)
		{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
			{
			$r2 = "UPDATE certificados_firmas SET listo='yep' , revisor='".$_SESSION['usuario']."' WHERE id='$id' ";
			$fin=mysqli_query($link, $r2);
			}
		$dv=cuerpo();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
	}
	
	
	function devolver_doc_auto($id)
		{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
			{
			$r2 = "UPDATE certificados_firmas SET listo='nop'  WHERE id='$id' ";
			$fin=mysqli_query($link, $r2);

			$div = "div_back_".$id."";
			$dv = "<img src ='imagenes/vig_s.jpg' width='20'>";

			//SE CUENTAN LOS CERTIFICADOS POR FIRMAR
			$r2 = "SELECT COUNT(ID) AS nn  FROM certificados_firmas WHERE listo='nop' ";
			$fin=mysqli_query($link, $r2);
			$row = mysqli_fetch_array($fin);
			$nn=$row["nn"];
			$r2 = "SELECT COUNT(ID) AS nn  FROM certificados_firmas WHERE listo='yep' ";
			$fin=mysqli_query($link, $r2);
			$row = mysqli_fetch_array($fin);
			$nn2=$row["nn"];

			$doc_por_autorizar = "<a href='#' align='center' Onclick='centro();'>Documentos por Autorizar ($nn) </a> /";
			$doc_por_firmar = "<a href='#' align='center' Onclick='centro_firma();'>Documentos por Firmar ($nn2) </a>";

			$tab = new TinyAjaxBehavior();
			$tab->add( TabInnerHtml::getBehavior($div, $dv));
			$tab->add( TabInnerHtml::getBehavior("doc_por_autorizar", $doc_por_autorizar));
			$tab->add( TabInnerHtml::getBehavior("doc_por_firmar", $doc_por_firmar));
			return $tab->getString();
			}	


	}
	
	function confirmar3()
		{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$tab = new TinyAjaxBehavior();
		$ip=$_SERVER['REMOTE_ADDR'];
		$usuario=$_SESSION['usuario'];
		
		$rep2 = "SELECT * FROM checkeados_firmar WHERE usuario='".$usuario."' AND ip='".$ip."' LIMIT 1";
		$fin_rep=mysqli_query($link, $rep2);
		$data_reper=mysql_fetch_assoc($fin_rep);
		if ($data_reper['id']<>'')
		{
			$checkeados = $data_reper['checkeados'];
			$pos = strpos($checkeados, ',');

			if ($pos !== false) {
     			$ya_checkeados      	 = explode(",", $checkeados);
     		
			foreach($ya_checkeados as $id)
			{
				if($id<>"")
				{
				$r2 = "UPDATE certificados_firmas SET listo='yep' , revisor='".$_SESSION['usuario']."' WHERE id='$id' ";
				$fin=mysqli_query($link, $r2);
				}
			}
				
			} else if ($checkeados<>'' && $checkeados<>','){
     		
     			
				$r2 = "UPDATE certificados_firmas SET listo='yep' , revisor='".$_SESSION['usuario']."' WHERE id='$checkeados' ";
				$fin=mysqli_query($link, $r2);
     		
			}
			
		$result1 = "DELETE FROM checkeados_firmar  WHERE usuario = '".$usuario."' AND  ip = '".$ip."'";
		$fin1 = mysqli_query($link, $result1);
		
		
		}else{
			$tab->add( TabInnerHtml::getBehavior("html_advertencia_edu", 'Se debe seleccionar al menos uno'));
		
		}
		$dv=cuerpo();
		
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
	}
	
  function eliminar($id)
		{
		$tab = new TinyAjaxBehavior();
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
			{
			$r2 = "SELECT  id, tipo, caratula, numero_firma, libro, folio_real, foja, numero, anho, hora, usuario, revisor  FROM certificados_firmas WHERE id= '".$id."' ";
			$fin=mysqli_query($link, $r2);
			$row = mysqli_fetch_array($fin);
			$tipo= mb_convert_case ( str_replace("_"," ", $row["tipo"]) , MB_CASE_TITLE, "UTF-8");;
			$numero_firma=$row["numero_firma"];
			$msg="<br>
				<table width='100%' align='center' border='0'>
					<tr>
						<td colspan='3' class='td1_red'>
							DESEA ELIMINAR EL DOCUMENTO N&ordm; $numero_firma - $tipo
						</td>
					<tr>
						<td align='center'><input type='button' class='btn btn-danger btn-lg' value='ELIMINAR' onclick='eliminar2($id);'></td>
						<td width='20'></td>
						<td align='center'><input type='button' class='btn btn-success btn-lg' value='CANCELAR' onclick='borrar_confirmar($id);'></td>
					</tr>
				</table><br>
				";
			$tab->add( TabInnerHtml::getBehavior("id_".$id, $msg));
			$tab->add( TabSetFocus_div::getBehavior("id_".$id));
			}
		return $tab->getString();
	}

  function eliminar2($id)
		{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
			{
			$r3 = "SELECT * FROM certificados_firmas WHERE id ='$id'";
			$fin=mysqli_query($link, $r3);
			$row = mysqli_fetch_array($fin);
			$numero_firma= $row["numero_firma"];
			$mensaje_rastreo="Doc $numero_firma eliminado de docs por autorizar";
			$usr = new usuario();
			$usr->rastreo( $_SESSION['usuario'], $mensaje_rastreo, "");
			$r2 = "DELETE FROM certificados_firmas WHERE id='$id' ";
			$fin=mysqli_query($link, $r2);
			}
		$dv=cuerpo();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
		}

  function eliminar_firma($id)
		{
		$tab = new TinyAjaxBehavior();
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
			{
			$msg="
				<table width='50%' align='center' border='0'>
					<td><input type='button' class='btn btn-danger btn-lg'  value='Eliminar' onclick='eliminar2_firma($id);'></td>
					<td width='20'></td>
					<td><input type='button' class='btn btn-success btn-lg'  value='Cancelar' onclick='borrar_confirmar($id);'></td>
				</table>";
			$tab->add( TabInnerHtml::getBehavior("id_".$id, $msg));
			$tab->add( TabSetFocus_div::getBehavior("id_".$id));
			}
		return $tab->getString();
	}

  function eliminar2_firma($id)
		{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
			{
			$r3 = "SELECT * FROM certificados_firmas WHERE id ='$id'";
			$fin=mysqli_query($link, $r3);
			$row = mysqli_fetch_array($fin);
			$numero_firma= $row["numero_firma"];
			$mensaje_rastreo="Doc $numero_firma eliminado de docs por firmar";				
			$usr = new usuario();
			$usr->rastreo( $_SESSION['usuario'], $mensaje_rastreo, "");
			$r2 = "DELETE FROM certificados_firmas WHERE id='$id' ";
			$fin=mysqli_query($link, $r2);
			}
		$dv=cuerpo_firma();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
		}

    function borrar_confirmar($id)
		{
		$tab = new TinyAjaxBehavior();
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
			{
			$msg="";
			$tab->add( TabInnerHtml::getBehavior("id_".$id, $msg));
			}
		return $tab->getString();
		}
	

  function postergar_por_caratula($caratula_firma)
	{
	$dis= new diseno;
	$caratula_firma=$dis->protege($caratula_firma);
	if($caratula_firma<>"" )
		{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
		$fin=mysqli_query($link, $r2);
			
		$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE caratula='$caratula_firma' AND  listo='tmp' ";
		$fin=mysqli_query($link, $r2);
		}	
	$dv=cuerpo_firma();
	$tab = new TinyAjaxBehavior();
	$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
	return $tab->getString();
	}	

	function postergar_por_caratula_agregar($caratula_firma)
	{
		$dis= new diseno;
		$caratula_firma=$dis->protege($caratula_firma);
		if($caratula_firma<>"" )
		{
			$conect= new ingreso;
			$link=$conect->Conectarse();
//			$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
//			$fin=mysqli_query($link, $r2);
			
			$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE caratula='$caratula_firma' AND  listo='tmp' ";
			$fin=mysqli_query($link, $r2);
		}	
		$dv=cuerpo_firma();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
	}		

	function postergar_todo_fecha($dia, $mes, $anho)
	{
		$ffff=$anho."-".$mes."-".$dia;
		if($dia<>"" && $mes<>"" && $anho<>"")
			{
			$conect= new ingreso;
			$link=$conect->Conectarse();
			$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
			$fin=mysqli_query($link, $r2);
				
			$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE fecha='$ffff' AND  listo='tmp' ";
			$fin=mysqli_query($link, $r2);
			}	
		$dv=cuerpo_firma();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
	}	

	function postergar_todo_usuario($usuario)
	{
		if($usuario<>"")
		{
			$conect= new ingreso;
			$link=$conect->Conectarse();
			$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
			$fin=mysqli_query($link, $r2);
			
			$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE usuario='$usuario' AND  listo='tmp' ";
			$fin=mysqli_query($link, $r2);
		}	
		$dv=cuerpo_firma();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
	}	

function postergar_todo()
	{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
		$fin=mysqli_query($link, $r2);

		$dv=cuerpo_firma();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
	}	
	

	function reanudar_todo()
	{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE listo='tmp' ";
		$fin=mysqli_query($link, $r2);
		$dv=cuerpo_firma();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		return $tab->getString();
	}	
	
	  function postergar_por_doc_privado()
	{
	$dis= new diseno;

		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
		$fin=mysqli_query($link, $r2);
			
		$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE libro='Documento Privado' AND  listo='tmp' ";
		$fin=mysqli_query($link, $r2);

	$dv=cuerpo_firma();
	$tab = new TinyAjaxBehavior();
	$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
	return $tab->getString();
	}	

	  function postergar_por_escrituras_publicas()
	{
	$dis= new diseno;

		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
		$fin=mysqli_query($link, $r2);
			
		$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE libro='Escrituras Publicas' AND  listo='tmp' ";
		$fin=mysqli_query($link, $r2);

	$dv=cuerpo_firma();
	$tab = new TinyAjaxBehavior();
	$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
	return $tab->getString();
	}	

	  function postergar_por_extractos()
	{
	$dis= new diseno;
	$caratula_firma=$dis->protege($caratula_firma);

		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
		$fin=mysqli_query($link, $r2);
			
		$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE tipo='EXTRACTO' AND  listo='tmp' ";
		$fin=mysqli_query($link, $r2);

	$dv=cuerpo_firma();
	$tab = new TinyAjaxBehavior();
	$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
	return $tab->getString();
	}		

	  function postergar_todos()
	{
	$dis= new diseno;
	$caratula_firma=$dis->protege($caratula_firma);

		$conect= new ingreso;
		$link=$conect->Conectarse();
		$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE listo='yep' ";
		$fin=mysqli_query($link, $r2);
			
		$r2 = "UPDATE certificados_firmas SET listo='yep' ";
		$fin=mysqli_query($link, $r2);

	$dv=cuerpo_firma();
	$tab = new TinyAjaxBehavior();
	$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
	return $tab->getString();
	}		
	
	function limpiar_caratula($caratula)
		{
		if(strlen($caratula)>=12 )
			{
			$card= new cardex;
			$caratula=$card->comprobar_numero($caratula);
			$tab = new TinyAjaxBehavior();
			$tab->add( TabSetValue::getBehavior("caratula",$caratula));
			return $tab->getString();
			}
		}




	function limpiar_caratula_firma($caratula)
	{
		if(strlen($caratula)>=12 )
		{
			$card= new cardex;
			$caratula=$card->comprobar_numero($caratula);
			$tab = new TinyAjaxBehavior();
			$tab->add( TabSetValue::getBehavior("caratula_firma",$caratula));
			return $tab->getString();
		}
	}	
	
	
		function postergar_firma($id)
	{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
		{
			$r2 = "UPDATE certificados_firmas SET listo='tmp' WHERE id='$id' ";
			$fin=mysqli_query($link, $r2);
		}
		$dv=cuerpo_firma();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
			$tab->add( TabSetFocus_div::getBehavior("id_".$id));
		return $tab->getString();
	}	

	function postergar_cancelar_firma($id)
	{
		$conect= new ingreso;
		$link=$conect->Conectarse();
		if($id<>"")
		{
			$r2 = "UPDATE certificados_firmas SET listo='yep' WHERE id='$id' ";
			$fin=mysqli_query($link, $r2);
		}
		$dv=cuerpo_firma();
		$tab = new TinyAjaxBehavior();
		$tab->add( TabInnerHtml::getBehavior("cuerpo", $dv));
		$tab->add( TabSetFocus_div::getBehavior("id_".$id));
		return $tab->getString();
	}
	
	function focus_pocus()
	{
	$tab = new TinyAjaxBehavior();
	$tab->add( TabSetFocus::getBehavior("nanana"));
	return $tab->getString();
	}	
	
	
	function asignar_opcion($id,$checked,$checkeados)
	{
		$tab = new TinyAjaxBehavior();
		$conect= new ingreso;
		$link=$conect->Conectarse();
		$cant_check = array();
		$ya_checkeados_f = array();
		$string_input = '';
		
		$pos = strpos($checkeados, ',');

		if ($pos !== false) {
     		$ya_checkeados      	 = explode(",", $checkeados);
     		//$ya_checkeados_cant      = explode(",", $cant_checkeados);
     		
			foreach($ya_checkeados as $key=>$c)
			{
				$ya_checkeados_f[$c]=$c;
				//$cant_check[$c] =  $ya_checkeados_cant[$key];
			}
				
		} else if ($checkeados<>''){
     		$ya_checkeados_f[$checkeados]=$checkeados;
     		//$cant_check[$checkeados] = $cant_checkeados;
		}
		

			if ($checked=='true')
			{
				$ya_checkeados_f[$id] = $id;
				//$cant_check[$id] = $cant;
			
			}else{
				unset($ya_checkeados_f[$id]);
				//unset($cant_check[$id]);
			}
		
	 if (!empty($ya_checkeados_f))
		{
			foreach($ya_checkeados_f as $c)
			{
				$string_input .= ','.$c;
				//$string_input_cant .= ','.$cant_check[$c];
			}
			
				
		}
			


		$rest = substr($string_input, 0, 1);
		if ($rest==',')
		{
			$string_input=substr($string_input,1);
		
		}
		
		$r2_ad4="CREATE TABLE IF NOT EXISTS `checkeados_firmar` (
  				`id` bigint(20) NOT NULL AUTO_INCREMENT,
  				`usuario` varchar(50) NOT NULL,
  				`checkeados` text,
  				`ip` varchar(20) NOT NULL,
  				PRIMARY KEY (`id`)
				) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
		$fin_ad4=mysqli_query($link, $r2_ad4);
		
		$ip=$_SERVER['REMOTE_ADDR'];
		$usuario=$_SESSION['usuario'];
		
		$result1 = "DELETE FROM checkeados_firmar  WHERE usuario = '".$usuario."' AND  ip = '".$ip."'";
		$fin1 = mysqli_query($link, $result1);
		
		$result="INSERT INTO checkeados_firmar (usuario,  checkeados, ip )
				VALUES ('".$usuario."' ,  '".$string_input."' , '".$ip."')";
		
		$fin1_i = mysqli_query($link, $result);
		
		
		
		$tab->add( TabSetValue::getBehavior("checkeados", $string_input));
		$tab->add( TabSetValue::getBehavior("html_advertencia_edu", $r2_ad4));
		return $tab->getString();
	
	
	}

	$ajax = new TinyAjax();   
	$ajax->showLoading();
	$ajax->exportFunction("centro");
	$ajax->exportFunction("centro_firma");
	$ajax->exportFunction("resto");
	$ajax->exportFunction("resto_firma");
	$ajax->exportFunction("confirmar");
	$ajax->exportFunction("confirmar2");
	$ajax->exportFunction("eliminar");
	$ajax->exportFunction("eliminar2");
	$ajax->exportFunction("subir");
	$ajax->exportFunction("eliminar_firma");
	$ajax->exportFunction("eliminar2_firma");
	$ajax->exportFunction("borrar_confirmar");
	$ajax->exportFunction("limpiar_caratula", array("caratula"));

	$ajax->exportFunction("postergar_firma");
	$ajax->exportFunction("postergar_cancelar_firma");

	$ajax->exportFunction("postergar_por_caratula", array("caratula_firma"));		
	$ajax->exportFunction("postergar_por_caratula_agregar", array("caratula_firma"));		
	$ajax->exportFunction("postergar_todo");
	$ajax->exportFunction("postergar_todo_fecha", array("dia_x","mes_x","anho_x"));		
	$ajax->exportFunction("postergar_todo_usuario", array("usuario"));		

	$ajax->exportFunction("postergar_por_extractos");
	$ajax->exportFunction("postergar_por_escrituras_publicas");
	$ajax->exportFunction("postergar_por_doc_privado");		
	$ajax->exportFunction("postergar_todos");		
	
	$ajax->exportFunction("reanudar_todo");	
	$ajax->exportFunction("limpiar_caratula_firma", array("caratula_firma"));
	$ajax->exportFunction("focus_pocus");	
	// eduardo
	$ajax->exportFunction("asignar_opcion");
	$ajax->exportFunction("confirmar3");	

	$ajax->exportFunction("devolver_doc_auto");	

	$ajax->process();




	echo"<html>
	<head>";
	echo"<meta http-equiv='content-type' content='text/html; charset=ISO-8859-1'>";
	$ajax->drawJavaScript(); 
	echo"
	<title>Sign</title>";

	echo "</head>
	<script language='javascript' type='text/javascript' src='calls/libreria_java.js'></script>
	<script src='dropzone/js/jquery_1.11.js'></script> 
	<script type='text/javascript' src='dropzone/js/jquery-ui.js'></script>
	<link href='dropzone/js/jquery-ui.css' type='text/css' rel='stylesheet' />
	<link href='//netdna.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css' rel='stylesheet' id='bootstrap-css'>
	<script src='//netdna.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js'></script>
	<script src='//code.jquery.com/jquery-1.11.1.min.js'></script>
	<link href='https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css' rel='stylesheet' integrity='sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN' crossorigin='anonymous'>
	<link rel='stylesheet' href='https://use.fontawesome.com/releases/v5.7.1/css/all.css' integrity='sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr' crossorigin='anonymous'>
	<script src='//netdna.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js'></script>
	<script>jQuery.noConflict();</script>
	<script type='text/javascript' src='highslide/highslide.js'></script>
	<script type='text/javascript' src='highslide/highslide-html.js'></script>
	<script type=\"text/javascript\">    
    hs.graphicsDir = 'highslide/graphics/';
    hs.outlineType = 'rounded-white';
    hs.outlineWhileAnimating = true;
    hs.objectLoadTime = 'after';
	</script>
	<script>
  jQuery(function() {
  jQuery('#arriba').click(function(){
    jQuery('body,html').animate({scrollTop : 0}, 500);
    return false;
});
jQuery(window).scroll(function(){
    if (jQuery(this).scrollTop() > 0) {
        jQuery('#arriba').fadeIn();
    } else {
        jQuery('#arriba').fadeOut();
    }
});
	 });
  </script>";
	$conect= new ingreso;
	$link=$conect->Conectarse();
	$linkfs=$conect->Coneccion_fs();
	$dis = new diseno;
	$dd = new datos;
	$dis->ingresar_body("doc_elect");

	$mgs_derecha1="";
	$msg_ini="DOCUMENTOS POR FIRMAR";
	$ip=$_SERVER['REMOTE_ADDR'];
	$usuario=$_SESSION['usuario'];
	$result1 = "DELETE FROM checkeados_firmar  WHERE usuario = '".$usuario."' AND ip = '".$ip."'";
    $fin1 = mysqli_query($link, $result1);
echo"

	
	<div class=\"highslide-html-content\" id=\"highslide-html\" style=\"width: 700px\">
		<div class=\"highslide-move\" style=\"border: 0; height: 10px; padding: 2px; cursor: default\">
		<a href=\"#\" onclick=\"return hs.close(this)\" class=\"control\">Cerrar</a>
		</div>
		<div class=\"highslide-body\"></div>
	</div>

	<div class=\"highslide-html-content\" id=\"origen\" style=\"width: 400px;\">
		<div class=\"highslide-body\" style=\"padding: 0 10px 10px 10px\"></div>
		</div>
	</div>

	<div class=\"highslide-html-content\" id=\"origen2\" style=\"width: 520px;\">
		<div class=\"highslide-move\" style=\"border: 0; height: 10px; padding: 2px; cursor: default\">
		<a href=\"#\" onclick=\"return hs.close(this)\" class=\"control\">Cerrar</a>
		</div>
		<div class=\"highslide-body\"></div>

		</div>
	</div>

	<div class=\"highslide-html-content\" id=\"highslide-html2\" style=\"width: 530px\">
	<div class=\"highslide-move\" style=\"border: 0; height: 10px; padding: 2px; cursor: default\">
	<a href=\"#\" onclick=\"return hs.close(this)\" class=\"control\">Cerrar</a>
	</div>
	<div class=\"highslide-body\"></div>
	</div>	
	
	";
	
$caratula=$_REQUEST['caratula'];		
if($caratula<>"")
{
echo"
<script language=\"JavaScript1.2\">
focus_pocus();		
</script>


<script>
function loadImage() {
    alert(\"Image is loaded\");
}
</script>
";
}

ECHO"
<div id='container'>
<table class='container_table' border='0'>
<tr>
<td width='740' valign='top'>
<h2 align='center'>".$msg_ini."</h2></div>

<body OnLoad='placeFocus()'>
<div id=''> 
";  


	$conect= new ingreso;
	$link=$conect->Conectarse();
	$usur = new usuario;	


$r_irepert2 = "CREATE TABLE IF NOT EXISTS `certificados_tmp_AAA_NAP` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `empresa` varchar(30) NOT NULL,
  `cons` varchar(30) NOT NULL,
  `id_toc` varchar(30) NOT NULL,
  `tipo_documento` varchar(60) NOT NULL,
  `fecha` date NOT NULL,
  `requirente` varchar(60) NOT NULL,
  `comprador` varchar(200) NOT NULL,
  `comprador_rut` varchar(14) NOT NULL,
  `comprador_domicilio` varchar(200) NOT NULL,
  `vendedor` varchar(200) NOT NULL,
  `vendedor_rut` varchar(14) NOT NULL,
  `vendedor_domicilio` varchar(200) NOT NULL,
  `tipo_vehiculo` varchar(30) NOT NULL,
  `marca` varchar(40) NOT NULL,
  `modelo` varchar(40) NOT NULL,
  `anho` bigint(4) NOT NULL,
  `nro_motor` varchar(25) NOT NULL,
  `nro_chasis` varchar(25) NOT NULL,
  `color` varchar(20) NOT NULL,
  `patente` varchar(10) NOT NULL,
  `monto_venta` varchar(23) NOT NULL,
  `id_fojas` bigint(12) NOT NULL,
  `blokeado` varchar(3) NOT NULL,
  `repertorio_asignado` varchar(60) NOT NULL,
  `fecha_repertorio` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `cons` (`cons`,`id_toc`,`fecha`,`patente`),
  KEY `empresa` (`empresa`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1 AUTO_INCREMENT=1 ;";
		$fin_repert = mysqli_query($link, $r_irepert2);



$dt = microtime(true);


	
//if($usur->verificar_permiso($_SESSION['usuario'],"23"))
if(1==1)
	{
	$dv=cuerpo();


	//SE CUENTAN LOS CERTIFICADOS POR FIRMAR
	$r2 = "SELECT COUNT(ID) AS nn  FROM certificados_firmas WHERE listo='nop' ";
	$fin=mysqli_query($link, $r2);
	$row = mysqli_fetch_array($fin);
	$nn=$row["nn"];
	$r2 = "SELECT COUNT(ID) AS nn  FROM certificados_firmas WHERE listo='yep' ";
	$fin=mysqli_query($link, $r2);
	$row = mysqli_fetch_array($fin);
	$nn2=$row["nn"];

	


	$include=$_REQUEST["include"];			
	echo"
	<div class='arriba' id='arriba'><a href='javascript:void(0);'><img src='imagenes/arriba2.png' height='100px' width='100px'/></a></div>
	<div id='cuerpo_centro'>

  </div>";
echo" <div id='titulo'><p align='center' class=''>Autorizacion de Certificados </strong></p></div>
	<table style='margin-left:150px;' align=''>
	<td><div id='doc_por_autorizar'><a href='#' class='' Onclick='centro();'>Documentos por Autorizar ($nn) </a> </div></td>
	<td>&nbsp;&nbsp; - &nbsp;&nbsp;</td>
	<td><div id='doc_por_firmar'><a href='#' class='' Onclick='centro_firma();'>Documentos por Firmar ($nn2) </a></div></td>
	</table>
	</div>	
<div id='result'></div>
<FORM name='form10' ACTION='firma_digital.php'  METHOD='POST' >";
$sad=focus_pocus();


echo "<input type='hidden' value='$dt' name='tiempo_inicio'>
<input name='include' id='include' type='hidden' value='".$include."'>
<input type='hidden' id='checkeados' name='checkeados' value='' >
	<input type='hidden' id='cant_checkeados' name='cant_checkeados' value='' >	
<div id='cuerpo'>$dv</div>





</div></div>

  </td>";

if($_REQUEST["include"]<>"yep")
{
echo"
<td width='300' height='300'  valign='top'>
	<div id='cuadro_texto' class='cuadroDerecha' style='background:none; margin:50px 0 0 0;'>
	<br>
		<br>
		<br>
		<a href='index.php'  class='btn btn-success btn-block' style='color:white !important;'>VOLVER</a>
		<br>
		<!--<div class='textAreaDerecha'>".$mgs_derecha1.">-->
		<div id='centro_div_solicitud'></div>
		<div id='html_advertencia'></div>
		<div id='html_advertencia_edu'></div>
		<div><br></div>
		</div>
	
	";
}
echo"
</tr>
</table>
</div>
<style>
.centro2{
	margin-left:22% !important;
	margin-top:22%;
	position:relative;
}
.centro3{
position:absolute;
left :540px;
top:93px;


}
.textAreaDerecha{
	border-radius:10px;
    overflow:hidden;
	border:1px solid #536976;
	height: 30%;
	color:7D8F9F;
}
.cuadroDerecha{
	margin-left:25% !important;
}
</style>
</body></html>";




	$cons= new diseno;
	$cons->ingresar_fin();
	}
else
	{
	$cons= new diseno;
	$cons->negacion_permiso();
	}
	
			
	
function validar_pago_online($id_pago)
		{




$proxyhost = isset($_POST['proxyhost']) ? $_POST['proxyhost'] : '';
$proxyport = isset($_POST['proxyport']) ? $_POST['proxyport'] : '';
$proxyusername = isset($_POST['proxyusername']) ? $_POST['proxyusername'] : '';
$proxypassword = isset($_POST['proxypassword']) ? $_POST['proxypassword'] : '';



$client = new nusoap_client('http://notarial.cl/ws/ws_pagos.php?wsdl', true, $proxyhost, $proxyport, $proxyusername, $proxypassword);
	 		
	
			
			
			//$consk     = $ddtt->pass_notarial();               //PASSWORD DE USUARIO UNICA PARA EL NOTARIO
			//$sol= new solicitudes;
			//$consk     = $sol->devolver_pass_notarial();

			$var1 ="0777617530ac2138dbd62ae0e24bc4ed";
			$var2 ="ba0724a3b9c3992af58a74a48fc2266f";
			
					//* Actualizo estado de la descarga en el notarial
					$msg1 = $client->call('consultar_pago', array('var1' => ''.$var1.'',
					'var2' => ''.$var2.'',
					'id_unico' => ''.$id_pago.''));
					
					//echo $client->response."</br>";
					
					if ($client->fault)
					{
						echo 'Error </br>'.$msg1;
					}
					else
					{
					
						if ($msg1 == "YEP")
						{
								$dd = new ingreso;
								$link= $dd->Conectarse();
															
								$rs_up="UPDATE certificados_firmas SET listo = 'yep' WHERE id_pago = ".$id_pago."";							
								$fin_max= mysqli_query($link, $rs_up);
							
						
						}
					
						return $msg1;
					
					}
					

//return "NOP";					
		}	

?>