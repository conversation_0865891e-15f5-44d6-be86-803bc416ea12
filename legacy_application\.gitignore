##### Custom
# Carpetas Personalizadas de Desarrollador
/tmp_local/
/tmp_bin/
/tmp_qr/
/OT/tmp_local/
/OT_SINUSO/
/.idea/
/app/temporal_test/
/app/modulo_bhe/
/app/documento_privado/documento_privado_local/tmp_qr/

#archivos de configuracion
/app/ot/env.js
/layouts/config.php
app/ot/env.js
layouts/config.php

/library/html2pdf
##### Signv2 Extensions

#tmp de registro de firma
/app/registro_firma/tmp/

Icon_
*.pdf
#*.jpg
#*.jpeg
#*.png
*.zip
*.rar
*.txt
*.log
#*.css
#*.js
#*.htm
#*.html
*.map
*.afm
#*.ttf
*.exe
*.ini
*.dll
*.z
*.fdf
*.crt
*.dat
*.rtf
#*.json
*.xslx
*.xsl

##### Windows
# Caché de imágenes de Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

##### MacOS
# General
.DS_Store
.AppleDouble
.LSOverride

# Thumbnails
._*

##### Backup
*.bak
*.gho
*.ori
*.orig
*.tmp

##### Folders

/node_modules
/public/storage
/storage/*.key
/vendor
/.idea
Homestead.json
Homestead.yaml
.env
/app/documento_privado/documento_privado_local/tmp/
/tmp
/app/suplencias/tmp/
/app/escrituras_publicas/tmp/
# User-specific stuff
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf

# AWS User-specific
.idea/**/aws.xml

# Generated files
.idea/**/contentModel.xml

# Sensitive or high-churn files
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml

# Gradle
.idea/**/gradle.xml
.idea/**/libraries

# Gradle and Maven with auto-import
# When using Gradle or Maven with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
# .idea/artifacts
# .idea/compiler.xml
# .idea/jarRepositories.xml
# .idea/modules.xml
# .idea/*.iml
# .idea/modules
# *.iml
# *.ipr

# CMake
cmake-build-*/

# Mongo Explorer plugin
.idea/**/mongoSettings.xml

# File-based project format
*.iws

# IntelliJ
out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/replstate.xml

# SonarLint plugin
.idea/sonarlint/

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Editor-based Rest Client
.idea/httpRequests

# Android studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

#ioncube
!ioncube_loader_win_8.1.dll


