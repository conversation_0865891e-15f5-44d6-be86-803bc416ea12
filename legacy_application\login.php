
<?php

/* $ips_permitidas = array('127.0.0.1', '*************', '*************'); 
$default_src= "'self' " . implode(' ', $ips_permitidas);
header("Content-Security-Policy: default-src $default_src; style-src 'self' fonts.googleapis.com; font-src 'self' fonts.gstatic.com"); */
header("Referrer-Policy: no-referrer");
header("X-Content-Type-Options: nosniff");

// Include archivos config
require_once "layouts/config.php";
//require_once "config/constantes.php";new bootstrap.Modal(document.
require_once "appa/autoload.php";
/* require_once "./entidades/aplicacion.php"; */
// Verificar siel usuario ya está logueado, si es SI, entonces redirigir a página index
//file_put_contents("session.txt",'Login: '.json_encode($_SESSION)." \n",FILE_APPEND);
if (isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true) {
    header("location: " . BASE_PATH . "/index.php");
    setcookie("xtk", 0); 
    exit;
} else {
    setcookie("xtk", 1); 
}

?>

<!DOCTYPE html>
<html lang="es">

<head>
    <title>Login | SIGN+ </title>
    <?php include 'layouts/head.php'; ?>
    <!-- JAVASCRIPT -->
    <script src="<?php echo BASE_PATH; ?>/assets/libs/jquery/jquery.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/libs/metismenu/metisMenu.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/libs/simplebar/simplebar.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/assets/libs/node-waves/waves.min.js"></script>
    <script src="<?php echo BASE_PATH; ?>/library/blockUI/jquery.blockUI.js"></script>
    <script>
        function siguiente(){
            document.getElementById('secondLogin').value = 's';
            document.getElementById('username').value = "<?php echo $_REQUEST['username']; ?>"
            document.getElementById('password').value = "<?php echo $_REQUEST['password']; ?>"
            document.getElementById('form_login').submit()
        }
        function no(){
            document.getElementById('secondLogin').value = 'n';
            document.getElementById('username').value = ""
            document.getElementById('password').value = ""
            document.getElementById('form_login').submit()
        }
    </script>
    <!-- Punto de Acceso a App js -->
</head>
<!-- Modal -->
<div class="modal fade" id="confirmar_login" tabindex="-1" aria-labelledby="confirmar_loginLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="confirmar_loginLabel">SIGN+</h5>
        <!-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button> -->
      </div>
      <div class="modal-body">
      Ya existe una sesión activa con este usuario, <br>¿Desea realmente ingresar?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary reject-login" data-bs-dismiss="modal" onclick='no()'>No Ingresar</button>
        <button type="button" class="btn btn-primary submit-login" onclick='siguiente()'>Ingresar a SIGN+</button>
      </div>
    </div>
  </div>
</div>
<?php
// Definición de variables
$username = $password = "";
$username_err = $password_err = "";

// Procesamiento de datos del formulario cuando es enviado
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Verificar si el usuario está vacío
    if (empty(trim($_POST["username"]))) {
        $username_err = "Por favor, ingrese un nombre de usuario.";
    } else {
        $username = trim($_POST["username"]);
    }

    // verificar si la contraseña está vacía
    if (empty(trim($_POST["password"]))) {
        $password_err = "Por favor, ingrese una contraseña.";
    } else {
        $password = trim($_POST["password"]);
    }

    // Validar credenciales
    if (empty($username_err) && empty($password_err)) {
        $secondLogin = $_REQUEST['secondLogin'] <> "" ? $_REQUEST['secondLogin'] : 'n';
        $user = new entidades\usuario();
        $resp_validacion = $user->validarLogin($username, $password, $secondLogin);
        //file_put_contents("session.txt",'Login valida resp: '.json_encode($resp_validacion)." \n",FILE_APPEND);
        if ($resp_validacion['estado']) {
            $token = $user->getCipheredToken();
            header("location: index.php");
            //echo "<script>localStorage.setItem('token', '$token');</script>";
        } else {
            if($resp_validacion['mensaje'] == 'Ya existe una sesión activa con este usuario'){
                //Abro modal para confirmar ingreso en un segundo equipo de la misma sesion y matar la primera
                setcookie("ssn", 1); 
                echo "<script> 
                        var myModal = new bootstrap.Modal(document.getElementById('confirmar_login')) 
                        myModal.show()      
                    </script>
                    ";
            }else{
                setcookie("ssn", 0); 
                $username_err = $resp_validacion['mensaje'];
            }
            
        }
    }
}

?>
<body class="custom-background" >
    <div class="account-pages my-5 pt-sm-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6 col-xl-5">
                    <div class="card overflow-hidden">
                        <div class="bg-primary" >
                            <div class="row">
                                <div class="col-7">
                                    <div class="text-black p-4">
                                        <h5 class="text-black">¡ Bienvenido !</h5>
                                        <p>Ingrese sus credenciales para acceder a SIGN+</p>
                                    </div>
                                </div>
                                <div class="col-5 align-self-end">
                                    <img src="<?php echo BASE_PATH ?>/assets/images/profile-img.png" alt="" class="img-fluid">
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div class="auth-logo">
                                <a href="index.php" class="auth-logo-light">
                                    <div class="avatar-md profile-user-wid mb-4">
                                        <span class="avatar-title rounded-circle bg-light">
                                            <img src="<?php echo BASE_PATH ?>/assets/images/sign-iso.svg" alt="" class="rounded-circle" height="34">
                                        </span>
                                    </div>
                                </a>

                                <a href="index.php" class="auth-logo-dark">
                                    <div class="avatar-md profile-user-wid mb-4">
                                        <span class="avatar-title rounded-circle bg-light">
                                            <img src="<?php echo BASE_PATH ?>/assets/images/sign-iso.svg" alt="" height="34">
                                        </span>
                                    </div>
                                </a>
                            </div>
                            <div class="p-2">
                                <form id='form_login' class="form-horizontal" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                <input type='hidden' id='secondLogin' name='secondLogin' value='n' >
                                    <div class="mb-3 <?php echo (!empty($username_err)) ? 'has-error' : ''; ?>">
                                        <label for="username" class="form-label">Usuario</label>
                                        <input type="text" class="form-control" id="username" placeholder="Ingrese su Nombre de Usuario" name="username" value="">
                                        <span class="text-danger"><?php echo $username_err; ?></span>
                                    </div>

                                    <div class="mb-3 <?php echo (!empty($password_err)) ? 'has-error' : ''; ?>">
                                        <label class="form-label">Contraseña</label>
                                        <div class="input-group auth-pass-inputgroup">
                                            <input type="password" id="password" name="password" class="form-control" autocomplete="new-password" placeholder="Ingrese su password" aria-label="Password" aria-describedby="password-addon" value="">
                                            <button class="btn btn-light " type="button" id="password-addon"><i class="mdi mdi-eye-outline"></i></button>
                                        </div>
                                        <span class="text-danger"><?php echo $password_err; ?></span>
                                    </div>

                                    <!-- <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember-check" style="accent-color:#42a4d7;">
                                        <label class="form-check-label" for="remember-check">
                                            Recordar datos
                                        </label>
                                    </div> -->

                                    <div class="mt-3 d-grid">
                                        <button class="btn waves-effect waves-light submit-login"  type="submit" value="Login">Ingresar a SIGN+</button>
                                    </div>

                                    <!-- <div class="mt-4 text-center">
                                        <a href="auth-recoverpw.php" class="text-muted"><i class="mdi mdi-lock me-1"></i> ¿Olvidó su contraseña?</a>
                                    </div> -->
                                </form>
                            </div>

                        </div>
                    </div>
                    <div class="mt-5 text-center">

                        <div>
                            <p id="year-placeholder">© <span id="current-year-placeholder"></span> Soc. Consultores APPA y Cía. Ltda.</p>

                           <!--  <p>© <script>
                                    document.write(new Date().getFullYear())
                                </script> Soc. Consultores APPA y Cía. Ltda.</p> -->
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    
    <!-- Fin de Página -->

    
 



</body>
<script src="<?php echo BASE_PATH; ?>assets\js\login.min.js"></script>
<link rel="stylesheet" href="<?php echo BASE_PATH; ?>assets\css\login.css">
</html>