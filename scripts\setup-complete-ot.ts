// Complete OT System Setup Script
import { PrismaClient } from '@prisma/client'
import { initializePermissions } from './init-permissions'
import { seedOTData } from './seed-ot-data'

const prisma = new PrismaClient()

async function setupCompleteOT() {
  console.log('🚀 Setting up complete OT system...')

  try {
    // 1. Initialize permissions
    console.log('🔐 Step 1: Initializing permissions...')
    await initializePermissions()

    // 2. Seed OT data
    console.log('🌱 Step 2: Seeding OT data...')
    await seedOTData()

    // 3. Create additional sample data
    console.log('📄 Step 3: Creating additional sample data...')
    
    const adminUser = await prisma.user.findUnique({ where: { username: 'ADMIN' } })
    if (adminUser) {
      // Create more sample OTs
      const sampleOTs = [
        {
          numeroOT: 'OT-2025-002',
          tipoDocumento: 'documento_privado',
          materiaDetalle: 'Contrato de arriendo de local comercial en Las Condes',
          estado: 'EN_PROCESO',
          prioridad: 'ALTA',
          gestora: 'INMOBILIARIA PREMIUM LTDA.',
          numeroWorkflow: 'WF-2025-002',
          observaciones: 'Cliente requiere urgencia en la tramitación',
          creadoPor: adminUser.id,
          comparecientes: [
            {
              rut: '11111111-1',
              nombres: 'CARLOS EDUARDO',
              apellidoPaterno: 'MARTINEZ',
              apellidoMaterno: 'LOPEZ',
              calidad: 'ARRENDADOR',
              esChileno: true
            },
            {
              rut: '22222222-2',
              nombres: 'SOFIA ALEJANDRA',
              apellidoPaterno: 'TORRES',
              apellidoMaterno: 'RAMIREZ',
              calidad: 'ARRENDATARIO',
              esChileno: true
            }
          ]
        },
        {
          numeroOT: 'OT-2025-003',
          tipoDocumento: 'repertorio_vehiculo',
          materiaDetalle: 'Transferencia de vehículo marca Toyota, modelo Corolla, año 2020',
          estado: 'CREADA',
          prioridad: 'MEDIA',
          gestora: 'AUTOMOTORA CENTRAL S.A.',
          numeroWorkflow: 'WF-2025-003',
          observaciones: 'Verificar documentos del vehículo antes de proceder',
          creadoPor: adminUser.id,
          comparecientes: [
            {
              rut: '33333333-3',
              nombres: 'MIGUEL ANGEL',
              apellidoPaterno: 'FERNANDEZ',
              apellidoMaterno: 'CASTRO',
              calidad: 'VENDEDOR',
              esChileno: true
            },
            {
              rut: '44444444-4',
              nombres: 'PATRICIA ELENA',
              apellidoPaterno: 'MORALES',
              apellidoMaterno: 'SILVA',
              calidad: 'COMPRADOR',
              esChileno: true
            }
          ]
        }
      ]

      for (const otData of sampleOTs) {
        // Check if OT already exists
        const existingOT = await prisma.ordenTrabajo.findUnique({ where: { numeroOT: otData.numeroOT } })
        if (existingOT) {
          console.log(`✅ Sample OT ${otData.numeroOT} already exists, skipping`)
          continue
        }

        const ot = await prisma.ordenTrabajo.create({
          data: {
            numeroOT: otData.numeroOT,
            tipoDocumento: otData.tipoDocumento,
            materiaDetalle: otData.materiaDetalle,
            estado: otData.estado,
            prioridad: otData.prioridad,
            gestora: otData.gestora,
            numeroWorkflow: otData.numeroWorkflow,
            observaciones: otData.observaciones,
            creadoPor: otData.creadoPor,
            comparecientes: {
              create: otData.comparecientes
            },
            historial: {
              create: {
                accion: 'CREADA',
                descripcion: `OT ${otData.numeroOT} creada en el sistema`,
                estadoNuevo: otData.estado,
                creadoPor: otData.creadoPor
              }
            }
          }
        })

        console.log(`✅ Created sample OT: ${ot.numeroOT}`)
      }
    }

    // 4. Verify system configuration
    console.log('🔍 Step 4: Verifying system configuration...')
    
    const stats = {
      users: await prisma.user.count(),
      roles: await prisma.role.count(),
      permissions: await prisma.permission.count(),
      ots: await prisma.ordenTrabajo.count(),
      tiposMateria: await prisma.tipoMateria.count(),
      workflows: await prisma.workflow.count()
    }

    console.log('📊 System Statistics:')
    console.log(`   👥 Users: ${stats.users}`)
    console.log(`   🔐 Roles: ${stats.roles}`)
    console.log(`   🎫 Permissions: ${stats.permissions}`)
    console.log(`   📄 OTs: ${stats.ots}`)
    console.log(`   📋 Tipos Materia: ${stats.tiposMateria}`)
    console.log(`   🔄 Workflows: ${stats.workflows}`)

    // 5. Create system configuration
    console.log('⚙️ Step 5: Creating system configuration...')
    
    const configs = [
      { key: 'ot_numero_format', value: 'OT-{YEAR}-{SEQUENCE}', description: 'Formato de numeración de OT' },
      { key: 'ot_sequence_current', value: '3', description: 'Secuencia actual de OT' },
      { key: 'uaf_value_current', value: '37000', description: 'Valor actual de la UAF en pesos' },
      { key: 'rndpa_enabled', value: 'true', description: 'RNDPA habilitado' },
      { key: 'roe_threshold_uaf', value: '10000', description: 'Umbral ROE en UAF' },
      { key: 'system_name', value: 'e-Notaría', description: 'Nombre del sistema' },
      { key: 'system_version', value: '2.0.0', description: 'Versión del sistema' }
    ]

    for (const config of configs) {
      await prisma.systemConfig.upsert({
        where: { key: config.key },
        update: { value: config.value, description: config.description },
        create: {
          key: config.key,
          value: config.value,
          description: config.description
        }
      })
    }

    console.log('✅ Complete OT system setup finished successfully!')
    console.log('')
    console.log('🎉 System is ready!')
    console.log('📍 Access: http://localhost:3000')
    console.log('👤 Admin: ADMIN / admin123')
    console.log('👤 Test User: TESTUSER / test123')
    console.log('')
    console.log('📋 Available features:')
    console.log('   ✅ Multi-tab OT creation (like legacy)')
    console.log('   ✅ Role-based permissions')
    console.log('   ✅ Error logging with unique IDs')
    console.log('   ✅ Complete workflow management')
    console.log('   ✅ RNDPA integration')
    console.log('   ✅ UAF/ROE tracking')
    console.log('   ✅ Valorization management')
    console.log('   ✅ Complete audit trail')

  } catch (error) {
    console.error('❌ Error setting up OT system:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
if (require.main === module) {
  setupCompleteOT()
    .then(() => {
      console.log('🎊 Setup completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error)
      process.exit(1)
    })
}

export { setupCompleteOT }
