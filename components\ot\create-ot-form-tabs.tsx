"use client"

import { useState, useTransition } from "react"
import { createOT } from "@/src/presentation/actions/ot-actions"
import { ErrorDisplay, useError<PERSON><PERSON><PERSON> } from "../ui/error-display"

// Tab components
import { DatosOTTab } from "./tabs/datos-ot-tab"
import { ValorizacionTab } from "./tabs/valorizacion-tab"
import { AvancesTab } from "./tabs/avances-tab"
import { RepertorioTab } from "./tabs/repertorio-tab"
import { UAFTab } from "./tabs/uaf-tab"
import { RNDPATab } from "./tabs/rndpa-tab"
import { HistorialTab } from "./tabs/historial-tab"

interface FormData {
  documentTypes: Array<{value: string, label: string}>
  priorities: Array<{value: string, label: string, color: string}>
  calidades: Array<{value: string, label: string}>
  currentUser: {id: string, name: string}
}

interface CreateOTFormTabsProps {
  formData: FormData
  currentUser: any
}

interface Tab {
  id: string
  label: string
  isActive: boolean
  disabled: boolean
  permission?: string
}

interface OTFormState {
  // Datos principales
  id?: string
  numeroOT?: string
  tipoDocumento: string
  fechaOT: string
  materiaDetalle: string

  // Repertorio
  numeroRepertorio?: string
  fechaRepertorio?: string
  fojaRepertorio?: string

  // Cliente y gestión
  gestora?: string
  numeroWorkflow?: string
  observaciones?: string

  // Comparecientes (se pueden agregar antes de crear la OT)
  comparecientes: Array<{
    id: string
    rut: string
    nombres: string
    apellidoPaterno: string
    apellidoMaterno: string
    calidad: string
    esChileno: boolean
  }>

  // Estados
  estado: string
  fechaVencimiento?: string

  // Control de guardado temporal
  isDraft: boolean
}

export function CreateOTFormTabs({ formData, currentUser }: CreateOTFormTabsProps) {
  const [isPending, startTransition] = useTransition()
  const [success, setSuccess] = useState<string>("")
  const { error, handleError, clearError, renderError } = useErrorHandler()
  
  // Tab state
  const [activeTab, setActiveTab] = useState('datos')
  const [tabs, setTabs] = useState<Tab[]>([
    { id: 'datos', label: 'Datos de OT', isActive: true, disabled: false },
    { id: 'valorizacion', label: 'Valorización de OT', isActive: false, disabled: true },
    { id: 'avances', label: 'Registro de Avances', isActive: false, disabled: true },
    { id: 'repertorio', label: 'Repertorio', isActive: false, disabled: true },
    { id: 'uaf', label: 'UAF (ROE)', isActive: false, disabled: true },
    { id: 'rndpa', label: 'RNDPA', isActive: false, disabled: true },
    { id: 'historial', label: 'Historial', isActive: false, disabled: true }
  ])
  
  // Form state
  const [formState, setFormState] = useState<OTFormState>({
    tipoDocumento: '',
    fechaOT: new Date().toISOString().split('T')[0], // Siempre HOY
    materiaDetalle: '',
    comparecientes: [],
    estado: 'BORRADOR', // Inicia como borrador
    isDraft: true // Es un borrador hasta que se cree oficialmente
  })

  const handleTabClick = (tabId: string) => {
    const tab = tabs.find(t => t.id === tabId)
    if (!tab || tab.disabled) return

    // Special validations for certain tabs
    if (tabId === 'repertorio' && formState.tipoDocumento === 'documento_privado') {
      handleError('No disponible para Documento Privado', undefined, 'validation')
      return
    }

    if (tabId === 'repertorio' && formState.tipoDocumento === 'instruccion') {
      handleError('No disponible para Instrucción', undefined, 'validation')
      return
    }

    setActiveTab(tabId)
    setTabs(tabs.map(t => ({ ...t, isActive: t.id === tabId })))
    clearError()
  }

  const enableTabsAfterCreation = (otId: string, numeroOT: string) => {
    setFormState(prev => ({ ...prev, id: otId, numeroOT }))
    setTabs(tabs.map(t => ({ ...t, disabled: t.id === 'datos' ? false : false })))
  }

  const updateFormState = (updates: Partial<OTFormState>) => {
    console.log('Updating form state:', updates)
    setFormState(prev => {
      const newState = { ...prev, ...updates }
      console.log('New form state:', newState)
      return newState
    })
  }

  const handleCreateOT = async (otData: any) => {
    clearError()
    setSuccess("")

    const formData = new FormData()
    formData.append('tipoDocumento', otData.tipoDocumento)
    formData.append('materiaDetalle', otData.materiaDetalle)
    formData.append('fechaVencimiento', otData.fechaVencimiento || '')
    formData.append('observaciones', otData.observaciones || '')
    formData.append('gestora', otData.gestora || '')
    formData.append('numeroWorkflow', otData.numeroWorkflow || '')
    formData.append('comparecientes', JSON.stringify(otData.comparecientes))

    startTransition(async () => {
      const result = await createOT(formData)

      if (result.success) {
        setSuccess(`OT ${result.numeroOT} creada exitosamente`)
        // Actualizar el estado para mostrar que ya no es borrador
        updateFormState({
          id: result.otId,
          numeroOT: result.numeroOT,
          estado: 'CREADA',
          isDraft: false
        })
        enableTabsAfterCreation(result.otId, result.numeroOT)
      } else {
        if (result.error === 'Permisos insuficientes') {
          handleError(result.error, result.errorId, 'permission')
        } else {
          handleError(result.error || "Error desconocido", result.errorId)
        }
        // NO limpiar el formulario en caso de error
        // Los datos se mantienen para que el usuario pueda corregir
      }
    })
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'datos':
        return (
          <DatosOTTab
            formData={formData}
            formState={formState}
            updateFormState={updateFormState}
            onCreateOT={handleCreateOT}
            isPending={isPending}
            isEditing={!!formState.id}
          />
        )
      case 'valorizacion':
        return (
          <ValorizacionTab
            otId={formState.id}
            numeroOT={formState.numeroOT}
            tipoDocumento={formState.tipoDocumento}
          />
        )
      case 'avances':
        return (
          <AvancesTab
            otId={formState.id}
            numeroOT={formState.numeroOT}
            tipoDocumento={formState.tipoDocumento}
          />
        )
      case 'repertorio':
        return (
          <RepertorioTab
            otId={formState.id}
            numeroOT={formState.numeroOT}
            tipoDocumento={formState.tipoDocumento}
            formState={formState}
            updateFormState={updateFormState}
          />
        )
      case 'uaf':
        return (
          <UAFTab
            otId={formState.id}
            numeroOT={formState.numeroOT}
            formState={formState}
            updateFormState={updateFormState}
          />
        )
      case 'rndpa':
        return (
          <RNDPATab
            otId={formState.id}
            numeroOT={formState.numeroOT}
            comparecientes={formState.comparecientes}
          />
        )
      case 'historial':
        return (
          <HistorialTab
            otId={formState.id}
            numeroOT={formState.numeroOT}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="w-full">
      {/* Success Message */}
      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {renderError("mb-6")}

      {/* OT Header Info */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-blue-900">
              {formState.numeroOT ? `OT: ${formState.numeroOT}` : 'Nueva Orden de Trabajo'}
            </h3>
            <p className="text-sm text-blue-700">
              {formState.tipoDocumento ?
                formData.documentTypes.find(t => t.value === formState.tipoDocumento)?.label :
                'Tipo de documento no seleccionado'
              } - {formState.estado}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-blue-700">
              Fecha: {new Date(formState.fechaOT).toLocaleDateString()}
            </p>
            <span className={`px-2 py-1 rounded text-xs ${
              formState.isDraft ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
            }`}>
              {formState.isDraft ? 'Borrador' : 'Creada'}
            </span>
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              disabled={tab.disabled}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                tab.isActive
                  ? 'border-blue-500 text-blue-600'
                  : tab.disabled
                  ? 'border-transparent text-gray-400 cursor-not-allowed'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.disabled && (
                <span className="ml-1 text-xs">(🔒)</span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-96">
        {renderTabContent()}
      </div>
    </div>
  )
}
