// Domain - User - Entities
import { BaseEntity } from '../../shared/entities/base-entity'
import { UserId } from '../../shared/value-objects/id'
import { Email } from '../../shared/value-objects/email'

export enum UserRole {
  ADMIN = 'ADMIN',
  NOTARIO = 'NOTARIO',
  USUARIO = 'USUARIO',
  CONSULTA = 'CONSULTA'
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED'
}

export interface UserPermission {
  module: string
  actions: string[]
}

export class User extends BaseEntity<UserId> {
  private username: string
  private email: Email
  private passwordHash: string
  private nombreSocial: string
  private role: UserRole
  private status: UserStatus
  private permissions: UserPermission[]
  private lastLoginAt?: Date

  constructor(
    id: UserId,
    username: string,
    email: Email,
    passwordHash: string,
    nombreSocial: string,
    role: UserRole = UserRole.USUARIO,
    status: UserStatus = UserStatus.ACTIVE,
    permissions: UserPermission[] = [],
    createdAt?: Date,
    updatedAt?: Date,
    lastLoginAt?: Date
  ) {
    super(id, createdAt, updatedAt)
    this.username = username
    this.email = email
    this.passwordHash = passwordHash
    this.nombreSocial = nombreSocial
    this.role = role
    this.status = status
    this.permissions = permissions
    this.lastLoginAt = lastLoginAt
  }

  // Getters
  public getUsername(): string {
    return this.username
  }

  public getEmail(): Email {
    return this.email
  }

  public getPasswordHash(): string {
    return this.passwordHash
  }

  public getNombreSocial(): string {
    return this.nombreSocial
  }

  public getRole(): UserRole {
    return this.role
  }

  public getStatus(): UserStatus {
    return this.status
  }

  public getPermissions(): UserPermission[] {
    return this.permissions
  }

  public getLastLoginAt(): Date | undefined {
    return this.lastLoginAt
  }

  // Business methods
  public isActive(): boolean {
    return this.status === UserStatus.ACTIVE
  }

  public isAdmin(): boolean {
    return this.role === UserRole.ADMIN
  }

  public hasPermission(module: string, action: string): boolean {
    if (this.isAdmin()) return true
    
    const modulePermission = this.permissions.find(p => p.module === module)
    return modulePermission?.actions.includes(action) || false
  }

  public updateLastLogin(): void {
    this.lastLoginAt = new Date()
    this.updateTimestamp()
  }

  public changePassword(newPasswordHash: string): void {
    this.passwordHash = newPasswordHash
    this.updateTimestamp()
  }

  public updateProfile(nombreSocial: string, email: Email): void {
    this.nombreSocial = nombreSocial
    this.email = email
    this.updateTimestamp()
  }

  public activate(): void {
    this.status = UserStatus.ACTIVE
    this.updateTimestamp()
  }

  public deactivate(): void {
    this.status = UserStatus.INACTIVE
    this.updateTimestamp()
  }

  public suspend(): void {
    this.status = UserStatus.SUSPENDED
    this.updateTimestamp()
  }

  // Factory method
  static create(
    username: string,
    email: string,
    passwordHash: string,
    nombreSocial: string,
    role: UserRole = UserRole.USUARIO
  ): User {
    return new User(
      UserId.generate(),
      username,
      new Email(email),
      passwordHash,
      nombreSocial,
      role
    )
  }
}
