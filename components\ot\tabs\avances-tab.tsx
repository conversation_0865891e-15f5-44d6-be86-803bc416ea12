"use client"

import { useState } from "react"
import { WorkflowVisual } from "../workflow-visual"

interface AvancesTabProps {
  otId?: string
  numeroOT?: string
  tipoDocumento?: string
  estadoActual?: string
  isEnabled: boolean
}

interface Avance {
  id: string
  workflow: string
  evento: string
  nota: string
  observacion?: string
  alerta: boolean
  fecha: string
  usuario: string
}

export function AvancesTab({ otId, numeroOT, tipoDocumento, estadoActual, isEnabled }: AvancesTabProps) {
  const [avances, setAvances] = useState<Avance[]>([])
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState({
    workflow: '',
    evento: '',
    nota: '',
    observacion: '',
    alerta: false
  })

  // Mock workflows based on document type
  const getWorkflows = () => {
    switch (tipoDocumento) {
      case 'escritura_publica':
        return [
          { value: 'INGRESO', label: 'Ingreso' },
          { value: 'REVISION', label: 'Revisión' },
          { value: 'PREPARACION', label: 'Preparación' },
          { value: 'FIRMA', label: 'Firma' },
          { value: 'REPERTORIO', label: 'Repertorio' },
          { value: 'ENTREGA', label: 'Entrega' }
        ]
      case 'documento_privado':
        return [
          { value: 'INGRESO', label: 'Ingreso' },
          { value: 'REVISION', label: 'Revisión' },
          { value: 'PREPARACION', label: 'Preparación' },
          { value: 'FIRMA', label: 'Firma' },
          { value: 'ENTREGA', label: 'Entrega' }
        ]
      default:
        return [
          { value: 'INGRESO', label: 'Ingreso' },
          { value: 'PROCESAMIENTO', label: 'Procesamiento' },
          { value: 'ENTREGA', label: 'Entrega' }
        ]
    }
  }

  const getEventos = () => {
    return [
      { value: 'INICIO', label: 'Inicio' },
      { value: 'EN_PROCESO', label: 'En Proceso' },
      { value: 'COMPLETADO', label: 'Completado' },
      { value: 'PENDIENTE', label: 'Pendiente' },
      { value: 'OBSERVACION', label: 'Observación' }
    ]
  }

  const getNotas = () => {
    return [
      { value: 'DOCUMENTOS_RECIBIDOS', label: 'Documentos recibidos' },
      { value: 'REVISION_COMPLETADA', label: 'Revisión completada' },
      { value: 'PREPARACION_INICIADA', label: 'Preparación iniciada' },
      { value: 'FIRMA_PROGRAMADA', label: 'Firma programada' },
      { value: 'DOCUMENTO_FIRMADO', label: 'Documento firmado' },
      { value: 'ENTREGA_REALIZADA', label: 'Entrega realizada' }
    ]
  }

  if (!otId) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">📋</div>
        <p className="text-gray-600">Debe crear la OT primero para registrar avances</p>
      </div>
    )
  }

  const handleAddAvance = () => {
    const newAvance: Avance = {
      id: Date.now().toString(),
      workflow: formData.workflow,
      evento: formData.evento,
      nota: formData.nota,
      observacion: formData.observacion,
      alerta: formData.alerta,
      fecha: new Date().toISOString(),
      usuario: 'Usuario Actual' // TODO: Get from session
    }

    setAvances([newAvance, ...avances])
    setFormData({
      workflow: '',
      evento: '',
      nota: '',
      observacion: '',
      alerta: false
    })
    setShowForm(false)
  }

  const removeAvance = (id: string) => {
    setAvances(avances.filter(a => a.id !== id))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-purple-600 text-white p-4 rounded-t-lg">
        <h3 className="text-lg font-medium">
          Registro de Avances - OT: {numeroOT}
        </h3>
        <p className="text-sm opacity-90">
          Seguimiento del workflow de {tipoDocumento?.replace('_', ' ') || 'documento'}
        </p>
      </div>

      {/* Workflow Visual */}
      <WorkflowVisual
        tipoDocumento={tipoDocumento || ''}
        estadoActual={estadoActual || 'BORRADOR'}
        className="mx-4"
      />

      <div className="bg-purple-50 p-6 rounded-b-lg border border-purple-200">
        {/* Add Button */}
        {!showForm && (
          <div className="mb-6">
            <button
              onClick={() => setShowForm(true)}
              className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              ➕ Registrar Avance
            </button>
          </div>
        )}

        {/* Add Form */}
        {showForm && (
          <div className="bg-white p-6 rounded-lg border border-gray-200 mb-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Nuevo Avance</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Workflow *
                </label>
                <select
                  value={formData.workflow}
                  onChange={(e) => setFormData({ ...formData, workflow: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                >
                  <option value="">Seleccionar workflow...</option>
                  {getWorkflows().map((wf) => (
                    <option key={wf.value} value={wf.value}>
                      {wf.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Evento *
                </label>
                <select
                  value={formData.evento}
                  onChange={(e) => setFormData({ ...formData, evento: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                >
                  <option value="">Seleccionar evento...</option>
                  {getEventos().map((ev) => (
                    <option key={ev.value} value={ev.value}>
                      {ev.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nota *
                </label>
                <select
                  value={formData.nota}
                  onChange={(e) => setFormData({ ...formData, nota: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                >
                  <option value="">Seleccionar nota...</option>
                  {getNotas().map((nota) => (
                    <option key={nota.value} value={nota.value}>
                      {nota.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Observación
                </label>
                <textarea
                  value={formData.observacion}
                  onChange={(e) => setFormData({ ...formData, observacion: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  rows={3}
                  placeholder="Observaciones adicionales..."
                />
              </div>

              <div className="md:col-span-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.alerta}
                    onChange={(e) => setFormData({ ...formData, alerta: e.target.checked })}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium text-gray-700">
                    Marcar como alerta
                  </span>
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setShowForm(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={handleAddAvance}
                disabled={!formData.workflow || !formData.evento || !formData.nota}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Registrar
              </button>
            </div>
          </div>
        )}

        {/* Avances Timeline */}
        {avances.length > 0 ? (
          <div className="space-y-4">
            {avances.map((avance, index) => (
              <div key={avance.id} className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        avance.alerta ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                      }`}>
                        {avance.workflow}
                      </span>
                      <span className="text-sm text-gray-500">→</span>
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                        {avance.evento}
                      </span>
                      {avance.alerta && (
                        <span className="text-red-500 text-sm">⚠️ Alerta</span>
                      )}
                    </div>
                    
                    <h4 className="font-medium text-gray-900 mb-1">
                      {getNotas().find(n => n.value === avance.nota)?.label || avance.nota}
                    </h4>
                    
                    {avance.observacion && (
                      <p className="text-sm text-gray-600 mb-2">
                        {avance.observacion}
                      </p>
                    )}
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>👤 {avance.usuario}</span>
                      <span>📅 {new Date(avance.fecha).toLocaleString()}</span>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => removeAvance(avance.id)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Eliminar
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">📋</div>
            <p>No hay avances registrados</p>
            <p className="text-sm">Registre el primer avance para comenzar el seguimiento</p>
          </div>
        )}
      </div>
    </div>
  )
}
