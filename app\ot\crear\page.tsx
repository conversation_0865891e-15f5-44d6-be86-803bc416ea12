// Server Side OT Creation Page - NextJS 15
import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { UnifiedLayout } from "@/components/layout/unified-layout"
import { CreateOTForm } from "@/components/ot/create-ot-form"
import { getOTFormData } from "@/src/presentation/actions/ot-actions"
import { PermissionService } from "@/src/domain/shared/services/permission-service"

export default async function CrearOTPage() {
  // Server-side authentication check
  const session = await getServerSession(authOptions)
  
  if (!session) {
    redirect("/auth/login")
  }

  // Check permissions using the new service
  if (!PermissionService.hasPermission(session.user.role, 'ot', 'create')) {
    redirect("/dashboard?error=insufficient_permissions")
  }

  // Get form data
  const formDataResult = await getOTFormData()
  
  if (!formDataResult.success) {
    redirect("/ot?error=form_data_error")
  }

  return (
    <UnifiedLayout>
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Nueva Orden de Trabajo
                </h1>
                <p className="text-gray-600 mt-2">
                  Complete la información para crear una nueva OT
                </p>
              </div>
              <div className="flex space-x-3">
                <a
                  href="/ot"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <span className="mr-2">←</span>
                  Volver
                </a>
              </div>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center">
              <div className="flex items-center text-sm text-blue-600">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full text-white font-medium">
                  1
                </div>
                <span className="ml-2 font-medium">Datos Generales</span>
              </div>
              <div className="flex-1 mx-4 h-px bg-gray-200"></div>
              <div className="flex items-center text-sm text-gray-400">
                <div className="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full text-gray-600 font-medium">
                  2
                </div>
                <span className="ml-2">Comparecientes</span>
              </div>
              <div className="flex-1 mx-4 h-px bg-gray-200"></div>
              <div className="flex items-center text-sm text-gray-400">
                <div className="flex items-center justify-center w-8 h-8 bg-gray-200 rounded-full text-gray-600 font-medium">
                  3
                </div>
                <span className="ml-2">Revisión</span>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white shadow-sm rounded-lg border border-gray-200">
            <CreateOTForm 
              formData={formDataResult.data}
              currentUser={session.user}
            />
          </div>

          {/* Help Section */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-blue-900 mb-4">
              💡 Consejos para crear una OT
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
              <div>
                <h4 className="font-medium mb-2">Materia</h4>
                <ul className="space-y-1">
                  <li>• Debe ser descriptiva y específica</li>
                  <li>• Mínimo 10 caracteres</li>
                  <li>• Incluir detalles relevantes</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Comparecientes</h4>
                <ul className="space-y-1">
                  <li>• RUT válido para chilenos</li>
                  <li>• Apellido materno obligatorio para chilenos</li>
                  <li>• Seleccionar calidad correcta</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Prioridad</h4>
                <ul className="space-y-1">
                  <li>• <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">Urgente</span> - Mismo día</li>
                  <li>• <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs">Alta</span> - 1-2 días</li>
                  <li>• <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">Media</span> - 3-5 días</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Seguridad</h4>
                <ul className="space-y-1">
                  <li>• Datos encriptados en tránsito</li>
                  <li>• Validación automática de RUT</li>
                  <li>• Auditoría completa de cambios</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  )
}
