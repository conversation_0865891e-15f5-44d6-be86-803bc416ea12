"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">S+</span>
                </div>
                <span className="text-2xl font-bold text-gray-900">
                  SIGN<span className="text-blue-500">+</span>
                </span>
              </div>
              <span className="ml-4 text-gray-600">Sistema de Gestión Notarial</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">
                Bienvenido, {session.user.nombreSocial || session.user.username}
              </span>
              <button
                onClick={() => signOut({ callbackUrl: "/auth/login" })}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Cerrar Sesión
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Panel de Inicio
            </h1>
            
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Información de Usuario
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Usuario</p>
                  <p className="text-lg text-gray-900">{session.user.username}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Nombre Social</p>
                  <p className="text-lg text-gray-900">{session.user.nombreSocial || "No definido"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Email</p>
                  <p className="text-lg text-gray-900">{session.user.email || "No definido"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Permisos</p>
                  <p className="text-lg text-gray-900">{session.user.permissions?.length || 0} permisos asignados</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Módulos del Sistema
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <h3 className="font-medium text-gray-900">Gestión de Documentos</h3>
                  <p className="text-sm text-gray-600 mt-1">Documentos privados, escrituras públicas</p>
                </div>
                <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <h3 className="font-medium text-gray-900">Órdenes de Trabajo</h3>
                  <p className="text-sm text-gray-600 mt-1">Creación y seguimiento de OT</p>
                </div>
                <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <h3 className="font-medium text-gray-900">Gestión de Usuarios</h3>
                  <p className="text-sm text-gray-600 mt-1">Administración de usuarios y permisos</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
