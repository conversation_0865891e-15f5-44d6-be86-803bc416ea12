<?php 
ini_set("memory_limit","2024M");
require_once ("calls/lib.php");
require_once ("calls/lib_firma_new.php");
require_once ("calls/lib_toc.php");
require_once ("calls/lib_notarial.php");
require_once ("calls/lib_cod_zzz.php");
//include("calls/soap/nusoap.php");
$aca = '';

	echo"<html>
	<head>";
	echo"<meta http-equiv='content-type' content='text/html; charset=ISO-8859-1'>";
	$ddtt = new datos;
	echo'
	<title>Sign - Central de Poderes</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link href="cons_estilos_new.css" rel="stylesheet" type="text/css">
	<link href="css/estilo.css" rel="stylesheet" type="text/css" />
	<title>Fojas Subida de Documentos</title>';

	$sig=$_REQUEST['sig'];

	if($sig=="text")
		{
			
		$conect= new ingreso;
		$link=$conect->Conectarse();				
		$r_in = "CREATE TABLE IF NOT EXISTS `certificados_firmas_pedazos_msg` (
		  `texto` text NOT NULL) ENGINE=MyISAM DEFAULT CHARSET=latin1;";
		$fin=mysqli_query($link, $r_in);	

		
		echo"  <SCRIPT LANGUAGE=";
			printf ("%c",34);
			echo"JavaScript";
			printf ("%c",34);
			echo" > function redireccionar1() {
			setTimeout(";
			printf ("%c",34);
			echo"	location.href='subir_fojas.php?sig=text'";
			printf ("%c",34);
			echo", 0);
			}
			</SCRIPT>
			</HEAD>";
		echo"<script language=\"javascript\" type=\"text/javascript\" >";
		echo"window.setInterval(\"redireccionar1() \",3000); ";
		echo"</script>	";			

		$rr_meta="SELECT * FROM certificados_firmas_pedazos_msg ";
		$fin_meta=mysqli_query($link, $rr_meta);
		$row_meta = mysqli_fetch_array($fin_meta);			
		echo "<br>".$row_meta["texto"];

		}
	else
		{
		if($sig<>"yep")
			{
			
			echo '	<body id="fondo1" >';
			echo '<span class="header">RESUMEN DOCUMENTOS FIRMADOS';
			echo '
			<iframe src="subir_fojas_ini_frame.php"
			style="border: medium none; overflow: hidden; height: 300px; width: 750px;"
			frameborder="0" marginwidth="0" marginheight ="0" scrolling="yes">
			</iframe>';

			echo '
			<iframe src="subir_fojas.php?sig=yep"
			style="border: medium none; overflow: hidden; height: 100px; width: 500px;""
			frameborder="0" marginwidth="0" marginheight ="0" scrolling="no">
			</iframe>
		
			<iframe src="subir_fojas.php?sig=text"
			style="border: medium none; overflow: hidden; height: 100px; width: 500px;""
			frameborder="0" marginwidth="0" marginheight ="0" scrolling="no">
			</iframe><div>		
		
			<div>';
			}
		else
			{
			$source=getcwd();
			$source.= "/Digi/firma/";
			$dv=subir_auto($source);
			echo"$dv";		
			}
		}
	


?> 

