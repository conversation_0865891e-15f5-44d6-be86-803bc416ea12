"use client"

import { useState } from "react"

interface ValorizacionTabProps {
  otId?: string
  numeroOT?: string
  tipoDocumento: string
}

interface Valorizacion {
  id: string
  tipo: 'derechos' | 'impuestos' | 'diligencias'
  detalle: string
  monto: number
  baseImpuesto?: number
  fechaVencimiento?: string
}

export function ValorizacionTab({ otId, numeroOT, tipoDocumento }: ValorizacionTabProps) {
  const [valorizaciones, setValorizaciones] = useState<Valorizacion[]>([])
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState({
    tipo: 'derechos' as const,
    detalle: '',
    monto: 0,
    baseImpuesto: 0,
    fechaVencimiento: ''
  })

  if (!otId) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">📊</div>
        <p className="text-gray-600">Debe crear la OT primero para acceder a la valorización</p>
      </div>
    )
  }

  const handleAddValorizacion = () => {
    const newValorizacion: Valorizacion = {
      id: Date.now().toString(),
      tipo: formData.tipo,
      detalle: formData.detalle,
      monto: formData.monto,
      baseImpuesto: formData.baseImpuesto || undefined,
      fechaVencimiento: formData.fechaVencimiento || undefined
    }

    setValorizaciones([...valorizaciones, newValorizacion])
    setFormData({
      tipo: 'derechos',
      detalle: '',
      monto: 0,
      baseImpuesto: 0,
      fechaVencimiento: ''
    })
    setShowForm(false)
  }

  const removeValorizacion = (id: string) => {
    setValorizaciones(valorizaciones.filter(v => v.id !== id))
  }

  const getTotalByType = (tipo: string) => {
    return valorizaciones
      .filter(v => v.tipo === tipo)
      .reduce((sum, v) => sum + v.monto, 0)
  }

  const getTotal = () => {
    return valorizaciones.reduce((sum, v) => sum + v.monto, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-green-600 text-white p-4 rounded-t-lg">
        <h3 className="text-lg font-medium">
          Valorización de OT: {numeroOT}
        </h3>
        <p className="text-sm opacity-90">
          Gestión de derechos, impuestos y diligencias
        </p>
      </div>

      <div className="bg-green-50 p-6 rounded-b-lg border border-green-200">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="text-sm font-medium text-gray-600">Derechos</h4>
            <p className="text-2xl font-bold text-blue-600">
              ${getTotalByType('derechos').toLocaleString()}
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="text-sm font-medium text-gray-600">Impuestos</h4>
            <p className="text-2xl font-bold text-red-600">
              ${getTotalByType('impuestos').toLocaleString()}
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="text-sm font-medium text-gray-600">Diligencias</h4>
            <p className="text-2xl font-bold text-yellow-600">
              ${getTotalByType('diligencias').toLocaleString()}
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="text-sm font-medium text-gray-600">Total</h4>
            <p className="text-2xl font-bold text-green-600">
              ${getTotal().toLocaleString()}
            </p>
          </div>
        </div>

        {/* Add Button */}
        {!showForm && (
          <div className="mb-6">
            <button
              onClick={() => setShowForm(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              ➕ Agregar Valorización
            </button>
          </div>
        )}

        {/* Add Form */}
        {showForm && (
          <div className="bg-white p-6 rounded-lg border border-gray-200 mb-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Nueva Valorización</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tipo *
                </label>
                <select
                  value={formData.tipo}
                  onChange={(e) => setFormData({ ...formData, tipo: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                >
                  <option value="derechos">Derechos</option>
                  <option value="impuestos">Impuestos</option>
                  <option value="diligencias">Diligencias</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Monto *
                </label>
                <input
                  type="number"
                  value={formData.monto}
                  onChange={(e) => setFormData({ ...formData, monto: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  min="0"
                  step="1"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Detalle *
                </label>
                <input
                  type="text"
                  value={formData.detalle}
                  onChange={(e) => setFormData({ ...formData, detalle: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Descripción del concepto"
                  required
                />
              </div>

              {formData.tipo === 'impuestos' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Base Impuesto
                    </label>
                    <input
                      type="number"
                      value={formData.baseImpuesto}
                      onChange={(e) => setFormData({ ...formData, baseImpuesto: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      min="0"
                      step="1"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fecha Vencimiento
                    </label>
                    <input
                      type="date"
                      value={formData.fechaVencimiento}
                      onChange={(e) => setFormData({ ...formData, fechaVencimiento: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setShowForm(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={handleAddValorizacion}
                disabled={!formData.detalle || formData.monto <= 0}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Agregar
              </button>
            </div>
          </div>
        )}

        {/* Valorizaciones List */}
        {valorizaciones.length > 0 ? (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Detalle
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Monto
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Base Impuesto
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Vencimiento
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {valorizaciones.map((val) => (
                  <tr key={val.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        val.tipo === 'derechos' ? 'bg-blue-100 text-blue-800' :
                        val.tipo === 'impuestos' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {val.tipo.charAt(0).toUpperCase() + val.tipo.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {val.detalle}
                    </td>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">
                      ${val.monto.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {val.baseImpuesto ? `$${val.baseImpuesto.toLocaleString()}` : '-'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {val.fechaVencimiento ? new Date(val.fechaVencimiento).toLocaleDateString() : '-'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <button
                        onClick={() => removeValorizacion(val.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Eliminar
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">💰</div>
            <p>No hay valorizaciones registradas</p>
          </div>
        )}
      </div>
    </div>
  )
}
