import { NextRequest, NextResponse } from 'next/server'
import { 
  generateSampleLicense,
  saveLocalLicense,
  encryptLicenseData 
} from '@/lib/license'

export async function POST(request: NextRequest) {
  try {
    const { clientName, clientEmail } = await request.json()
    
    const sampleLicense = generateSampleLicense(
      clientName || 'Notaría de Prueba',
      clientEmail || '<EMAIL>'
    )
    
    // Guardar la licencia localmente
    await saveLocalLicense(sampleLicense)
    
    // Devolver la licencia cifrada para mostrar al usuario
    const encryptedLicense = encryptLicenseData(sampleLicense)
    
    return NextResponse.json({
      success: true,
      license: sampleLicense,
      encryptedLicense,
      message: 'Licencia de prueba generada y guardada correctamente'
    })
  } catch (error) {
    console.error('Error generating sample license:', error)
    return NextResponse.json(
      { error: 'Error al generar la licencia de prueba' },
      { status: 500 }
    )
  }
}
