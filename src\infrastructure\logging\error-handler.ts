// Infrastructure - Logging - Error Handler
import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { errorLogger, ErrorSeverity, ErrorCategory, ErrorContext } from './error-logger'

export class ErrorHandler {
  public static async handleServerError(
    error: Error | string,
    request?: NextRequest,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.UNKNOWN
  ): Promise<string> {
    const context = await this.buildContext(request)
    return await errorLogger.logError(error, severity, category, context)
  }

  public static async handleAuthenticationError(
    error: Error | string,
    request?: NextRequest
  ): Promise<string> {
    return await this.handleServerError(
      error,
      request,
      ErrorSeverity.HIGH,
      ErrorCategory.AUTHENTICATION
    )
  }

  public static async handleAuthorizationError(
    error: Error | string,
    request?: NextRequest
  ): Promise<string> {
    return await this.handleServerError(
      error,
      request,
      ErrorSeverity.MEDIUM,
      ErrorCategory.AUTHORIZATION
    )
  }

  public static async handleValidationError(
    error: Error | string,
    request?: NextRequest
  ): Promise<string> {
    return await this.handleServerError(
      error,
      request,
      ErrorSeverity.LOW,
      ErrorCategory.VALIDATION
    )
  }

  public static async handleBusinessLogicError(
    error: Error | string,
    request?: NextRequest
  ): Promise<string> {
    return await this.handleServerError(
      error,
      request,
      ErrorSeverity.MEDIUM,
      ErrorCategory.BUSINESS_LOGIC
    )
  }

  public static async handleDatabaseError(
    error: Error | string,
    request?: NextRequest
  ): Promise<string> {
    return await this.handleServerError(
      error,
      request,
      ErrorSeverity.HIGH,
      ErrorCategory.DATABASE
    )
  }

  public static async handleCriticalError(
    error: Error | string,
    request?: NextRequest
  ): Promise<string> {
    return await this.handleServerError(
      error,
      request,
      ErrorSeverity.CRITICAL,
      ErrorCategory.SYSTEM
    )
  }

  private static async buildContext(request?: NextRequest): Promise<ErrorContext> {
    const context: ErrorContext = {}

    if (request) {
      context.url = request.url
      context.method = request.method
      context.userAgent = request.headers.get('user-agent') || undefined
      context.ip = this.getClientIP(request)
      context.requestId = request.headers.get('x-request-id') || undefined
    }

    // Try to get session information
    try {
      const session = await getServerSession(authOptions)
      if (session?.user) {
        context.userId = session.user.id
        context.sessionId = session.user.username // or actual session ID if available
      }
    } catch (sessionError) {
      // Don't log session errors to avoid infinite loops
    }

    return context
  }

  private static getClientIP(request: NextRequest): string | undefined {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    
    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    
    if (realIP) {
      return realIP
    }

    return request.ip || undefined
  }

  // Helper method for Server Actions
  public static createServerActionErrorHandler(actionName: string) {
    return async (error: Error | string, additionalContext?: Record<string, any>) => {
      const context: ErrorContext = {
        additionalData: {
          action: actionName,
          ...additionalContext
        }
      }

      // Try to get session for context
      try {
        const session = await getServerSession(authOptions)
        if (session?.user) {
          context.userId = session.user.id
          context.sessionId = session.user.username
        }
      } catch (sessionError) {
        // Ignore session errors
      }

      return await errorLogger.logError(
        error,
        ErrorSeverity.MEDIUM,
        ErrorCategory.BUSINESS_LOGIC,
        context
      )
    }
  }

  // Helper method for API routes
  public static createAPIErrorHandler(endpoint: string) {
    return async (error: Error | string, request?: NextRequest) => {
      const context = await this.buildContext(request)
      context.additionalData = {
        endpoint,
        ...context.additionalData
      }

      return await errorLogger.logError(
        error,
        ErrorSeverity.MEDIUM,
        ErrorCategory.SYSTEM,
        context
      )
    }
  }
}

// Utility function for easy error handling in Server Actions
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  actionName: string,
  additionalContext?: Record<string, any>
): Promise<{ success: true; data: T; errorId?: never } | { success: false; error: string; errorId: string; data?: never }> {
  try {
    const data = await operation()
    return { success: true, data }
  } catch (error) {
    const errorHandler = ErrorHandler.createServerActionErrorHandler(actionName)
    const errorId = await errorHandler(error, additionalContext)
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido',
      errorId
    }
  }
}

// Utility function for permission checks with error logging
export async function withPermissionCheck<T>(
  operation: () => Promise<T>,
  permissionCheck: () => boolean,
  actionName: string,
  requiredPermission: string
): Promise<{ success: true; data: T; errorId?: never } | { success: false; error: string; errorId: string; data?: never }> {
  try {
    if (!permissionCheck()) {
      const errorId = await ErrorHandler.handleAuthorizationError(
        `Insufficient permissions for ${actionName}. Required: ${requiredPermission}`
      )
      
      return {
        success: false,
        error: 'Permisos insuficientes',
        errorId
      }
    }

    const data = await operation()
    return { success: true, data }
  } catch (error) {
    const errorHandler = ErrorHandler.createServerActionErrorHandler(actionName)
    const errorId = await errorHandler(error, { requiredPermission })
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido',
      errorId
    }
  }
}
