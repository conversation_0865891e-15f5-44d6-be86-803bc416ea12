"use client"

interface UAFTabProps {
  otId?: string
  numeroOT?: string
  formState: any
  updateFormState: (updates: any) => void
}

export function UAFTab({ otId, numeroOT, formState, updateFormState }: UAFTabProps) {
  
  if (!otId) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">🏦</div>
        <p className="text-gray-600">Debe crear la OT primero para acceder a UAF</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="bg-orange-600 text-white p-4 rounded-t-lg">
        <h3 className="text-lg font-medium">
          UAF (ROE) - OT: {numeroOT}
        </h3>
        <p className="text-sm opacity-90">
          Unidad de Análisis Financiero - Reporte de Operaciones Efectivo
        </p>
      </div>

      <div className="bg-orange-50 p-6 rounded-b-lg border border-orange-200">
        <div className="space-y-6">
          {/* ROE Status */}
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Estado ROE</h4>
            
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="esRoe"
                  checked={!formState.esRoe}
                  onChange={() => updateFormState({ esRoe: false, montoUaf: null })}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">
                  No requiere ROE
                </span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="radio"
                  name="esRoe"
                  checked={formState.esRoe}
                  onChange={() => updateFormState({ esRoe: true })}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">
                  Requiere ROE
                </span>
              </label>
            </div>
          </div>

          {/* ROE Details */}
          {formState.esRoe && (
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <h4 className="text-lg font-medium text-gray-900 mb-4">Detalles ROE</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Monto en UAF *
                  </label>
                  <input
                    type="number"
                    value={formState.montoUaf || ''}
                    onChange={(e) => updateFormState({ montoUaf: parseFloat(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    min="0"
                    step="0.01"
                    placeholder="Ej: 10000.00"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Operaciones ≥ 10.000 UAF requieren reporte
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Equivalente en Pesos
                  </label>
                  <input
                    type="text"
                    value={formState.montoUaf ? `$${(formState.montoUaf * 37000).toLocaleString()}` : ''}
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                    placeholder="Cálculo automático"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Valor UAF aproximado: $37.000
                  </p>
                </div>
              </div>

              {/* ROE Status Indicator */}
              <div className="mt-4 p-3 rounded-md bg-yellow-50 border border-yellow-200">
                <div className="flex items-center">
                  <span className="text-yellow-600 mr-2">⚠️</span>
                  <div>
                    <p className="text-sm font-medium text-yellow-800">
                      {formState.montoUaf >= 10000 
                        ? 'Esta operación REQUIERE reporte ROE a la UAF'
                        : 'Esta operación NO requiere reporte ROE'
                      }
                    </p>
                    <p className="text-xs text-yellow-700">
                      {formState.montoUaf >= 10000 
                        ? 'Debe reportarse dentro de 3 días hábiles'
                        : 'Monto inferior al umbral de reporte'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Information Panel */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              ℹ️ Información sobre ROE
            </h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Las operaciones ≥ 10.000 UAF deben reportarse a la UAF</li>
              <li>• El reporte debe realizarse dentro de 3 días hábiles</li>
              <li>• Incluye operaciones en efectivo y equivalentes</li>
              <li>• El valor UAF se actualiza diariamente</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            {formState.esRoe && formState.montoUaf >= 10000 && (
              <button
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                📋 Generar Reporte ROE
              </button>
            )}
            
            <button
              className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700"
            >
              Guardar UAF
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
