// Presentation - Pages - Dashboard (Server Side)
import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { UnifiedLayout } from "@/components/layout/unified-layout"
import { DashboardStats } from "@/components/dashboard/dashboard-stats"
import { RecentActivity } from "@/components/dashboard/recent-activity"
import { SystemInfo } from "@/components/dashboard/system-info"

// Server Component - No "use client"
export default async function DashboardPage() {
  // Server-side authentication check
  const session = await getServerSession(authOptions)
  
  if (!session) {
    redirect("/auth/login")
  }

  // Server-side data fetching
  const dashboardData = await getDashboardData(session.user.id)

  return (
    <UnifiedLayout>
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Bienvenido, {session.user.nombreSocial || session.user.username}
            </h1>
            <p className="text-gray-600 mt-2">
              Panel de control - Sistema e-NOTARIA
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <DashboardStats data={dashboardData.stats} />
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recent Activity */}
            <div className="lg:col-span-2">
              <RecentActivity activities={dashboardData.recentActivities} />
            </div>

            {/* System Info */}
            <div>
              <SystemInfo 
                systemInfo={dashboardData.systemInfo}
                licenseInfo={dashboardData.licenseInfo}
              />
            </div>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  )
}

// Server-side data fetching function
async function getDashboardData(userId: string) {
  // This would use your use cases/repositories
  // For now, returning mock data
  return {
    stats: {
      totalOT: 156,
      pendingOT: 23,
      completedOT: 133,
      totalDocuments: 1247
    },
    recentActivities: [
      {
        id: '1',
        type: 'ot_created',
        description: 'Nueva OT creada: Escritura de Compraventa',
        timestamp: new Date(),
        user: 'María Silva'
      },
      {
        id: '2',
        type: 'document_signed',
        description: 'Documento firmado digitalmente',
        timestamp: new Date(Date.now() - 3600000),
        user: 'Carlos Rodríguez'
      }
    ],
    systemInfo: {
      version: '1.0.0',
      uptime: '15 días',
      lastBackup: new Date(Date.now() - 86400000)
    },
    licenseInfo: {
      type: 'STANDARD',
      expiresAt: new Date(Date.now() + 30 * 86400000),
      isActive: true
    }
  }
}
