"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, ReactNode } from "react"
import { usePermissions } from "@/hooks/use-permissions"

interface ProtectedRouteProps {
  children: ReactNode
  requiredPermissions?: string[]
  requiredRoles?: string[]
  requireAll?: boolean // Si true, requiere TODOS los permisos/roles. Si false, requiere AL MENOS UNO
  fallbackUrl?: string
  loadingComponent?: ReactNode
  unauthorizedComponent?: ReactNode
}

export function ProtectedRoute({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requireAll = false,
  fallbackUrl = "/auth/login",
  loadingComponent,
  unauthorizedComponent
}: ProtectedRouteProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const {
    hasPermission,
    hasRole,
    hasAllPermissions,
    hasAnyPermission,
    isLoading
  } = usePermissions()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push(fallbackUrl)
    }
  }, [status, router, fallbackUrl])

  // Mostrar loading mientras se carga la sesión
  if (isLoading || status === "loading") {
    return (
      loadingComponent || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      )
    )
  }

  // Redirigir si no está autenticado
  if (!session) {
    return null
  }

  // Verificar permisos
  let hasRequiredPermissions = true
  if (requiredPermissions.length > 0) {
    hasRequiredPermissions = requireAll
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions)
  }

  // Verificar roles
  let hasRequiredRoles = true
  if (requiredRoles.length > 0) {
    hasRequiredRoles = requireAll
      ? requiredRoles.every(role => hasRole(role))
      : requiredRoles.some(role => hasRole(role))
  }

  // Si no tiene los permisos o roles requeridos
  if (!hasRequiredPermissions || !hasRequiredRoles) {
    return (
      unauthorizedComponent || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Acceso No Autorizado
            </h2>
            <p className="text-gray-600 mb-6">
              No tienes los permisos necesarios para acceder a esta página.
            </p>
            <div className="space-y-2">
              {requiredPermissions.length > 0 && (
                <div className="text-sm text-gray-500">
                  <strong>Permisos requeridos:</strong> {requiredPermissions.join(", ")}
                </div>
              )}
              {requiredRoles.length > 0 && (
                <div className="text-sm text-gray-500">
                  <strong>Roles requeridos:</strong> {requiredRoles.join(", ")}
                </div>
              )}
            </div>
            <button
              onClick={() => router.back()}
              className="mt-6 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Volver
            </button>
          </div>
        </div>
      )
    )
  }

  return <>{children}</>
}

// Componente para proteger elementos específicos dentro de una página
interface ProtectedElementProps {
  children: ReactNode
  requiredPermissions?: string[]
  requiredRoles?: string[]
  requireAll?: boolean
  fallback?: ReactNode
  showFallback?: boolean
}

export function ProtectedElement({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requireAll = false,
  fallback = null,
  showFallback = false
}: ProtectedElementProps) {
  const {
    hasPermission,
    hasRole,
    hasAllPermissions,
    hasAnyPermission,
    isLoading
  } = usePermissions()

  if (isLoading) {
    return showFallback ? fallback : null
  }

  // Verificar permisos
  let hasRequiredPermissions = true
  if (requiredPermissions.length > 0) {
    hasRequiredPermissions = requireAll
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions)
  }

  // Verificar roles
  let hasRequiredRoles = true
  if (requiredRoles.length > 0) {
    hasRequiredRoles = requireAll
      ? requiredRoles.every(role => hasRole(role))
      : requiredRoles.some(role => hasRole(role))
  }

  if (!hasRequiredPermissions || !hasRequiredRoles) {
    return showFallback ? fallback : null
  }

  return <>{children}</>
}

// HOC para proteger páginas completas
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    )
  }
}
