"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { getAppName, getCompanyName } from "@/lib/config"
import { UserMenuInline } from "@/components/layout/user-menu"

export default function InlineStylesDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div style={{ 
        minHeight: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        backgroundColor: '#f9fafb'
      }}>
        <div style={{ 
          width: '48px', 
          height: '48px', 
          border: '2px solid #0ea5e9',
          borderTop: '2px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div style={{ height: '100vh', display: 'flex', backgroundColor: '#f3f4f6' }}>
      {/* Sidebar */}
      <div style={{ 
        width: '256px', 
        backgroundColor: 'white', 
        borderRight: '1px solid #d1d5db',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header del sidebar */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          padding: '16px', 
          borderBottom: '1px solid #d1d5db' 
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div style={{ 
              width: '32px', 
              height: '32px', 
              backgroundColor: '#0ea5e9', 
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <span style={{ color: 'white', fontWeight: 'bold', fontSize: '14px' }}>e</span>
            </div>
            <span style={{ fontSize: '20px', fontWeight: 'bold', color: '#374151' }}>
              {getAppName()}
            </span>
          </div>
        </div>

        {/* Navegación */}
        <nav style={{ flex: 1, padding: '16px 12px', display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <a
            href="/dashboard"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: '#dbeafe',
              color: '#1e40af',
              textDecoration: 'none'
            }}
          >
            <span style={{ marginRight: '12px' }}>🏠</span>
            <span>Inicio</span>
          </a>

          <a
            href="/ot"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              cursor: 'pointer',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <span style={{ marginRight: '12px' }}>📋</span>
            <span>Órdenes de Trabajo</span>
          </a>

          <a
            href="/documentos"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              cursor: 'pointer',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <span style={{ marginRight: '12px' }}>📄</span>
            <span>Documentos</span>
          </a>

          <a
            href="/usuarios"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              cursor: 'pointer',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <span style={{ marginRight: '12px' }}>👥</span>
            <span>Usuarios</span>
          </a>

          <a
            href="/reportes"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              cursor: 'pointer',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <span style={{ marginRight: '12px' }}>📊</span>
            <span>Reportes</span>
          </a>

          <a
            href="/caja"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              cursor: 'pointer',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <span style={{ marginRight: '12px' }}>💰</span>
            <span>Caja</span>
          </a>

          <a
            href="/licencias"
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#4b5563',
              cursor: 'pointer',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <span style={{ marginRight: '12px' }}>🔐</span>
            <span>Licencias</span>
          </a>
        </nav>

        {/* Footer del sidebar */}
        <div style={{ padding: '16px', borderTop: '1px solid #d1d5db' }}>
          <div style={{ fontSize: '12px', color: '#6b7280', textAlign: 'center' }}>
            {getAppName()} v1.0.0
          </div>
        </div>
      </div>

      {/* Área de contenido principal */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column', minWidth: 0 }}>
        {/* Navbar */}
        <header style={{ 
          backgroundColor: 'white', 
          borderBottom: '1px solid #d1d5db',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
        }}>
          <div style={{ padding: '0 16px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center', 
              height: '64px' 
            }}>
              {/* Left side */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ fontSize: '18px', fontWeight: '600', color: '#374151' }}>
                    {getAppName()}
                  </span>
                  <span style={{ fontSize: '14px', color: '#6b7280' }}>
                    - {getCompanyName()}
                  </span>
                </div>
              </div>

              {/* Right side */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <UserMenuInline />
              </div>
            </div>
          </div>
        </header>

        {/* Contenido principal */}
        <main style={{ flex: 1, overflow: 'auto' }}>
          <div style={{ padding: '24px' }}>
            <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
              {/* Header de página */}
              <div style={{ 
                backgroundColor: 'white', 
                borderBottom: '1px solid #d1d5db',
                padding: '24px',
                marginBottom: '24px',
                borderRadius: '8px',
                boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
              }}>
                <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827' }}>
                  Bienvenido, {session.user.nombreSocial || session.user.username}
                </h1>
                <p style={{ marginTop: '4px', fontSize: '14px', color: '#4b5563' }}>
                  Panel de control - {getCompanyName()}
                </p>
              </div>

              {/* Contenido */}
              <div style={{ 
                backgroundColor: 'white', 
                borderRadius: '8px',
                boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
                border: '1px solid #d1d5db',
                padding: '24px'
              }}>
                <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>
                  Dashboard e-NOTARIA
                </h2>
                <p style={{ color: '#4b5563', marginBottom: '16px' }}>
                  ✅ Layout funcionando correctamente con estilos inline. Si ves el sidebar a la izquierda y el navbar arriba, el layout está implementado.
                </p>
                
                <div style={{ 
                  display: 'grid', 
                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                  gap: '16px'
                }}>
                  <div style={{ 
                    backgroundColor: '#eff6ff', 
                    padding: '16px', 
                    borderRadius: '8px',
                    border: '1px solid #bfdbfe'
                  }}>
                    <h3 style={{ fontWeight: '500', color: '#1e3a8a', marginBottom: '8px' }}>
                      Información del Usuario
                    </h3>
                    <p style={{ fontSize: '14px', color: '#1e40af' }}>Usuario: {session.user.username}</p>
                    <p style={{ fontSize: '14px', color: '#1e40af' }}>Nombre: {session.user.nombreSocial || 'No definido'}</p>
                    <p style={{ fontSize: '14px', color: '#1e40af' }}>Email: {session.user.email || 'No definido'}</p>
                  </div>
                  
                  <div style={{ 
                    backgroundColor: '#f0fdf4', 
                    padding: '16px', 
                    borderRadius: '8px',
                    border: '1px solid #bbf7d0'
                  }}>
                    <h3 style={{ fontWeight: '500', color: '#14532d', marginBottom: '8px' }}>
                      Información del Sistema
                    </h3>
                    <p style={{ fontSize: '14px', color: '#15803d' }}>Sistema: {getAppName()}</p>
                    <p style={{ fontSize: '14px', color: '#15803d' }}>Empresa: {getCompanyName()}</p>
                    <p style={{ fontSize: '14px', color: '#15803d' }}>Versión: 1.0.0</p>
                  </div>
                </div>

                <div style={{ 
                  marginTop: '24px',
                  padding: '16px',
                  backgroundColor: '#fefce8',
                  border: '1px solid #fde047',
                  borderRadius: '8px'
                }}>
                  <h3 style={{ fontWeight: '500', color: '#a16207', marginBottom: '8px' }}>
                    Estado del Layout
                  </h3>
                  <p style={{ fontSize: '14px', color: '#a16207' }}>
                    ✅ Layout con sidebar y navbar implementado
                  </p>
                  <p style={{ fontSize: '14px', color: '#a16207', marginTop: '4px' }}>
                    ✅ Branding e-NOTARIA aplicado
                  </p>
                  <p style={{ fontSize: '14px', color: '#a16207', marginTop: '4px' }}>
                    ✅ Configuración centralizada de empresa
                  </p>
                  <p style={{ fontSize: '14px', color: '#a16207', marginTop: '4px' }}>
                    ✅ Estilos funcionando (usando inline styles como fallback)
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
