// Domain - User - Repository Interface
import { User } from '../entities/user'
import { UserId } from '../../shared/value-objects/id'
import { Email } from '../../shared/value-objects/email'

export interface UserRepository {
  // Queries
  findById(id: UserId): Promise<User | null>
  findByUsername(username: string): Promise<User | null>
  findByEmail(email: Email): Promise<User | null>
  findAll(): Promise<User[]>
  findActiveUsers(): Promise<User[]>

  // Commands
  save(user: User): Promise<void>
  update(user: User): Promise<void>
  delete(id: UserId): Promise<void>

  // Specific queries
  existsByUsername(username: string): Promise<boolean>
  existsByEmail(email: Email): Promise<boolean>
}
