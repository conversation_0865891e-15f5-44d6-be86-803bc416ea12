// Presentation - Server Actions - OT Actions
'use server'

import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { redirect } from "next/navigation"
import { revalidatePath } from "next/cache"
import { z } from "zod"
import { PermissionService } from "../../domain/shared/services/permission-service"
import { withErrorHandling, withPermissionCheck } from "../../infrastructure/logging/error-handler"

// Validation schemas
const ComparecienteSchema = z.object({
  rut: z.string().min(1, "RUT es requerido"),
  nombres: z.string().min(1, "Nombres son requeridos"),
  apellidoPaterno: z.string().min(1, "Apellido paterno es requerido"),
  apellidoMaterno: z.string().optional(),
  calidad: z.enum(['COMPRADOR', 'VENDEDOR', 'BANCO', 'MANDANTE', 'MANDATARIO', 'TESTIGO', 'NOTARIO', 'OTRO']),
  esChileno: z.boolean()
})

const CreateOTSchema = z.object({
  documentType: z.enum(['escritura_publica', 'documento_privado', 'repertorio_vehiculo', 'instruccion', 'certificado', 'letra']),
  materia: z.string().min(10, "Materia debe tener al menos 10 caracteres"),
  priority: z.enum(['BAJA', 'MEDIA', 'ALTA', 'URGENTE']),
  fechaVencimiento: z.string().optional(),
  observaciones: z.string().optional(),
  comparecientes: z.array(ComparecienteSchema).min(1, "Al menos un compareciente es requerido")
})

export async function createOT(formData: FormData) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return {
      success: false,
      error: 'No autenticado',
      errorId: 'AUTH_REQUIRED'
    }
  }

  return await withPermissionCheck(
    async () => {

      // Parse form data
      const rawData = {
        documentType: formData.get('documentType') as string,
        materia: formData.get('materia') as string,
        priority: formData.get('priority') as string,
        fechaVencimiento: formData.get('fechaVencimiento') as string,
        observaciones: formData.get('observaciones') as string,
        comparecientes: JSON.parse(formData.get('comparecientes') as string || '[]')
      }

      // Validate data
      const validatedData = CreateOTSchema.parse(rawData)

      // Additional validation for Chilean RUTs
      for (const comp of validatedData.comparecientes) {
        if (comp.esChileno) {
          if (!isValidChileanRUT(comp.rut)) {
            throw new Error(`RUT inválido para ${comp.nombres} ${comp.apellidoPaterno}`)
          }
          if (!comp.apellidoMaterno) {
            throw new Error(`Apellido materno es requerido para chilenos: ${comp.nombres} ${comp.apellidoPaterno}`)
          }
        }
      }

      // TODO: Use CreateOTUseCase here
      // const useCase = container.getCreateOTUseCase()
      // const result = await useCase.execute({
      //   ...validatedData,
      //   notarioAsignado: session.user.id,
      //   fechaVencimiento: validatedData.fechaVencimiento ? new Date(validatedData.fechaVencimiento) : undefined,
      //   metadata: {
      //     observaciones: validatedData.observaciones
      //   }
      // })

      // For now, simulate success
      const result = { success: true, ot: { numeroOT: 'OT-2024-' + Date.now() } }

      if (!result.success) {
        throw new Error('Failed to create OT')
      }

      revalidatePath('/ot')
      revalidatePath('/dashboard')

      return {
        message: `OT ${result.ot.numeroOT} creada exitosamente`,
        otId: result.ot.numeroOT
      }
    },
    () => PermissionService.hasPermission(session.user.role, 'ot', 'create'),
    'createOT',
    'ot:create'
  )
}

export async function getOTFormData() {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return {
      success: false,
      error: 'No autenticado',
      errorId: 'AUTH_REQUIRED'
    }
  }

  return await withPermissionCheck(
    async () => {

      // TODO: Get real data from repositories
      return {
        documentTypes: [
          { value: 'escritura_publica', label: 'Escritura Pública' },
          { value: 'documento_privado', label: 'Documento Privado' },
          { value: 'repertorio_vehiculo', label: 'Vehículo' },
          { value: 'instruccion', label: 'Instrucción' },
          { value: 'certificado', label: 'Certificado' },
          { value: 'letra', label: 'Letra' }
        ],
        priorities: [
          { value: 'BAJA', label: 'Baja', color: 'bg-green-100 text-green-800' },
          { value: 'MEDIA', label: 'Media', color: 'bg-yellow-100 text-yellow-800' },
          { value: 'ALTA', label: 'Alta', color: 'bg-orange-100 text-orange-800' },
          { value: 'URGENTE', label: 'Urgente', color: 'bg-red-100 text-red-800' }
        ],
        calidades: [
          { value: 'COMPRADOR', label: 'Comprador' },
          { value: 'VENDEDOR', label: 'Vendedor' },
          { value: 'BANCO', label: 'Banco' },
          { value: 'MANDANTE', label: 'Mandante' },
          { value: 'MANDATARIO', label: 'Mandatario' },
          { value: 'TESTIGO', label: 'Testigo' },
          { value: 'NOTARIO', label: 'Notario' },
          { value: 'OTRO', label: 'Otro' }
        ],
        currentUser: {
          id: session.user.id,
          name: session.user.nombreSocial || session.user.username
        }
      }
    },
    () => PermissionService.hasPermission(session.user.role, 'ot', 'view'),
    'getOTFormData',
    'ot:view'
  )
}

// Helper function to validate Chilean RUT
function isValidChileanRUT(rut: string): boolean {
  const cleanRut = rut.replace(/[^0-9kK]/g, '').toUpperCase()
  
  if (cleanRut.length < 8 || cleanRut.length > 9) {
    return false
  }

  const body = cleanRut.slice(0, -1)
  const dv = cleanRut.slice(-1)

  if (!/^\d+$/.test(body)) {
    return false
  }

  return calculateDV(body) === dv
}

function calculateDV(rut: string): string {
  let sum = 0
  let multiplier = 2

  for (let i = rut.length - 1; i >= 0; i--) {
    sum += parseInt(rut[i]) * multiplier
    multiplier = multiplier === 7 ? 2 : multiplier + 1
  }

  const remainder = sum % 11
  const dv = 11 - remainder

  if (dv === 11) return '0'
  if (dv === 10) return 'K'
  return dv.toString()
}
