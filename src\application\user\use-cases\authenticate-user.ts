// Application - User - Use Cases
import bcrypt from 'bcryptjs'
import { UserRepository } from '../../../domain/user/repositories/user-repository'
import { User } from '../../../domain/user/entities/user'

export interface AuthenticateUserRequest {
  username: string
  password: string
}

export interface AuthenticateUserResponse {
  success: boolean
  user?: User
  error?: string
}

export class AuthenticateUserUseCase {
  constructor(private userRepository: UserRepository) {}

  async execute(request: AuthenticateUserRequest): Promise<AuthenticateUserResponse> {
    try {
      const { username, password } = request

      // Find user by username
      const user = await this.userRepository.findByUsername(username)
      if (!user) {
        return {
          success: false,
          error: 'Invalid credentials'
        }
      }

      // Check if user is active
      if (!user.isActive()) {
        return {
          success: false,
          error: 'User account is not active'
        }
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.getPasswordHash())
      if (!isPasswordValid) {
        return {
          success: false,
          error: 'Invalid credentials'
        }
      }

      // Update last login
      user.updateLastLogin()
      await this.userRepository.update(user)

      return {
        success: true,
        user
      }
    } catch (error) {
      return {
        success: false,
        error: 'Authentication failed'
      }
    }
  }
}
