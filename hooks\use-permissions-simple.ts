"use client"

import { useSession } from "next-auth/react"
import { useMemo } from "react"

export function usePermissionsSimple() {
  const { data: session, status } = useSession()

  const permissions = useMemo(() => {
    if (!session?.user?.permissions) return []
    return session.user.permissions
  }, [session?.user?.permissions])

  const roles = useMemo(() => {
    if (!session?.user?.roles) return []
    return session.user.roles
  }, [session?.user?.roles])

  // Verificar si el usuario tiene un permiso específico
  const hasPermission = (permissionName: string): boolean => {
    return permissions.some((p: any) => p.name === permissionName)
  }

  // Verificar si el usuario tiene un rol específico
  const hasRole = (roleName: string): boolean => {
    return roles.some((r: any) => r.name === roleName)
  }

  // Verificar si es administrador
  const isAdmin = (): boolean => {
    return hasRole('admin') || hasPermission('admin_access')
  }

  return {
    // Estado
    isLoading: status === "loading",
    isAuthenticated: !!session,
    user: session?.user,
    permissions,
    roles,

    // Métodos de verificación
    hasPermission,
    hasRole,
    isAdmin,

    // Métodos de conveniencia para módulos específicos
    canAccessOT: () => hasPermission('ot_access') || isAdmin(),
    canAccessDocuments: () => hasPermission('documento_privado_access') || isAdmin(),
    canAccessUsers: () => hasPermission('user_management') || isAdmin(),
    canAccessReports: () => hasPermission('reportes_access') || isAdmin(),
    canAccessCaja: () => hasPermission('caja_access') || isAdmin(),
    canAccessRepertorio: () => hasPermission('repertorio_access') || isAdmin(),
  }
}
